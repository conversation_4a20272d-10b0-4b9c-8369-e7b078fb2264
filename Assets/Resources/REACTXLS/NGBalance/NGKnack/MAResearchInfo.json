[{"id": "66d1ba9201512b02d9a5e135", "m_key": "Commoners::A<PERSON><PERSON>", "m_name": "AHole", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON>|Commoners::G<PERSON><PERSON>_<PERSON>ker", "m_unlock": "m_handGestureAHole=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Gesture: A-Hole", "m_description": "My mother always said, if you can't say anything nice, use an offensive gesture.", "m_position": "(-201.00, 76.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:02", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Gesture: A-Hole", "m_newDescription": "My mother always said, if you can't say anything nice, use an offensive gesture.", "m_newDetails": "Gives the power to signal your opinion of whosoever you consider to be of the rectal persuasion.", "m_newDetailsLine": "", "m_newUsage": "Press a button", "m_newHint": "Consider the tactical uses…", "m_newWarning": "Abusing your workforce may lead to a loss of cohesion among the grunts.", "m_newLore": ""}, {"id": "685a97c507ee6f1a9a23e68d", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "ArmourerPack", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "MA_RL_Armourer_Parts1", "m_dollarCost": 750, "m_factionCost": 1, "m_icon": "ResearchPickup", "m_title": "Armoury Block Pack ", "m_description": "New blocks for the Armoury. Jazz up those javelins.", "m_position": "(797.00, -290.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:03", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Armoury Block Pack", "m_newDescription": "New blocks for the Armoury. ", "m_newDetails": "Jazz up those javelins.", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2d6a278802e3610bf6", "m_key": "Lords::ArmourSalesPricePlus1", "m_name": "ArmourSalesPricePlus1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ArmourSalesPricePlus2", "m_unlock": "m_armourSalesPriceMarkup= 1.05", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 2, "m_icon": "ResearchFactoryLevel", "m_title": "Armour Sales Price Plus", "m_description": "increase by 5%", "m_position": "(-366.77, 166.99, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:19", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Armour Sales Price Plus", "m_newDescription": "increase by 5%", "m_newDetails": "Boosts the value of all armour sales by 5%", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Increase profit without additional resource costs.", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2d6a278802e3610bf9", "m_key": "Lords::ArmourSalesPricePlus2", "m_name": "ArmourSalesPricePlus2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ArmourSalesPricePlus3", "m_unlock": "m_armourSalesPriceMarkup= 1.1", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 4, "m_icon": "ResearchFactoryLevel", "m_title": "Armour Sales Price Plus", "m_description": "increase by 10%", "m_position": "(-598.29, 210.79, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:20", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Armour Sales Price Plus", "m_newDescription": "increase by 10%", "m_newDetails": "Boosts the value of all armour sales by 10%", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Increase profit without additional resource costs.", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2d6a278802e3610bfc", "m_key": "Lords::ArmourSalesPricePlus3", "m_name": "ArmourSalesPricePlus3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ArmourSalesPricePlus4", "m_unlock": "m_armourSalesPriceMarkup= 1.15", "m_giftCard": "", "m_dollarCost": 750, "m_factionCost": 6, "m_icon": "ResearchFactoryLevel", "m_title": "Armour Sales Price Plus", "m_description": "increase by 15%", "m_position": "(-746.5, 137.35, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:20", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Armour Sales Price Plus", "m_newDescription": "increase by 15%", "m_newDetails": "Boosts the value of all armour sales by 15%", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Increase profit without additional resource costs.", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2d6a278802e3610bff", "m_key": "Lords::ArmourSalesPricePlus4", "m_name": "ArmourSalesPricePlus4", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ArmourSalesPricePlus5", "m_unlock": "m_armourSalesPriceMarkup= 1.25", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 8, "m_icon": "ResearchFactoryLevel", "m_title": "Armour Sales Price Plus", "m_description": "increase by 25%", "m_position": "(-985.24, 187.39, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:21", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Armour Sales Price Plus", "m_newDescription": "increase by 25%", "m_newDetails": "Boosts the value of all armour sales by 25%", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Increase profit without additional resource costs.", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2d6a278802e3610c02", "m_key": "Lords::ArmourSalesPricePlus5", "m_name": "ArmourSalesPricePlus5", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_armourSalesPriceMarkup= 1.5", "m_giftCard": "", "m_dollarCost": 1500, "m_factionCost": 10, "m_icon": "ResearchFactoryLevel", "m_title": "Armour Sales Price Plus", "m_description": "increase by 50%", "m_position": "(-1126.14, 112.5, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:21", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Armour Sales Price Plus", "m_newDescription": "increase by 50%", "m_newDetails": "Boosts the value of all armour sales by 50%", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Increase profit without additional resource costs.", "m_newWarning": "", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b042a4", "m_key": "Lords::BedroomL1", "m_name": "BedroomL1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::BedroomL2", "m_unlock": "m_blockBedroomLevel =1", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 1, "m_icon": "ResearchBedroomCapacity", "m_title": "Additional Bed", "m_description": "Efficient living is efficient working. Add another bed to a worker bedroom and increase the building's residential capacity by +1", "m_position": "(437.5562, 314.1185, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:21", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Additional Bed", "m_newDescription": "<b>Available during Early Access updates</b>\nEfficient living is efficient working. Add another bed to a worker bedroom and increase the building's residential capacity by +1", "m_newDetails": "Increases the capacity of your worker's bedrooms by +1", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Do you need every bedroom you own anymore?", "m_newWarning": "Overcrowding comes with its own problems.", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b042a9", "m_key": "Lords::BedroomL2", "m_name": "BedroomL2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::BedroomL3", "m_unlock": "m_blockBedroomLevel =2", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 2, "m_icon": "ResearchBedroomCapacity", "m_title": "Bunk Bed", "m_description": "Floorspace is at a premium in these small bedrooms. Add this bunk bed and Increase the capacity of all bedrooms by 2!", "m_position": "(643.82, 388.3439, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:22", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Bunk Bed", "m_newDescription": "<b>Available during Early Access updates</b>\nFloorspace is at a premium in these small bedrooms. Add this bunk bed and Increase the capacity of all bedrooms by 2!", "m_newDetails": "Increases the capacity of your worker's bedrooms by +2", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Do you need every bedroom you own anymore?", "m_newWarning": "Overcrowding comes with its own problems.", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b042ae", "m_key": "Lords::BedroomL3", "m_name": "BedroomL3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_blockBedroomLevel =3", "m_giftCard": "", "m_dollarCost": 2000, "m_factionCost": 4, "m_icon": "ResearchBedroomCapacity", "m_title": "Stacked Beds", "m_description": "You can fit more workers in here if you add a third bed to the bunks. Increases capacity by 3!", "m_position": "(824.0521, 483.49, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:22", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Stacked Beds", "m_newDescription": "<b>Available during Early Access updates</b>\nYou can fit more workers in here if you add a third bed to the bunks. Increases capacity by 3!", "m_newDetails": "Increases the capacity of your worker's bedrooms by +3", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Do you need every bedroom you own anymore?", "m_newWarning": "Overcrowding comes with its own problems.", "m_newLore": ""}, {"id": "66e465e35d2a8102ba3319b2", "m_key": "Royal::BloodAxe", "m_name": "BloodAxe", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 400, "m_factionCost": 4, "m_icon": "ResearchPaints", "m_title": "Blood Luster", "m_description": "It wants to cut and tear. Feed it blood before it turns on you.", "m_position": "(695.00, -201.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:45", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Blood Luster", "m_newDescription": "<b>Available during Early Access updates</b>\nIt wants to cut and tear. Feed it blood before it turns on you.", "m_newDetails": "A tremendous weapon of exceptional quality. Blood Luster loves to cut and slice, always hungry to open up a vein or two.", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "A heavy weapon requires a hunk of a hand.", "m_newWarning": "Resource heavy manufacturing costs put this weapon in the upper echelons for cash flow calamities.", "m_newLore": ""}, {"id": "670cff232fb8e202f7cc4fd1", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_name": "BreadPack", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "MA_RL_BreadPack", "m_dollarCost": 100, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Bread Pack", "m_description": "They do dough, though, don't you know? It's a pack of <b>Bread Parts</b>, not a bag of bread. We're not feeding the ducks here.", "m_position": "(-151.00, -36.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:02", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Bread Pack", "m_newDescription": "They do dough, though, don't you know? It's a pack of <b>Bread Parts</b>, not a bag of bread. We're not feeding the ducks here.", "m_newDetails": "An expansion pack of bread based product parts. Increase the sales price of your sandwiches, baps and buns.", "m_newDetailsLine": "Parts:NGBusinessGift[MA_RL_BreadPack].GetCardPowerParts", "m_newUsage": "Product parts", "m_newHint": "The price of bread may go up as well as down.", "m_newWarning": "Using the same product parts repeatedly will damage your reputation.", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b04268", "m_key": "Commoners::Buildings", "m_name": "Buildings", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>Mill|Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>|Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 0, "m_factionCost": 0, "m_icon": "ResearchPickup", "m_title": "Buildings", "m_description": "The Building Blocks of a sucessful development.", "m_position": "(319.15, -118.63, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:03", "m_activatedAtStart": "True", "m_isLocked": "False", "m_newDisplayName": "Buildings", "m_newDescription": "The Building Blocks of a sucessful development.", "m_newDetails": "Your one stop shop for construction blocks and parts.", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670cff25bd7d6702b497ab3b", "m_key": "Commoners::BuildLumberMill", "m_name": "BuildLumberMill", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON>", "m_unlock": "BuildLumberMill=true", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Lumber Mill", "m_description": "With the Lumber Mill, you can set workers to chop down trees and gain valuable wood resources.", "m_position": "(343.00, -283.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:03", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Lumber Mill", "m_newDescription": "With the Lumber Mill, you can set workers to chop down trees and gain valuable wood resources.", "m_newDetails": "The Lumber Mill gives you the opportunity to harvest wood from local trees. Build it, add a worker, and leave the lumberjack to it.", "m_newDetailsLine": "Max Workers:MAComponentInfo[ActionLumberMill].GetFieldEntry(m_maxWorkers)\nResource:MAComponentInfo[ActionLumberMill].GetFieldEntry(m_input)\n", "m_newUsage": "Production Building", "m_newHint": "Place it as close to a wood as possible for the most efficient felling.", "m_newWarning": "Trees take time to regrow…", "m_newLore": ""}, {"id": "67ba29290b1b3e0bee0fca84", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "BuildTurret", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "BuildTurret=true", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 1, "m_icon": "ResearchPickup", "m_title": "Build Turrets", "m_description": "Auto-fire cannons that target enemies only. Load them with anything, they shoot at will.", "m_position": "(231.00, -368.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:04", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Build Turrets", "m_newDescription": "Auto-fire cannons that target enemies only. Load them with anything, they shoot at will.", "m_newDetails": "The turret is a most powerful weapon. Stout, strong, immovable. It will fire at will, raining down death and destruction on all enemies.", "m_newDetailsLine": "Speed:MAComponentInfo[ActionTurret].GetFieldEntry(m_timeBetweenShots)\nExplosive Radius:MAComponentInfo[ActionTurret].GetFieldEntry(m_explosionRadius)\nExplosive Force:MAComponentInfo[ActionTurret].GetFieldEntry(m_explosionForce)\n", "m_newUsage": "Defense Building", "m_newHint": "Default ammo has its limits. Experiment with other projectiles…", "m_newWarning": "The speed at which the basic turrets fire is lacking in passion.", "m_newLore": ""}, {"id": "67cee29d89c8e00314b46c5d", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "BuildWeaponsmith", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_unlock": "BuildWeaponsmith=true", "m_giftCard": "", "m_dollarCost": 400, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Weaponsmith", "m_description": "Make and equip weapons", "m_position": "(554.00, -280.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:04", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Weaponsmith", "m_newDescription": "Make and equip weapons", "m_newDetails": "The warrior's first port of call, the Weaponsmith can be used to design weapons for heroes. Each weapon can be as unique or as ubiquitous as you please. ", "m_newDetailsLine": "Production Speed:MAComponentInfo[ActionWeaponsmith].GetFieldEntry(m_workerSellSpeed)", "m_newUsage": "Arming Heroes", "m_newHint": "The better the design, the more deadly the weapon.", "m_newWarning": "More does not always mean more. In the case of weapons, a hero needs to kill with it, no more, no less.", "m_newLore": ""}, {"id": "66e465e372ce6202c016cf46", "m_key": "Royal::CastleGuardAxe", "m_name": "CastleGuardAxe", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::BloodAxe", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 275, "m_factionCost": 3, "m_icon": "ResearchPatterns", "m_title": "<PERSON><PERSON>'s Chopper", "m_description": "A squire's best friend. It's ithcing to be buried in the guts of an enemy.", "m_position": "(725.00, -9.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:45", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "<PERSON><PERSON>'s Chopper", "m_newDescription": "<b>Available during Early Access updates</b>\nA squire's best friend. It's itching to be buried in the guts of an enemy.", "m_newDetails": "Made from local Ash trees and metals mined from Wyrmscar, the Squire's Chopper is a reliable weapon with solid killing capabilities. A most reliable weapon.", "m_newDetailsLine": "", "m_newUsage": "Cutting into flesh", "m_newHint": "Choose your enemies wisely…", "m_newWarning": "The greater the threat, the weaker this chopper becomes.", "m_newLore": ""}, {"id": "670cff23f35328031c8aab54", "m_key": "Commoners::Chefs", "m_name": "Chefs", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Chefs", "m_description": "An advanced worker is a skilled worker and a skilled worker works fast.  The <b>Chef</b> is the perfect worker for a factory that makes Food products.", "m_position": "(-201.00, -353.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:04", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Chefs", "m_newDescription": "<b>Available during Early Access updates</b>\nAn advanced worker is a skilled worker and a skilled worker works fast.  The <b>Chef</b> is the perfect worker for a factory that makes Food products.", "m_newDetails": "Monarchs of Mealtimes, these Chefs are skilled workers of note. Able to produce food based products at alarming speeds.", "m_newDetailsLine": "", "m_newUsage": "Factory workers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67e143111c82d602c3d27c4f", "m_key": "Lords::ClickBoost1", "m_name": "ClickBoost1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ClickBoost2", "m_unlock": "m_tapHoldMultiplier=1.05", "m_giftCard": "", "m_dollarCost": 300, "m_factionCost": 4, "m_icon": "ResearchFactoryLevel", "m_title": "Click Boost 1", "m_description": "Increase the bonus granted by clicking buildings by 5%.", "m_position": "(163.2, 488.54, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:22", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Click Boost 1", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67e14315f2ddf9030f403653", "m_key": "Lords::ClickBoost2", "m_name": "ClickBoost2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ClickBoost3", "m_unlock": "m_tapHoldMultiplier=1.07", "m_giftCard": "", "m_dollarCost": 900, "m_factionCost": 12, "m_icon": "ResearchFactoryLevel", "m_title": "Click Boost 2", "m_description": "Increase the bonus granted by clicking buildings by 7%.", "m_position": "(78.94, 608.85, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:22", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Click Boost 2", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67e1431d59a34102d2776fc3", "m_key": "Lords::ClickBoost3", "m_name": "ClickBoost3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::<PERSON>lickB<PERSON>t4", "m_unlock": "m_tapHoldMultiplier=1.09", "m_giftCard": "", "m_dollarCost": 1500, "m_factionCost": 18, "m_icon": "ResearchFactoryLevel", "m_title": "Click Boost 3", "m_description": "tbd.", "m_position": "(-38.72, 705.69, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:23", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Click Boost 3", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67e143261c82d602c3d27d25", "m_key": "Lords::<PERSON>lickB<PERSON>t4", "m_name": "ClickBoost4", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_tapHoldMultiplier=1.12", "m_giftCard": "", "m_dollarCost": 2500, "m_factionCost": 27, "m_icon": "ResearchFactoryLevel", "m_title": "Click Boost 4", "m_description": "tbd.", "m_position": "(-168.08, 789.91, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:23", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Click Boost 4", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2c6a278802e3610bd8", "m_key": "Lords::ClothesSalesPricePlus1", "m_name": "ClothesSalesPricePlus1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ClothesSalesPricePlus2", "m_unlock": "m_clothesSalesPriceMarkup= 1.05", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 2, "m_icon": "ResearchFactoryLevel", "m_title": "Clothes Sales Price Plus", "m_description": "increase by 5%", "m_position": "(-188.31, -84.4, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:23", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Clothes Sales Price Plus", "m_newDescription": "increase by 5%", "m_newDetails": "Boosts the value of all clothing sales by 5%", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Increase profit without additional resource costs.", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2c6a278802e3610bdb", "m_key": "Lords::ClothesSalesPricePlus2", "m_name": "ClothesSalesPricePlus2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ClothesSalesPricePlus3", "m_unlock": "m_clothesSalesPriceMarkup= 1.1", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 4, "m_icon": "ResearchFactoryLevel", "m_title": "Clothes Sales Price Plus", "m_description": "increase by 10%", "m_position": "(-368.75, -164.5, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:24", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Clothes Sales Price Plus", "m_newDescription": "increase by 10%", "m_newDetails": "Boosts the value of all clothing sales by 10%", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Increase profit without additional resource costs.", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2c6a278802e3610bde", "m_key": "Lords::ClothesSalesPricePlus3", "m_name": "ClothesSalesPricePlus3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::ClothesSalesPricePlus4", "m_unlock": "m_clothesSalesPriceMarkup= 1.15", "m_giftCard": "", "m_dollarCost": 750, "m_factionCost": 6, "m_icon": "ResearchFactoryLevel", "m_title": "Clothes Sales Price Plus", "m_description": "increase by 15%", "m_position": "(-519.54, -239.79, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:25", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Clothes Sales Price Plus", "m_newDescription": "increase by 15%", "m_newDetails": "Boosts the value of all clothing sales by 15%", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Increase profit without additional resource costs.", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2c6a278802e3610be1", "m_key": "Lords::ClothesSalesPricePlus4", "m_name": "ClothesSalesPricePlus4", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::<PERSON><PERSON><PERSON>SalesPricePlus5", "m_unlock": "m_clothesSalesPriceMarkup= 1.25", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 8, "m_icon": "ResearchFactoryLevel", "m_title": "Clothes Sales Price Plus", "m_description": "increase by 25%", "m_position": "(-748.01, -317.52, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:25", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Clothes Sales Price Plus", "m_newDescription": "increase by 25%", "m_newDetails": "Boosts the value of all clothing sales by 25%", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Increase profit without additional resource costs.", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2c6a278802e3610be4", "m_key": "Lords::<PERSON><PERSON><PERSON>SalesPricePlus5", "m_name": "ClothesSalesPricePlus5", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_clothesSalesPriceMarkup  1.5", "m_giftCard": "", "m_dollarCost": 1500, "m_factionCost": 10, "m_icon": "ResearchFactoryLevel", "m_title": "Clothes Sales Price Plus", "m_description": "increase by 50%", "m_position": "(-940.82, -392.69, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:25", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Clothes Sales Price Plus", "m_newDescription": "increase by 50%", "m_newDetails": "Boosts the value of all clothing sales by 50%", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Increase profit without additional resource costs.", "m_newWarning": "", "m_newLore": ""}, {"id": "66d1bf1571545702ce232a48", "m_key": "Commoners::<PERSON><PERSON><PERSON>", "m_name": "Cross<PERSON>inger", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handGestureCrossFingers=true", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Gesture: Cross Fingers", "m_description": "<b>Cross Fingers</b>, not angry ones. No, no, no. These are for wishing good luck on passing workers. Good luck getting a pay rise, you filthy oiks!", "m_position": "(-334.00, 433.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:05", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Gesture: Cross Fingers", "m_newDescription": "<b>Cross Fingers</b>, not angry ones. No, no, no. These are for wishing good luck on passing workers. Good luck getting a pay rise, you filthy oiks!", "m_newDetails": "A simple gesture, a way to signal to those around you that you wish them the best of luck.", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Wishing a fellow good luck does necessarily mean the Lady will give her blessings…", "m_newWarning": "Luck is a figment of the imagination. No good will come of this. Or bad.", "m_newLore": ""}, {"id": "66e465e572ce6202c016cf6b", "m_key": "Royal::<PERSON><PERSON><PERSON><PERSON>", "m_name": "DeathH<PERSON>mer", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 1750, "m_factionCost": 10, "m_icon": "ResearchPaints", "m_title": "Death Claw", "m_description": "Glides through the air, glides through the face.", "m_position": "(-178.00, -272.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:45", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Death Claw", "m_newDescription": "<b>Available during Early Access updates</b>\nGlides through the air, glides through the face.", "m_newDetails": "It glides through the air like it glides through a face – with all the grace and charm of a vicious lump of turned steel with hate for a heart.", "m_newDetailsLine": "", "m_newUsage": "In combat", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670d1071e76a77031e647858", "m_key": "Commoners::DisciplineHeroes ", "m_name": "DisciplineHeroes ", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Discipline Heroes ", "m_description": "Oh sure, they're big and muscular, but you have a hand bigger than their faces! <b>Discipline Heroes</b> like a god.", "m_position": "(295.35, 469.25, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:05", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Discipline Heroes ", "m_newDescription": "<b>Available during Early Access updates</b> \nOh sure, they're big and muscular, but you have a hand bigger than their faces! <b>Discipline Heroes</b> like a god.", "m_newDetails": "A useful Hand Power, with this in your tool bag, you will be able to show your heroes exactly who is boss. Spare the rod, spoil the barbarian.", "m_newDetailsLine": "", "m_newUsage": "Heroes", "m_newHint": "Discipline in small doses does little harm…", "m_newWarning": "Push them too hard and you risk losing them.", "m_newLore": ""}, {"id": "670e504db4a167031102c712", "m_key": "Commoners::DisciplineWorkers", "m_name": "DisciplineWorkers", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Discipline Workers", "m_description": "The common folk, don't let them step out of line. Always <b>Discipline Workers</b> before they get out of line.", "m_position": "(745.00, 398.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:05", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Discipline Workers", "m_newDescription": "<b>Available during Early Access updates</b> \nThe common folk, don't let them step out of line. Always <b>Discipline Workers</b> before they get out of line.", "m_newDetails": "A factory owner knows that their workforce is only as good as their discipline. Here, then, is a tool for the manager in your God Hand.", "m_newDetailsLine": "", "m_newUsage": "Workers", "m_newHint": "Discipline in small doses does little harm…", "m_newWarning": "Push them too hard and you risk losing them.", "m_newLore": ""}, {"id": "670d1071f35328031c8d5f06", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "m_name": "EncourageHeroes ", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::DisciplineHeroes ", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Encourage Heroes ", "m_description": "Give them a boost of self confidence with the glory that is <b>Encourage Heroes</b>.", "m_position": "(248.21, 275.67, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:06", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Encourage Heroes ", "m_newDescription": "<b>Available during Early Access updates</b>\nGive them a boost of self confidence with the glory that is <b>Encourage Heroes</b>.", "m_newDetails": "Even the most zealous of heroes has doubts. Encourage them and their heroic tendencies to keep them on the straight and narrow.", "m_newDetailsLine": "", "m_newUsage": "Heroes", "m_newHint": "Use sparingly…", "m_newWarning": "It can quickly slide into mollycoddling…", "m_newLore": ""}, {"id": "670e504dcb288b0318c68001", "m_key": "Commoners::En<PERSON><PERSON>W<PERSON>kers", "m_name": "EncourageWorkers", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::DisciplineWorkers", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Encourage Workers", "m_description": "There's more than one way to skin a cat. And there's more than one way to <b>Encourage Workers</b>, but none are as kind as this.", "m_position": "(544.00, 441.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:06", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Encourage Workers", "m_newDescription": "<b>Available during Early Access updates</b>\nThere's more than one way to skin a cat. And there's more than one way to <b>Encourage Workers</b>, but none are as kind as this.", "m_newDetails": "A good worker is a happy worker. With this power, you can gently encourage any worker to do more.", "m_newDetailsLine": "", "m_newUsage": "Workers", "m_newHint": "Don't over do it.", "m_newWarning": "Workers are no fools. If they think you're a soft touch…", "m_newLore": ""}, {"id": "670e4e40cb288b0318c64867", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "ExpertChefs", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Expert Chefs", "m_description": "They're <b>Expert Chefs</b> because they've been in the job for years and can mash a potato in seconds with their hands tied behind their backs. But can they tidy up after themselves? Can they feck.", "m_position": "(-413.00, -393.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:06", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Expert Chefs", "m_newDescription": "<b>Available during Early Access updates</b>\nThey're <b>Expert Chefs</b> because they've been in the job for years and can mash a potato in seconds with their hands tied behind their backs. But can they tidy up after themselves? Can they feck.", "m_newDetails": "Take a skilled factory food worker and let them stew for 10,000 hours. The results are worth the wait. The Expert Chef is the ninja of the kitchen.", "m_newDetailsLine": "", "m_newUsage": "Food Product Manufacturing", "m_newHint": "They work best when surrounded by associates.", "m_newWarning": "", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b0429a", "m_key": "Lords::FactoryBlockL1", "m_name": "FactoryBlockL1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_blockFactoryLevel=2", "m_giftCard": "", "m_dollarCost": 2000, "m_factionCost": 2, "m_icon": "ResearchFactoryLevel", "m_title": "Superior Factory Productivity", "m_description": "Squeeze more out of your Factory, increase its efficiency by 4%. \n<b>Even Faster = Even Better!</b>", "m_position": "(502, 184.5946, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:26", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Superior Factory Productivity", "m_newDescription": "<b>Available during Early Access updates</b>\nSqueeze more out of your Factory, increase its efficiency by 4%. \n<b>Even Faster = Even Better!</b>", "m_newDetails": "Squeeze more out of your Factory, increase its efficiency by 4%. \n<b>Even Faster = Even Better!</b>", "m_newDetailsLine": "", "m_newUsage": "Factory Efficiency", "m_newHint": "Efficiency is not the only metric to measure...", "m_newWarning": "", "m_newLore": ""}, {"id": "670e4e3fcb288b0318c64856", "m_key": "Commoners::Farmer", "m_name": "<PERSON>", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "<PERSON>", "m_description": "Put this <b>Farmer</b> in the factory, you're wasting your time. Put this <b><PERSON></b> in your Farm, you're making hay while the sun shines.", "m_position": "(38.00, -490.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:06", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "<PERSON>", "m_newDescription": "<b>Available during Early Access updates</b>\nPut this <b>Farmer</b> in the factory, you're wasting your time. Put this <b>Farmer</b> in your Farm, you're making hay while the sun shines.", "m_newDetails": "The skilled worker, Farmer, makes light work of the harvest.", "m_newDetailsLine": "", "m_newUsage": "Farm", "m_newHint": "A farm full of Farmers frees up your labourers...", "m_newWarning": "", "m_newLore": ""}, {"id": "66d1bf168ad9f402fc0a896b", "m_key": "Commoners::<PERSON><PERSON><PERSON>", "m_name": "FOff", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handGestureFOff=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Gesture: F-Off", "m_description": "Do you shake your mother's hand with that dirty mitt? Filthy.", "m_position": "(-440.50, 81.50, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:07", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Gesture: F-Off", "m_newDescription": "Do you shake your mother's hand with that dirty mitt? Filthy.", "m_newDetails": "A useful gesture that is packed with meaning. As offensive as any hand can get. ", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "Not all Albions are equally offended…", "m_newWarning": "Disrespect comes with adverse effects.", "m_newLore": ""}, {"id": "67d42bc944847c031e10ad6e", "m_key": "Lords::FoodSalesPricePlus1", "m_name": "FoodSalesPricePlus1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::FoodSalesPricePlus2", "m_unlock": "m_foodSalesPriceMarkup= 1.05", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 2, "m_icon": "ResearchFactoryLevel", "m_title": "Food Sales Price Plus", "m_description": "increase by 5%", "m_position": "(-318.16, 13.41, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:26", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Food Sales Price Plus", "m_newDescription": "increase by 5%", "m_newDetails": "Bolster your profits on all Food orders.", "m_newDetailsLine": "", "m_newUsage": "Food Orders", "m_newHint": "Force the market to bend to your will…", "m_newWarning": "Money is the root of all evil", "m_newLore": ""}, {"id": "67d42bca44847c031e10ad78", "m_key": "Lords::FoodSalesPricePlus2", "m_name": "FoodSalesPricePlus2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::FoodSalesPricePlus3", "m_unlock": "m_foodSalesPriceMarkup= 1.1", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 4, "m_icon": "ResearchFactoryLevel", "m_title": "Food Sales Price Plus", "m_description": "increase by 10%", "m_position": "(-566.5, 39.69, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:27", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Food Sales Price Plus", "m_newDescription": "increase by 10%", "m_newDetails": "Bolster your profits on all Food orders.", "m_newDetailsLine": "", "m_newUsage": "Food Orders", "m_newHint": "Force the market to bend to your will…", "m_newWarning": "Money is the root of all evil", "m_newLore": ""}, {"id": "67d42bca44847c031e10ad82", "m_key": "Lords::FoodSalesPricePlus3", "m_name": "FoodSalesPricePlus3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::FoodSalesPricePlus4", "m_unlock": "m_foodSalesPriceMarkup= 1.15", "m_giftCard": "", "m_dollarCost": 750, "m_factionCost": 6, "m_icon": "ResearchFactoryLevel", "m_title": "Food Sales Price Plus", "m_description": "increase by 15%", "m_position": "(-680.82, -51.02, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:27", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Food Sales Price Plus", "m_newDescription": "increase by 15%", "m_newDetails": "Bolster your profits on all Food orders.", "m_newDetailsLine": "", "m_newUsage": "Food Orders", "m_newHint": "Force the market to bend to your will…", "m_newWarning": "Money is the root of all evil", "m_newLore": ""}, {"id": "67d42bca44847c031e10ad8c", "m_key": "Lords::FoodSalesPricePlus4", "m_name": "FoodSalesPricePlus4", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::FoodSalesPricePlus5", "m_unlock": "m_foodSalesPriceMarkup= 1.25", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 8, "m_icon": "ResearchFactoryLevel", "m_title": "Food Sales Price Plus", "m_description": "increase by 25%", "m_position": "(-946.52, -31.84, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:27", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Food Sales Price Plus", "m_newDescription": "increase by 25%", "m_newDetails": "Bolster your profits on all Food orders.", "m_newDetailsLine": "", "m_newUsage": "Food Orders", "m_newHint": "Force the market to bend to your will…", "m_newWarning": "Money is the root of all evil", "m_newLore": ""}, {"id": "67d42bca44847c031e10ad96", "m_key": "Lords::FoodSalesPricePlus5", "m_name": "FoodSalesPricePlus5", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_foodSalesPriceMarkup= 1.5", "m_giftCard": "", "m_dollarCost": 1500, "m_factionCost": 10, "m_icon": "ResearchFactoryLevel", "m_title": "Food Sales Price Plus", "m_description": "increase by 50%", "m_position": "(-1168.47, -110.29, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:28", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Food Sales Price Plus", "m_newDescription": "increase by 50%", "m_newDetails": "Bolster your profits on all Food orders.", "m_newDetailsLine": "", "m_newUsage": "Food Orders", "m_newHint": "Force the market to bend to your will…", "m_newWarning": "Money is the root of all evil", "m_newLore": ""}, {"id": "66d1bf17373f5002c5c6e38a", "m_key": "Commoners::<PERSON><PERSON><PERSON>_<PERSON><PERSON>", "m_name": "Gesture_Loose", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handGestureHangLoose=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Gesture: <PERSON><PERSON>", "m_description": "Tell your people to <b><PERSON></b>. Then ask yourself what your people think it means.", "m_position": "(-619.00, 142.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-06-27 17:05:17", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Gesture: <PERSON><PERSON>", "m_newDescription": "Tell your people to <b><PERSON></b>. Then ask yourself what your people think it means.", "m_newDetails": "The hand signal favoured by the slacker and the sloth. For work shy fops and beachcombers.", "m_newDetailsLine": "", "m_newUsage": "Gestures", "m_newHint": "Best used on beaches and coastlines.", "m_newWarning": "The term \"hang loose\" has nothing to do with hanging.", "m_newLore": ""}, {"id": "66d1bf168ad9f402fc0a8976", "m_key": "Commoners::G<PERSON><PERSON>_Loser", "m_name": "Gesture_Loser", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handGestureLoser=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Gesture: Loser", "m_description": "How to win enemies and influence people's disdain. Get the <b>Loser</b> gesture!", "m_position": "(-508.00, 242.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:07", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Gesture: Loser", "m_newDescription": "How to win enemies and influence people's disdain. Get the <b>Loser</b> gesture!", "m_newDetails": "An insultory gesture designed to give maximum offence with minimum action. ", "m_newDetailsLine": "", "m_newUsage": "Gestures", "m_newHint": "Works best in combination.", "m_newWarning": "The more you use this gesture, the more harm you will do to your reputation.", "m_newLore": ""}, {"id": "66d1bf17373f5002c5c6e35d", "m_key": "Commoners::G<PERSON><PERSON>_<PERSON>", "m_name": "Gesture_Peace", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handGesturePeace=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Gesture: Peace", "m_description": "Wish <b>Peace</b> upon your enemies, perhaps they'll think twice about attacking…", "m_position": "(-396.00, 328.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:07", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Gesture: Peace", "m_newDescription": "Wish <b>Peace</b> upon your enemies, perhaps they'll think twice about attacking…", "m_newDetails": "War is not the answer. Give peace and love a chance.", "m_newDetailsLine": "", "m_newUsage": "Gestures", "m_newHint": "I HAVE NO HINTS", "m_newWarning": "I HAVE NO WARNING", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b0427c", "m_key": "Commoners::Gest<PERSON>", "m_name": "Gestures", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::Gesture_ThumbsUp|Commoners::Gesture_ThumbsDown|Commoners::A<PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 0, "m_factionCost": 0, "m_icon": "ResearchPickup", "m_title": "Gestures", "m_description": "Follow this path to unlock a myriad of mood enhancing <b>Gestures</b>.", "m_position": "(83.46, 81.75, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:09", "m_activatedAtStart": "True", "m_isLocked": "False", "m_newDisplayName": "Gestures", "m_newDescription": "Follow this path to unlock a myriad of mood enhancing <b>Gestures</b>.", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "66d1bf168ad9f402fc0a8985", "m_key": "Commoners::Gesture_ThumbsDown", "m_name": "Gesture_ThumbsDown", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::Gesture_Loser|Commoners::Gest<PERSON>_<PERSON>ose", "m_unlock": "m_handGestureThumbsDown=true", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Gesture: Thumbs Down", "m_description": "In Rome, <b>Thumbs Down</b> meant don't kill him. Stupid Romans, getting it wrong like that.", "m_position": "(-261.00, 192.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:08", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Gesture: Thumbs Down", "m_newDescription": "In Rome, <b>Thumbs Down</b> meant don't kill him. Stupid Romans, getting it wrong like that.", "m_newDetails": "Show your displeasure or decide a person's fate with this judgemental gesture.", "m_newDetailsLine": "", "m_newUsage": "Gestures", "m_newHint": "There is no court, but there are places where a hand may decide a person's fate…", "m_newWarning": "Once a man is hung, he cannot be unhung.", "m_newLore": ""}, {"id": "66d1bf17373f5002c5c6e33e", "m_key": "Commoners::Gesture_ThumbsUp", "m_name": "Gesture_ThumbsUp", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON>|Commoners::Gesture_Peace", "m_unlock": "m_handGestureThumbsUp=true", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Gesture: Thumbs Up", "m_description": "Kill him!", "m_position": "(-100.00, 261.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:08", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Gesture: Thumbs Up", "m_newDescription": "Kill him!", "m_newDetails": "Show mercy with this positive reaffirming gesture designed to put minds at rest.", "m_newDetailsLine": "", "m_newUsage": "Gestures", "m_newHint": "There is no court, but there are places where a hand may decide a person's fate…", "m_newWarning": "Leniency can be a weakness…", "m_newLore": ""}, {"id": "66d1bf16dcba5702dc96cf4a", "m_key": "Commoners::<PERSON><PERSON><PERSON>_<PERSON><PERSON>", "m_name": "Gesture_Wanker", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handGestureWanker=true", "m_giftCard": "", "m_dollarCost": 1500, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Gesture: <PERSON><PERSON>", "m_description": "This is an R18 gesture and, as such, we shall refrain from describing it.", "m_position": "(-605.50, -33.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:09", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Gesture: <PERSON><PERSON>", "m_newDescription": "This is an R18 gesture and, as such, we shall refrain from describing it.", "m_newDetails": "There can be no confusion as to the meaning of this gesture. If you wish to inform a person of your total disregard for them, their opinions, their entire existence, then this is the gesture for you.", "m_newDetailsLine": "", "m_newUsage": "Gestures", "m_newHint": "???", "m_newWarning": "???", "m_newLore": ""}, {"id": "67e137aa834277030d91a058", "m_key": "Lords::GrowCrops1", "m_name": "GrowCrops1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::GrowCrops2", "m_unlock": "m_wheatUpgradeLevel=1", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 6, "m_icon": "ResearchFactoryLevel", "m_title": "Grow Crops 1", "m_description": "tbd.", "m_position": "(-76.14, 462.46, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:28", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Grow Crops 1", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67e137ebf2ddf9030f3ff9e2", "m_key": "Lords::GrowCrops2", "m_name": "GrowCrops2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::GrowCrops3", "m_unlock": "m_wheatUpgradeLevel=2", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 9, "m_icon": "ResearchFactoryLevel", "m_title": "Grow Crops 2", "m_description": "tbd.", "m_position": "(-222.34, 541.31, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:28", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Grow Crops 2", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67e137f0f2ddf9030f3ffa0d", "m_key": "Lords::GrowCrops3", "m_name": "GrowCrops3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::GrowCrops4", "m_unlock": "m_wheatUpgradeLevel=3", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 12, "m_icon": "ResearchFactoryLevel", "m_title": "Grow Crops 3", "m_description": "tbd.", "m_position": "(-321.31, 630.67, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:28", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Grow Crops 3", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67e137f4834277030d91a2fc", "m_key": "Lords::GrowCrops4", "m_name": "GrowCrops4", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::GrowCrops5", "m_unlock": "m_wheatUpgradeLevel=4", "m_giftCard": "", "m_dollarCost": 2500, "m_factionCost": 15, "m_icon": "ResearchFactoryLevel", "m_title": "Grow Crops 4", "m_description": "tbd.", "m_position": "(-449.52, 708.72, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:29", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Grow Crops 4", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67e137faf2ddf9030f3ffa31", "m_key": "Lords::GrowCrops5", "m_name": "GrowCrops5", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_wheatUpgradeLevel=5", "m_giftCard": "", "m_dollarCost": 4000, "m_factionCost": 20, "m_icon": "ResearchFactoryLevel", "m_title": "Grow Crops 5", "m_description": "tbd.", "m_position": "(-530.3, 797.56, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:29", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Grow Crops 5", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "66e465e5c8409802f420099d", "m_key": "Royal::<PERSON><PERSON><PERSON>", "m_name": "HammerHard", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 5, "m_icon": "ResearchPaints", "m_title": "<PERSON>", "m_description": "Strikes like fabled pugilist <PERSON>. ", "m_position": "(-356.00, -127.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:46", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "<PERSON>", "m_newDescription": "<b>Available during Early Access updates</b>\nStrikes like fabled pugilist <PERSON>. ", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "66e465e5c8409802f420098e", "m_key": "Royal::<PERSON><PERSON><PERSON>", "m_name": "HammerPoor", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::<PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 3, "m_icon": "ResearchPaints", "m_title": "Thumper", "m_description": "Kicks like a mule on fire.", "m_position": "(-463.00, 36.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:46", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Thumper", "m_newDescription": "<b>Available during Early Access updates</b>\nKicks like a mule on fire.", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6666ec94182bfe1fd6b04258", "m_key": "Commoners::Hand", "m_name": "Hand", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>|Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>|Commoners::PickupProduce|Commoners::Pickup<PERSON><PERSON>|Commoners::<PERSON>|Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 0, "m_factionCost": 0, "m_icon": "ResearchPickup", "m_title": "Hand", "m_description": "Power of the God Hand compels you!", "m_position": "(368.18, 73.34, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:10", "m_activatedAtStart": "True", "m_isLocked": "False", "m_newDisplayName": "Hand", "m_newDescription": "Power of the God Hand compels you!", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "66700c76da5fdc6d5d0182bc", "m_key": "Mystic::HandPowerDigL1", "m_name": "HandPowerDigL1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerDig=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 15, "m_icon": "ResearchHandPowerDig", "m_title": "Dig it baby", "m_description": "Dig out treasures", "m_position": "(137.00, 172.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:32", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Dig it baby", "m_newDescription": "Dig out treasures", "m_newDetails": "Use your hand to bite deep into the earth in search of buried treasures. ", "m_newDetailsLine": "", "m_newUsage": "Dig Sites", "m_newHint": "The ground that glows is gracious in its gifts", "m_newWarning": "Careful, you don't want to break a nail.", "m_newLore": ""}, {"id": "66700c76da5fdc6d5d01828f", "m_key": "Mystic::HandPowerFireballL1", "m_name": "HandPowerFireballL1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerFireballL2", "m_unlock": "m_handPowerFireball=true", "m_giftCard": "", "m_dollarCost": 8000, "m_factionCost": 10, "m_icon": "ResearchHandPowerFireball", "m_title": "Ball of Fire", "m_description": "TODO: A ball of fire will engulf more enemies", "m_position": "(39.69, 32.77, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:33", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Ball of Fire", "m_newDescription": "TODO: A ball of fire will engulf more enemies", "m_newDetails": "Bring death from above. The Fireball creates pockets of devastation wherever it lands.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Fireball1].m_baseDamage\nHits:MAHandPowerInfo[Fireball1].m_numOfHits\nRadius:MAHandPowerInfo[Fireball1].m_aoeRadius\nMana Cost:MAHandPowerInfo[Fireball1].m_manaCost\nCooldown:MAHandPowerInfo[Fireball1].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Acts like a blazing boulder…", "m_newWarning": "Fire knows not who is friend and who is foe…", "m_newLore": ""}, {"id": "66700c76da5fdc6d5d018294", "m_key": "Mystic::HandPowerFireballL2", "m_name": "HandPowerFireballL2", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerFireballL3", "m_unlock": "m_handPowerFireballLevel=2", "m_giftCard": "", "m_dollarCost": 11200, "m_factionCost": 30, "m_icon": "ResearchHandPowerFireball", "m_title": "Ball of Fire Level 2", "m_description": "TODO: A ball of fire will engulf more enemies", "m_position": "(-157.09, 127.39, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:33", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Ball of Fire Level 2", "m_newDescription": "TODO: A ball of fire will engulf more enemies", "m_newDetails": "Fan the flames of fury, increase the damage done.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Fireball2].m_baseDamage\nHits:MAHandPowerInfo[Fireball2].m_numOfHits\nRadius:MAHandPowerInfo[Fireball2].m_aoeRadius\nMana Cost:MAHandPowerInfo[Fireball2].m_manaCost\nCooldown:MAHandPowerInfo[Fireball2].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Acts like a blazing boulder…", "m_newWarning": "Fire knows not who is friend and who is foe…", "m_newLore": ""}, {"id": "66700c76da5fdc6d5d018299", "m_key": "Mystic::HandPowerFireballL3", "m_name": "HandPowerFireballL3", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerFireballL4", "m_unlock": "m_handPowerFireballLevel=3", "m_giftCard": "", "m_dollarCost": 15000, "m_factionCost": 60, "m_icon": "ResearchHandPowerFireball", "m_title": "Ball of Fire Level 3", "m_description": "TODO: A ball of fire will engulf more enemies", "m_position": "(-328.39, 224.96, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:33", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Ball of Fire Level 3", "m_newDescription": "TODO: A ball of fire will engulf more enemies", "m_newDetails": "Let them burn! The dead have nowhere to hide from this heated horror.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Fireball3].m_baseDamage\nHits:MAHandPowerInfo[Fireball3].m_numOfHits\nRadius:MAHandPowerInfo[Fireball3].m_aoeRadius\nMana Cost:MAHandPowerInfo[Fireball3].m_manaCost\nCooldown:MAHandPowerInfo[Fireball3].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Acts like a blazing boulder…", "m_newWarning": "Fire knows not who is friend and who is foe…", "m_newLore": ""}, {"id": "6798fb39b5470e02f370b2b7", "m_key": "Mystic::HandPowerFireballL4", "m_name": "HandPowerFireballL4", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerFireballL5", "m_unlock": "m_handPowerFireballLevel=4", "m_giftCard": "", "m_dollarCost": 20000, "m_factionCost": 100, "m_icon": "ResearchHandPowerFireball", "m_title": "Ball of Fire Level 4", "m_description": "TODO: A ball of fire will engulf more enemies", "m_position": "(-529.39, 330.96, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:34", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Ball of Fire Level 4", "m_newDescription": "TODO: A ball of fire will engulf more enemies", "m_newDetails": "Engulfed in these flames, a zombie will feel no pain…", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Fireball4].m_baseDamage\nHits:MAHandPowerInfo[Fireball4].m_numOfHits\nRadius:MAHandPowerInfo[Fireball4].m_aoeRadius\nMana Cost:MAHandPowerInfo[Fireball4].m_manaCost\nCooldown:MAHandPowerInfo[Fireball4].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Acts like a blazing boulder…", "m_newWarning": "Fire knows not who is friend and who is foe…", "m_newLore": ""}, {"id": "6798fb400d763303141dc34b", "m_key": "Mystic::HandPowerFireballL5", "m_name": "HandPowerFireballL5", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerFireballLevel=5", "m_giftCard": "", "m_dollarCost": 25000, "m_factionCost": 150, "m_icon": "ResearchHandPowerFireball", "m_title": "Ball of Fire Level 5", "m_description": "TODO: A ball of fire will engulf more enemies", "m_position": "(-721.39, 448.96, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:34", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Ball of Fire Level 5", "m_newDescription": "TODO: A ball of fire will engulf more enemies", "m_newDetails": "A ring of fire so wide that were it to grow any larger, it would leave the earth and rise up as a new star.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Fireball5].m_baseDamage\nHits:MAHandPowerInfo[Fireball5].m_numOfHits\nRadius:MAHandPowerInfo[Fireball5].m_aoeRadius\nMana Cost:MAHandPowerInfo[Fireball5].m_manaCost\nCooldown:MAHandPowerInfo[Fireball5].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Acts like a blazing boulder…", "m_newWarning": "Fire knows not who is friend and who is foe…", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b042fa", "m_key": "Mystic::HandPowers", "m_name": "HandPowers", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersLightningL1|Mystic::<PERSON><PERSON><PERSON><PERSON>|Mystic::HandPowerFireballL1|Mystic::HandPowersFlameThrowerL1|Mystic::HandPowerWaterBlobL1|Mystic::HandPowerWaterSpoutL1|Mystic::HandPowerDigL1|Mystic::<PERSON>aIncrease1", "m_unlock": "m_handPowers=true", "m_giftCard": "", "m_dollarCost": 0, "m_factionCost": 0, "m_icon": "ResearchPickup", "m_title": "Sorcerer's Hand", "m_description": "Open up a new world of possibilities with this magical unlock. Allows the owner to conjure mystical powers against the armies of the night.", "m_position": "(190.74, -71.71, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:34", "m_activatedAtStart": "True", "m_isLocked": "False", "m_newDisplayName": "Sorcerer's Hand", "m_newDescription": "Open up a new world of possibilities with this magical unlock. Allows the owner to conjure mystical powers against the armies of the night.", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "66700c76da5fdc6d5d018280", "m_key": "Mystic::HandPowersFlameThrowerL1", "m_name": "HandPowersFlameThrowerL1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersFlameThrowerL2", "m_unlock": "m_handPowerFlamethrower=true", "m_giftCard": "", "m_dollarCost": 6000, "m_factionCost": 10, "m_icon": "ResearchHandPowerFlamethower", "m_title": "Flame of Fire", "m_description": "TODO: Touch your enemies", "m_position": "(344.00, -182.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:34", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Flame of Fire", "m_newDescription": "The curl of a dragon's tongue. Unleashing this power is like unleashing the demon wyrm upon a defenceless town built from tinder brush and matches.", "m_newDetails": "Fires a stream of flames in a straight line.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Flamethrower1].m_baseDamage\nAttack Width:MAHandPowerInfo[Flamethrower1].m_attackWidth\nAttack Length:MAHandPowerInfo[Flamethrower1].m_attackLength\nMan Cost:MAHandPowerInfo[Flamethrower1].m_manaCost\nCooldown:MAHandPowerInfo[Flamethrower1].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Whip crack precision with a scorched earth effect…", "m_newWarning": "Fire is indiscriminate. All are willing recipients of her love…", "m_newLore": ""}, {"id": "66700c76da5fdc6d5d018285", "m_key": "Mystic::HandPowersFlameThrowerL2", "m_name": "HandPowersFlameThrowerL2", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersFlameThrowerL3", "m_unlock": "m_handPowerFlamethrowerLevel=2", "m_giftCard": "", "m_dollarCost": 8400, "m_factionCost": 20, "m_icon": "ResearchHandPowerFlamethower", "m_title": "Flame of Fire Level 2", "m_description": "TODO: Touch More enemies", "m_position": "(517.00, -293.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:35", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Flame of Fire Level 2", "m_newDescription": "Let the dragon lick and lap at your enemies heels as they flee from its boiling kisses.", "m_newDetails": "Increase the power of the flame.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Flamethrower2].m_baseDamage\nAttack Width:MAHandPowerInfo[Flamethrower2].m_attackWidth\nAttack Length:MAHandPowerInfo[Flamethrower2].m_attackLength\nMan Cost:MAHandPowerInfo[Flamethrower2].m_manaCost\nCooldown:MAHandPowerInfo[Flamethrower2].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Whip crack precision with a scorched earth effect…", "m_newWarning": "Fire is indiscriminate. All are willing recipients of her love…", "m_newLore": ""}, {"id": "66700c76da5fdc6d5d01828a", "m_key": "Mystic::HandPowersFlameThrowerL3", "m_name": "HandPowersFlameThrowerL3", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersFlameThrowerL4", "m_unlock": "m_handPowerFlamethrowerLevel=3", "m_giftCard": "", "m_dollarCost": 11760, "m_factionCost": 50, "m_icon": "ResearchHandPowerFlamethower", "m_title": "Flame of Fire Level 3", "m_description": "TODO: Touch Many More enemies", "m_position": "(701.00, -392.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:35", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Flame of Fire Level 3", "m_newDescription": "The night sky lights up as the lizard yawns and sighs, bringing eternal sleep to all those under its fiery gaze. ", "m_newDetails": "The flame burns hotter and longer.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Flamethrower3].m_baseDamage\nAttack Width:MAHandPowerInfo[Flamethrower3].m_attackWidth\nAttack Length:MAHandPowerInfo[Flamethrower3].m_attackLength\nMan Cost:MAHandPowerInfo[Flamethrower3].m_manaCost\nCooldown:MAHandPowerInfo[Flamethrower3].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Whip crack precision with a scorched earth effect…", "m_newWarning": "Fire is indiscriminate. All are willing recipients of her love…", "m_newLore": ""}, {"id": "6798fb54aa5b9d02aa02892c", "m_key": "Mystic::HandPowersFlameThrowerL4", "m_name": "HandPowersFlameThrowerL4", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersFlameThrowerL5", "m_unlock": "m_handPowerFlamethrowerLevel=4", "m_giftCard": "", "m_dollarCost": 15000, "m_factionCost": 75, "m_icon": "ResearchHandPowerFlamethower", "m_title": "Flame of Fire Level 4", "m_description": "TODO: Touch Many More enemies", "m_position": "(778.00, -536.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:35", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Flame of Fire Level 4", "m_newDescription": "TODO: Touch Many More enemies", "m_newDetails": "A flame as hot as the earth's core.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Flamethrower4].m_baseDamage\nAttack Width:MAHandPowerInfo[Flamethrower4].m_attackWidth\nAttack Length:MAHandPowerInfo[Flamethrower4].m_attackLength\nMan Cost:MAHandPowerInfo[Flamethrower4].m_manaCost\nCooldown:MAHandPowerInfo[Flamethrower4].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Whip crack precision with a scorched earth effect…", "m_newWarning": "Fire is indiscriminate. All are willing recipients of her love…", "m_newLore": ""}, {"id": "6798fb5bce1cf20302d4b53d", "m_key": "Mystic::HandPowersFlameThrowerL5", "m_name": "HandPowersFlameThrowerL5", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerFlamethrowerLevel=5", "m_giftCard": "", "m_dollarCost": 20000, "m_factionCost": 125, "m_icon": "ResearchHandPowerFlamethower", "m_title": "Flame of Fire Level 5", "m_description": "TODO: Touch Many More enemies", "m_position": "(968.00, -668.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:36", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Flame of Fire Level 5", "m_newDescription": "TODO: Touch Many More enemies", "m_newDetails": "This power is so great, it is akin to holding a volcano in your hand and squeezing.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Flamethrower5].m_baseDamage\nAttack Width:MAHandPowerInfo[Flamethrower5].m_attackWidth\nAttack Length:MAHandPowerInfo[Flamethrower5].m_attackLength\nMan Cost:MAHandPowerInfo[Flamethrower5].m_manaCost\nCooldown:MAHandPowerInfo[Flamethrower5].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Whip crack precision with a scorched earth effect…", "m_newWarning": "Fire is indiscriminate. All are willing recipients of her love…", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b042ff", "m_key": "Mystic::HandPowersLightningL1", "m_name": "HandPowersLightningL1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersLightningL2", "m_unlock": "UnlockLightning=true", "m_giftCard": "", "m_dollarCost": 200, "m_factionCost": 0, "m_icon": "ResearchHandPowerLightning", "m_title": "<PERSON> Bolts", "m_description": "Fire an earth scorching <b>Bolt of Lightning</b> from your fingertip!", "m_position": "(344.64, 91.90, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:36", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "<PERSON> Bolts", "m_newDescription": "Fire an earth scorching <b>Bolt of Lightning</b> from your fingertip!", "m_newDetails": "Split the skies apart, rend the heavens open!", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Lightning1].m_baseDamage\nChains:MAHandPowerInfo[Lightning1].m_numOfHits\nBonus Attack Chance:MAHandPowerInfo[Lightning1].m_bonusAttackChance\nManna Cost:MAHandPowerInfo[Lightning1].m_manaCost\nCooldown:MAHandPowerInfo[Lightning1].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Distance yourself from the target…", "m_newWarning": "If the power has no target, it will find its own victim…", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b04304", "m_key": "Mystic::HandPowersLightningL2", "m_name": "HandPowersLightningL2", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersLightningL3", "m_unlock": "m_handPowerLightningLevel=2", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 5, "m_icon": "ResearchHandPowerLightning", "m_title": "Lightning Bolts Level 2", "m_description": "Frazzle fry friend and foe with the <b>Level 2 Bolt of Lightning</b>.", "m_position": "(560.57, 195.73, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:36", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Lightning Bolts Level 2", "m_newDescription": "Frazzle fry friend and foe with the <b>Level 2 Bolt of Lightning</b>.", "m_newDetails": "The Level 2 Lightning Bolt hits harder for longer.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Lightning2].m_baseDamage\nChains:MAHandPowerInfo[Lightning2].m_numOfHits\nBonus Attack Chance:MAHandPowerInfo[Lightning2].m_bonusAttackChance\nManna Cost:MAHandPowerInfo[Lightning2].m_manaCost\nCooldown:MAHandPowerInfo[Lightning2].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Distance yourself from the target…", "m_newWarning": "If the power has no target, it will find its own victim…", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b04309", "m_key": "Mystic::HandPowersLightningL3", "m_name": "HandPowersLightningL3", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersLightningL4", "m_unlock": "m_handPowerLightningLevel=3", "m_giftCard": "", "m_dollarCost": 2500, "m_factionCost": 50, "m_icon": "ResearchHandPowerLightning", "m_title": "Lightning Bolts Level 3", "m_description": "Level the playing field with the <b>Level 3 Bolt of Lightning</b>.", "m_position": "(766.03, 292.34, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:37", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Lightning Bolts Level 3", "m_newDescription": "Level the playing field with the <b>Level 3 Bolt of Lightning</b>.", "m_newDetails": "Thrust your electrical tendrils deep into the enemy skull, burn out all those synapses.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Lightning3].m_baseDamage\nChains:MAHandPowerInfo[Lightning3].m_numOfHits\nBonus Attack Chance:MAHandPowerInfo[Lightning3].m_bonusAttackChance\nManna Cost:MAHandPowerInfo[Lightning3].m_manaCost\nCooldown:MAHandPowerInfo[Lightning3].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Distance yourself from the target…", "m_newWarning": "If the power has no target, it will find its own victim…", "m_newLore": ""}, {"id": "6798fb7b0d763303141dc3dd", "m_key": "Mystic::HandPowersLightningL4", "m_name": "HandPowersLightningL4", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowersLightningL5", "m_unlock": "m_handPowerLightningLevel=4", "m_giftCard": "", "m_dollarCost": 5000, "m_factionCost": 75, "m_icon": "ResearchHandPowerLightning", "m_title": "Lightning Bolts Level 4", "m_description": "Level the playing field with the <b>Level 4 Bolt of Lightning</b>.", "m_position": "(982.03, 419.34, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:37", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Lightning Bolts Level 4", "m_newDescription": "Level the playing field with the <b>Level 4 Bolt of Lightning</b>.", "m_newDetails": "A paralysing force of nature that cripples enemies.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Lightning4].m_baseDamage\nChains:MAHandPowerInfo[Lightning4].m_numOfHits\nBonus Attack Chance:MAHandPowerInfo[Lightning4].m_bonusAttackChance\nManna Cost:MAHandPowerInfo[Lightning4].m_manaCost\nCooldown:MAHandPowerInfo[Lightning4].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Distance yourself from the target…", "m_newWarning": "If the power has no target, it will find its own victim…", "m_newLore": ""}, {"id": "6798fb820d763303141dc3e3", "m_key": "Mystic::HandPowersLightningL5", "m_name": "HandPowersLightningL5", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerLightningLevel=5", "m_giftCard": "", "m_dollarCost": 10000, "m_factionCost": 125, "m_icon": "ResearchHandPowerLightning", "m_title": "Lightning Bolts Level 5", "m_description": "The <b>Level 5 Bolt of Lightning</b> makes even the god of thunder quake in his boots.", "m_position": "(1165.03, 298.34, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:37", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Lightning Bolts Level 5", "m_newDescription": "The <b>Level 5 Bolt of Lightning</b> makes even the god of thunder quake in his boots.", "m_newDetails": "Like pressing the reset button on all internal organs. Nothing will ever be the same again.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[Lightning5].m_baseDamage\nChains:MAHandPowerInfo[Lightning5].m_numOfHits\nBonus Attack Chance:MAHandPowerInfo[Lightning5].m_bonusAttackChance\nManna Cost:MAHandPowerInfo[Lightning5].m_manaCost\nCooldown:MAHandPowerInfo[Lightning5].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Distance yourself from the target…", "m_newWarning": "If the power has no target, it will find its own victim…", "m_newLore": ""}, {"id": "66700c76da5fdc6d5d0182ad", "m_key": "Mystic::HandPowerWaterBlobL1", "m_name": "HandPowerWaterBlobL1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterBlobL2", "m_unlock": "m_handPowerWaterBlob=true", "m_giftCard": "", "m_dollarCost": 2500, "m_factionCost": 5, "m_icon": "ResearchHandPowerWaterBlob", "m_title": "Water Blob", "m_description": "Jet stream power wash your enemies into submission!", "m_position": "(564.00, -60.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:38", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Water Blob", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "Revel in the destructive force of nature. Water that hits hard as stone.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[WaterBlob1].m_baseDamage\nMin Damage:MAHandPowerInfo[WaterBlob1].m_minDamage\nNo of Hits:MAHandPowerInfo[WaterBlob1].m_numOfHits\nRadius:MAHandPowerInfo[WaterBlob1].m_aoeRadius\nMana Cost:MAHandPowerInfo[WaterBlob1].m_manaCost", "m_newUsage": "Combat", "m_newHint": "Literal splash damage available right here…", "m_newWarning": "Careful who you get wet.", "m_newLore": ""}, {"id": "66700c76da5fdc6d5d0182b2", "m_key": "Mystic::HandPowerWaterBlobL2", "m_name": "HandPowerWaterBlobL2", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterBlobL3", "m_unlock": "m_handPowerWaterBlobLevel=2", "m_giftCard": "", "m_dollarCost": 3500, "m_factionCost": 25, "m_icon": "ResearchHandPowerWaterBlob", "m_title": "Water Blob Level 2", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(790.00, -119.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:38", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Water Blob Level 2", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "Increase the bulk of your blobs.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[WaterBlob2].m_baseDamage\nMin Damage:MAHandPowerInfo[WaterBlob2].m_minDamage\nNo of Hits:MAHandPowerInfo[WaterBlob2].m_numOfHits\nRadius:MAHandPowerInfo[WaterBlob2].m_aoeRadius\nMana Cost:MAHandPowerInfo[WaterBlob2].m_manaCost", "m_newUsage": "Combat", "m_newHint": "Literal splash damage available right here…", "m_newWarning": "Careful who you get wet.", "m_newLore": ""}, {"id": "66700c76da5fdc6d5d0182b7", "m_key": "Mystic::HandPowerWaterBlobL3", "m_name": "HandPowerWaterBlobL3", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterBlobL4", "m_unlock": "m_handPowerWaterBlobLevel=3", "m_giftCard": "", "m_dollarCost": 4900, "m_factionCost": 50, "m_icon": "ResearchHandPowerWaterBlob", "m_title": "Water Blob Level 3", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(934.00, -237.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:38", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Water Blob Level 3", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "Make the water heavy.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[WaterBlob3].m_baseDamage\nMin Damage:MAHandPowerInfo[WaterBlob3].m_minDamage\nNo of Hits:MAHandPowerInfo[WaterBlob3].m_numOfHits\nRadius:MAHandPowerInfo[WaterBlob3].m_aoeRadius\nMana Cost:MAHandPowerInfo[WaterBlob3].m_manaCost", "m_newUsage": "Combat", "m_newHint": "Literal splash damage available right here…", "m_newWarning": "Careful who you get wet.", "m_newLore": ""}, {"id": "6798fb92b090ee02ca13dba5", "m_key": "Mystic::HandPowerWaterBlobL4", "m_name": "HandPowerWaterBlobL4", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterBlobL5", "m_unlock": "m_handPowerWaterBlobLevel=4", "m_giftCard": "", "m_dollarCost": 7800, "m_factionCost": 75, "m_icon": "ResearchHandPowerWaterBlob", "m_title": "Water Blob Level 4", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(1192.00, -167.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:39", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Water Blob Level 4", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "Each blob forces itself into the smallest of nooks and crevasses of your enemies' bodies.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[WaterBlob4].m_baseDamage\nMin Damage:MAHandPowerInfo[WaterBlob4].m_minDamage\nNo of Hits:MAHandPowerInfo[WaterBlob4].m_numOfHits\nRadius:MAHandPowerInfo[WaterBlob4].m_aoeRadius\nMana Cost:MAHandPowerInfo[WaterBlob4].m_manaCost", "m_newUsage": "Combat", "m_newHint": "Literal splash damage available right here…", "m_newWarning": "Careful who you get wet.", "m_newLore": ""}, {"id": "6798fb99b090ee02ca13dbbf", "m_key": "Mystic::HandPowerWaterBlobL5", "m_name": "HandPowerWaterBlobL5", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerWaterBlobLevel=5", "m_giftCard": "", "m_dollarCost": 12000, "m_factionCost": 125, "m_icon": "ResearchHandPowerWaterBlob", "m_title": "Water Blob Level 5", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(1324.00, -313.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:39", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Water Blob Level 5", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "Like dropping the Atlantic Ocean onto your head.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[WaterBlob5].m_baseDamage\nMin Damage:MAHandPowerInfo[WaterBlob5].m_minDamage\nNo of Hits:MAHandPowerInfo[WaterBlob5].m_numOfHits\nRadius:MAHandPowerInfo[WaterBlob5].m_aoeRadius\nMana Cost:MAHandPowerInfo[WaterBlob5].m_manaCost", "m_newUsage": "Combat", "m_newHint": "Literal splash damage available right here…", "m_newWarning": "Careful who you get wet.", "m_newLore": ""}, {"id": "66700c76da5fdc6d5d01829e", "m_key": "Mystic::HandPowerWaterSpoutL1", "m_name": "HandPowerWaterSpoutL1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterSpoutL2", "m_unlock": "m_handPowerWaterSpout=true", "m_giftCard": "", "m_dollarCost": 2000, "m_factionCost": 5, "m_icon": "ResearchHandPowerWaterSpout", "m_title": "Water Spout", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(124.00, -246.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:39", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Water Spout", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "Turn on the tap and let it wash away the sins of the undead.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[WaterSpout1].m_baseDamage\nAttack Width:MAHandPowerInfo[WaterSpout1].m_attackWidth\nAttack Length:MAHandPowerInfo[WaterSpout1].m_attackLength\nMan Cost:MAHandPowerInfo[WaterSpout1].m_manaCost\nCooldown:MAHandPowerInfo[WaterSpout1].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Spray and pray…", "m_newWarning": "The flotsam and the jetsam will wash away just the same.", "m_newLore": ""}, {"id": "66700c76da5fdc6d5d0182a3", "m_key": "Mystic::HandPowerWaterSpoutL2", "m_name": "HandPowerWaterSpoutL2", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterSpoutL3", "m_unlock": "m_handPowerWaterSpoutLevel=2", "m_giftCard": "", "m_dollarCost": 2800, "m_factionCost": 10, "m_icon": "ResearchHandPowerWaterSpout", "m_title": "Water Spout Level 2", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(31.00, -359.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:39", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Water Spout Level 2", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "Increase the strength of your jets to power your enemies back to hell.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[WaterSpout2].m_baseDamage\nAttack Width:MAHandPowerInfo[WaterSpout2].m_attackWidth\nAttack Length:MAHandPowerInfo[WaterSpout2].m_attackLength\nMan Cost:MAHandPowerInfo[WaterSpout2].m_manaCost\nCooldown:MAHandPowerInfo[WaterSpout2].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Spray and pray…", "m_newWarning": "The flotsam and the jetsam will wash away just the same.", "m_newLore": ""}, {"id": "66700c76da5fdc6d5d0182a8", "m_key": "Mystic::HandPowerWaterSpoutL3", "m_name": "HandPowerWaterSpoutL3", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterSpoutL4", "m_unlock": "m_handPowerWaterSpoutLevel=3", "m_giftCard": "", "m_dollarCost": 3920, "m_factionCost": 20, "m_icon": "ResearchHandPowerWaterSpout", "m_title": "Water Spout Level 3", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(-124.00, -457.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:40", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Water Spout Level 3", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "<PERSON><PERSON>t through ranks of enemies with this high powered stream.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[WaterSpout3].m_baseDamage\nAttack Width:MAHandPowerInfo[WaterSpout3].m_attackWidth\nAttack Length:MAHandPowerInfo[WaterSpout3].m_attackLength\nMan Cost:MAHandPowerInfo[WaterSpout3].m_manaCost\nCooldown:MAHandPowerInfo[WaterSpout3].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Spray and pray…", "m_newWarning": "The flotsam and the jetsam will wash away just the same.", "m_newLore": ""}, {"id": "6798fbb0b090ee02ca13dc47", "m_key": "Mystic::HandPowerWaterSpoutL4", "m_name": "HandPowerWaterSpoutL4", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::HandPowerWaterSpoutL5", "m_unlock": "m_handPowerWaterSpoutLevel=4", "m_giftCard": "", "m_dollarCost": 5000, "m_factionCost": 50, "m_icon": "ResearchHandPowerWaterSpout", "m_title": "Water Spout Level 4", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(-277.00, -569.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:40", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Water Spout Level 4", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "Blast limbs off from 50 paces with this increased pressure spout.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[WaterSpout4].m_baseDamage\nAttack Width:MAHandPowerInfo[WaterSpout4].m_attackWidth\nAttack Length:MAHandPowerInfo[WaterSpout4].m_attackLength\nMan Cost:MAHandPowerInfo[WaterSpout4].m_manaCost\nCooldown:MAHandPowerInfo[WaterSpout4].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Spray and pray…", "m_newWarning": "The flotsam and the jetsam will wash away just the same.", "m_newLore": ""}, {"id": "6798fbb7aa5b9d02aa028ba2", "m_key": "Mystic::HandPowerWaterSpoutL5", "m_name": "HandPowerWaterSpoutL5", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerWaterSpoutLevel=5", "m_giftCard": "", "m_dollarCost": 9000, "m_factionCost": 75, "m_icon": "ResearchHandPowerWaterSpout", "m_title": "Water Spout Level 5", "m_description": "TODO:  water is useful as a weapon and to put out stuff", "m_position": "(-501.00, -485.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:40", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Water Spout Level 5", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "Unleash a tsunami of doom.", "m_newDetailsLine": "Base Damage:MAHandPowerInfo[WaterSpout5].m_baseDamage\nAttack Width:MAHandPowerInfo[WaterSpout5].m_attackWidth\nAttack Length:MAHandPowerInfo[WaterSpout5].m_attackLength\nMan Cost:MAHandPowerInfo[WaterSpout5].m_manaCost\nCooldown:MAHandPowerInfo[WaterSpout5].m_baseCooldownTime", "m_newUsage": "Combat", "m_newHint": "Spray and pray…", "m_newWarning": "The flotsam and the jetsam will wash away just the same.", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b0428b", "m_key": "Commoners::Heroes", "m_name": "Heroes", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::Heroes<PERSON><PERSON>|Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 0, "m_factionCost": 0, "m_icon": "ResearchPickup", "m_title": "Heroes", "m_description": "They're rough, they're tough, they'll tear your head off. <b>Heroes</b>!", "m_position": "(214.56, 157.32, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:10", "m_activatedAtStart": "True", "m_isLocked": "False", "m_newDisplayName": "Heroes", "m_newDescription": "They're rough, they're tough, they'll tear your head off. <b>Heroes</b>!", "m_newDetails": "Masters of combat, heroes come in all shapes and sizes, but each and every one of them is a killing machine of the utmost skill.", "m_newDetailsLine": "", "m_newUsage": "Combat & Defence", "m_newHint": "Master the Hero, Master the horror…", "m_newWarning": "Do not put all your eggs into one basket!", "m_newLore": ""}, {"id": "670cff252fb7f9030fcee0ff", "m_key": "Commoners::HeroesGuild", "m_name": "HeroesGuild", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "BuildHeroGuild=true", "m_giftCard": "", "m_dollarCost": 40, "m_factionCost": 2, "m_icon": "ResearchPickup", "m_title": "Heroes Guild", "m_description": "With this power you will be gifted the ability to build a Hero Guild. The Guild acts as a home for any hero you hire, giving them a base for all of their operations. ", "m_position": "(73.00, 355.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:10", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Heroes Guild", "m_newDescription": "With this power you will be gifted the ability to build a Hero Guild.", "m_newDetails": "The Guild acts as a home for any hero you hire, giving them a base for all of their operations. Upgrades to add include fast healing and Exp Training.", "m_newDetailsLine": "Heal Multiplier:MAComponentInfo[ActionHeroesGuild].GetFieldEntry(m_healMultiplier)\nTraining Multiplier:MAComponentInfo[ActionHeroesGuild].GetFieldEntry(m_trailMultiplier)", "m_newUsage": "Heroes", "m_newHint": "Find the rest of this block set…", "m_newWarning": "When is a hero not a hero?", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b042eb", "m_key": "Mystic::<PERSON><PERSON><PERSON><PERSON>", "m_name": "HitZombie", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerHitZombie=true", "m_giftCard": "", "m_dollarCost": 5000, "m_factionCost": 35, "m_icon": "ResearchPickup", "m_title": "Zombie Flick", "m_description": "Line them up like dominoes, watch them fall like flies. <b>Flick Zombie</b> power enabled!", "m_position": "(-197.99, 7.29, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:41", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Zombie Flick", "m_newDescription": "<b>Available during Early Access updates</b>\nLine them up like dominoes, watch them fall like flies. <b>Flick Zombie</b> power enabled!", "m_newDetails": "Placing the ability to flick low level zombies away from you is akin to giving the keys to the city of Lilliput to <PERSON><PERSON><PERSON> and telling him to do his worst.", "m_newDetailsLine": "", "m_newUsage": "Toying with Zombies", "m_newHint": "Flick, don't pick.", "m_newWarning": "Careful where you flick them…", "m_newLore": ""}, {"id": "66e465e45d2a8102ba3319ca", "m_key": "Royal::IronMace", "m_name": "IronMace", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 245, "m_factionCost": 3, "m_icon": "ResearchPaints", "m_title": "<PERSON><PERSON><PERSON> Finder", "m_description": "A precision mace. Will crush a windpipe with ease.", "m_position": "(58.00, -150.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:46", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "<PERSON><PERSON><PERSON> Finder", "m_newDescription": "<b>Available during Early Access updates</b>\nA precision mace. Will crush a windpipe with ease.", "m_newDetails": "The Throat Finder gets straight to the point without a sharp edge on it. The point? Crushing windpipes, not taking no for an answer.", "m_newDetailsLine": "", "m_newUsage": "Combat", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670e4e41cb288b0318c648b6", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Journeyman <PERSON>", "m_description": "This one's been round the fields more than once.", "m_position": "(-130.00, -567.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:11", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Journeyman <PERSON>", "m_newDescription": "<b>Available during Early Access updates</b>\nThis one's been round the fields more than once.", "m_newDetails": "This one's been round the fields more than once. Of marked improvement over a common worker, the Journeyman Farmer works longer, faster, harder.", "m_newDetailsLine": "", "m_newUsage": "Farm", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670e4a13bd7c8c02d3f35cd0", "m_key": "Commoners::Journeyman<PERSON><PERSON>ber<PERSON>", "m_name": "JourneymanLumberjack", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::Master<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Journeyman <PERSON>", "m_description": "Three fellas in one.", "m_position": "(620.00, -471.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:11", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Journeyman <PERSON>", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "A broad back and good, strong, long arms. The Journeyman Lumberjack will upgrade your wood gathering teams with their tree felling gifts.", "m_newDetailsLine": "", "m_newUsage": "Lumber Mill", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670e4e40cb288b0318c6488d", "m_key": "Commoners::Journeyman<PERSON>iller", "m_name": "JourneymanMiller", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Journey<PERSON>", "m_description": "You'd be mad to sack this one off. A hardened miller with more experience at the grind stone than any other.", "m_position": "(-259.00, -453.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:11", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Journey<PERSON>", "m_newDescription": "<b>Available during Early Access updates</b>\nYou'd be mad to sack this one off. A hardened miller with more experience at the grind stone than any other.", "m_newDetails": "You'd be mad to sack this one off. A hardened miller with more experience at the grind stone than any other.", "m_newDetailsLine": "", "m_newUsage": "Mill", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "66e465e372ce6202c016cf51", "m_key": "Royal::LeadMace", "m_name": "LeadMace", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::IronMace|Royal::SteelMace", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 180, "m_factionCost": 3, "m_icon": "ResearchStickers", "m_title": "Lead Mace", "m_description": "Heavy in the hands, easy to drop. Deadly when it connects.", "m_position": "(218.00, -20.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:46", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Lead Mace", "m_newDescription": "<b>Available during Early Access updates</b>\nHeavy in the hands, easy to drop. Deadly when it connects.", "m_newDetails": "Though made from a malleable metal like lead, this mace will reshape an enemy skull with consummate ease, and if it loses a little of its original shape, it means it's done its job to an exceptional standard.", "m_newDetailsLine": "", "m_newUsage": "Combat", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "66e465e4c8409802f4200981", "m_key": "Royal::<PERSON><PERSON><PERSON><PERSON>", "m_name": "LegendaryMace", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 600, "m_factionCost": 8, "m_icon": "ResearchStickers", "m_title": "The Face of Doom", "m_description": "A mace that needs no introduction because once you meet it, you're dead.", "m_position": "(252.00, -322.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:47", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "The Face of Doom", "m_newDescription": "<b>Available during Early Access updates</b>\nA mace that needs no introduction because once you meet it, you're dead.", "m_newDetails": "A brutal, bludgeoning weapon that is most at home in the hands of a fierce powerhouse of a warrior.", "m_newDetailsLine": "", "m_newUsage": "Combat", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670e4a13cb288b0318c557df", "m_key": "Commoners::<PERSON><PERSON><PERSON>", "m_name": "Lumber<PERSON>", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::Journeyman<PERSON><PERSON>ber<PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Lumber<PERSON>", "m_description": "tbd.", "m_position": "(499.00, -378.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:12", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Lumber<PERSON>", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "A master of woodwork, able to cut down trees like a beaver. With this worker in your team, you can't see the wood for the fact that they're all cut down.", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6808b1fc7a122102c939ed10", "m_key": "Mystic::ManaIncrease1", "m_name": "ManaIncrease1", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::ManaIncrease2", "m_unlock": "m_manaStorageMultiplier= 1.25", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 0, "m_icon": "ResearchPickup", "m_title": "Mana Increase 1", "m_description": "Increase your Mana Pool by 25%.", "m_position": "(-94.00, -107.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:41", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Mana Increase 1", "m_newDescription": "Increase your Mana Pool by 10%.", "m_newDetails": "Increase your Mana Pool by 10%.", "m_newDetailsLine": "", "m_newUsage": "Hand Powers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6808b22b61196602d27d0356", "m_key": "Mystic::ManaIncrease10", "m_name": "ManaIncrease10", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_manaStorageMultiplier= 5", "m_giftCard": "", "m_dollarCost": 15000, "m_factionCost": 350, "m_icon": "ResearchPickup", "m_title": "Mana Increase 10", "m_description": "Increase your Mana Pool by 400%.", "m_position": "(-1097.00, 16.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:41", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Mana Increase 10", "m_newDescription": "Increase your Mana Pool by 200%.", "m_newDetails": "Increase your Mana Pool by 200%.", "m_newDetailsLine": "", "m_newUsage": "Hand Powers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6808b200eca77c02fd1f4d83", "m_key": "Mystic::ManaIncrease2", "m_name": "ManaIncrease2", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::ManaIncrease3", "m_unlock": "m_manaStorageMultiplier= 1.5", "m_giftCard": "", "m_dollarCost": 350, "m_factionCost": 10, "m_icon": "ResearchPickup", "m_title": "Mana Increase 2", "m_description": "Increase your Mana Pool by 50%.", "m_position": "(-302.00, -186.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:42", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Mana Increase 2", "m_newDescription": "Increase your Mana Pool by 25%.", "m_newDetails": "Increase your Mana Pool by 25%.", "m_newDetailsLine": "", "m_newUsage": "Hand Powers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6808b203eca77c02fd1f4d92", "m_key": "Mystic::ManaIncrease3", "m_name": "ManaIncrease3", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::ManaIncrease4", "m_unlock": "m_manaStorageMultiplier= 2", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Mana Increase 3", "m_description": "Increase your Mana Pool by 100%.", "m_position": "(-413.00, -70.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:42", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Mana Increase 3", "m_newDescription": "Increase your Mana Pool by 50%.", "m_newDetails": "Increase your Mana Pool by 50%.", "m_newDetailsLine": "", "m_newUsage": "Hand Powers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6808b20a7a122102c939ed72", "m_key": "Mystic::ManaIncrease4", "m_name": "ManaIncrease4", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::<PERSON>aIncrease5", "m_unlock": "m_manaStorageMultiplier= 2.25", "m_giftCard": "", "m_dollarCost": 750, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Mana Increase 4", "m_description": "Increase your Mana Pool by 125%.", "m_position": "(-548.00, -244.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:42", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Mana Increase 4", "m_newDescription": "Increase your Mana Pool by 75%.", "m_newDetails": "Increase your Mana Pool by 75%.", "m_newDetailsLine": "", "m_newUsage": "Hand Powers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6808b20eeca77c02fd1f4d98", "m_key": "Mystic::<PERSON>aIncrease5", "m_name": "ManaIncrease5", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::<PERSON><PERSON>I<PERSON><PERSON>6", "m_unlock": "m_manaStorageMultiplier= 2.5", "m_giftCard": "", "m_dollarCost": 1250, "m_factionCost": 50, "m_icon": "ResearchPickup", "m_title": "Mana Increase 5", "m_description": "Increase your Mana Pool by 150%.", "m_position": "(-725.00, -159.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:43", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Mana Increase 5", "m_newDescription": "Increase your Mana Pool by 100%.", "m_newDetails": "Increase your Mana Pool by 100%.", "m_newDetailsLine": "", "m_newUsage": "Hand Powers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6808b21361196602d27d02c0", "m_key": "Mystic::<PERSON><PERSON>I<PERSON><PERSON>6", "m_name": "ManaIncrease6", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::<PERSON><PERSON>Increase7", "m_unlock": "m_manaStorageMultiplier= 3", "m_giftCard": "", "m_dollarCost": 1800, "m_factionCost": 80, "m_icon": "ResearchPickup", "m_title": "Mana Increase 6", "m_description": "Increase your Mana Pool by 200%.", "m_position": "(-744.00, -318.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:43", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Mana Increase 6", "m_newDescription": "Increase your Mana Pool by 110%.", "m_newDetails": "Increase your Mana Pool by 110%.", "m_newDetailsLine": "", "m_newUsage": "Hand Powers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6808b217d86acd02f461198e", "m_key": "Mystic::<PERSON><PERSON>Increase7", "m_name": "ManaIncrease7", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::<PERSON><PERSON><PERSON><PERSON><PERSON>8", "m_unlock": "m_manaStorageMultiplier= 3.5", "m_giftCard": "", "m_dollarCost": 2600, "m_factionCost": 100, "m_icon": "ResearchPickup", "m_title": "Mana Increase 7", "m_description": "Increase your Mana Pool by 250%.", "m_position": "(-938.00, -83.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:44", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Mana Increase 7", "m_newDescription": "Increase your Mana Pool by 125%.", "m_newDetails": "Increase your Mana Pool by 125%.", "m_newDetailsLine": "", "m_newUsage": "Hand Powers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6808b21bd86acd02f4611994", "m_key": "Mystic::<PERSON><PERSON><PERSON><PERSON><PERSON>8", "m_name": "ManaIncrease8", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::<PERSON><PERSON>I<PERSON><PERSON>9", "m_unlock": "m_manaStorageMultiplier= 4", "m_giftCard": "", "m_dollarCost": 5000, "m_factionCost": 150, "m_icon": "ResearchPickup", "m_title": "Mana Increase 8", "m_description": "Increase your Mana Pool by 300%.", "m_position": "(-1004.00, -271.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:44", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Mana Increase 8", "m_newDescription": "Increase your Mana Pool by 150%.", "m_newDetails": "Increase your Mana Pool by 150%.", "m_newDetailsLine": "", "m_newUsage": "Hand Powers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6808b21f61196602d27d0310", "m_key": "Mystic::<PERSON><PERSON>I<PERSON><PERSON>9", "m_name": "ManaIncrease9", "m_index": "", "m_factionType": "Mystic", "m_linkTo": "", "m_linkToString": "Mystic::ManaIncrease10", "m_unlock": "m_manaStorageMultiplier= 4.5", "m_giftCard": "", "m_dollarCost": 8000, "m_factionCost": 250, "m_icon": "ResearchPickup", "m_title": "Mana Increase 9", "m_description": "Increase your Mana Pool by 350%.", "m_position": "(-1172.00, -146.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:44", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Mana Increase 9", "m_newDescription": "Increase your Mana Pool by 175%.", "m_newDetails": "Increase your Mana Pool by 175%.", "m_newDetailsLine": "", "m_newUsage": "Hand Powers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670e4e40cb288b0318c64878", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_name": "<PERSON><PERSON><PERSON><PERSON>", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Master Chefs", "m_description": "tbd.", "m_position": "(-629.00, -310.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:12", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Master Chefs", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "In the highly structured kitchen, the Master Chef is top of the tea tree.", "m_newDetailsLine": "", "m_newUsage": "Factory", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670e4e42cb288b0318c648cb", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_name": "Master<PERSON>armer", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Master Farmer", "m_description": "tbd.", "m_position": "(-377.00, -618.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:12", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Master Farmer", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "Steeped in agricultural knowhow, the Master Farmer has green fingers, thumbs, hands, arms, and beyond.", "m_newDetailsLine": "", "m_newUsage": "Farm", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670e4a144e9c4302c41e7e47", "m_key": "Commoners::Master<PERSON><PERSON><PERSON><PERSON>", "m_name": "MasterLumberjack", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Master <PERSON><PERSON><PERSON>", "m_description": "tbd.", "m_position": "(798.00, -376.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:13", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Master <PERSON><PERSON><PERSON>", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "Able to make tree felling look so easy the axe appears like a knife through a butter tree.", "m_newDetailsLine": "", "m_newUsage": "Lumber Mill", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670e4e41cb288b0318c6489e", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_name": "Master<PERSON><PERSON><PERSON>", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 25, "m_icon": "ResearchPickup", "m_title": "Master <PERSON>", "m_description": "tbd.", "m_position": "(-518.00, -517.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:13", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Master <PERSON>", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "So skilled with the grindstone, the Master <PERSON> leaves his competitors behind. Eat my flour!", "m_newDetailsLine": "", "m_newUsage": "Mill", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "66e465e272ce6202c016cf3b", "m_key": "Royal::MediocreAxe", "m_name": "MediocreAxe", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::CastleGuardAxe", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 220, "m_factionCost": 3, "m_icon": "ResearchPaints", "m_title": " <PERSON><PERSON><PERSON>'s Hatchet", "m_description": "Will bite through leather armour like a hammer through lard.", "m_position": "(509.00, 95.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:47", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": " <PERSON><PERSON><PERSON>'s Hatchet", "m_newDescription": "<b>Available during Early Access updates</b>\nWill bite through leather armour like a hammer through lard.", "m_newDetails": "Will bite through leather armour like a hammer through lard.", "m_newDetailsLine": "", "m_newUsage": "Combat", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670e4e3e8e639602c429c08d", "m_key": "Commoners::<PERSON>", "m_name": "<PERSON>", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::Journeyman<PERSON>iller", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "<PERSON>", "m_description": "tbd.", "m_position": "(10.00, -386.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:13", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "<PERSON>", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "A step above the common worker, the Miller should be put to work in the Mill.", "m_newDetailsLine": "", "m_newUsage": "Mill", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "66d1ae1801512b02d9a475be", "m_key": "Commoners::PickupDecorations", "m_name": "PickupDecorations", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 13, "m_icon": "ResearchPickup", "m_title": "Pickup Decorations", "m_description": "tbd.", "m_position": "(975.92, 8.92, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:14", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Pickup Decorations", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b04277", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "PickupLogs", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::PickupDecorations|Commoners::PickupTree", "m_unlock": "m_handPowerPickupLogs=true", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Pickup Logs", "m_description": "Wield freshly felled trees like they were toothpicks with the power to <b>Pickup Logs</b>!", "m_position": "(735.57, 97.64, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:14", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Pickup Logs", "m_newDescription": "<b>Available during Early Access updates</b>\nWield freshly felled trees like they were toothpicks with the power to <b>Pickup Logs</b>!", "m_newDetails": "Assist your lumberjacks, add the Pickup Logs to your powers and carry freshly felled trees with ease.", "m_newDetailsLine": "", "m_newUsage": "Resources: Wood", "m_newHint": "Build the wall, keep them out.", "m_newWarning": "No wall stays standing forever...", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b04281", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_name": "PickupOre", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerPickupOre=true", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Pickup Metal Ore", "m_description": "Straight from the seam the ore it comes. Don't bother your bods, put your hand where the hole is and <b>Pickup <PERSON>e</b> straight from the Metal Mine!", "m_position": "(616.96, -83.71, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:14", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Pickup Metal Ore", "m_newDescription": "<b>Available during Early Access updates</b>\nStraight from the seam the ore it comes. Don't bother your bods, put your hand where the hole is and <b>Pickup Ore</b> straight from the Metal Mine!", "m_newDetails": "Aide your miners by adding this hand power to your collection. Drag iron ore straight from the seam.", "m_newDetailsLine": "", "m_newUsage": "Resources: Metal", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b04272", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "PickupProduce", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_handPowerPickupProduce=true", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Pickup Produce", "m_description": "Enjoy the pleasure of grabbing and dragging from the Mill using the <b>PickUp Produce</b> hand power!", "m_position": "(546.49, -173.20, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:15", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Pickup Produce", "m_newDescription": "<b>Available during Early Access updates</b>\nEnjoy the pleasure of grabbing and dragging from the Mill using the <b>PickUp Produce</b> hand power!", "m_newDetails": "Keep the manufacturing team supplied, use this power to manually move prepared ingredients from the Mill to the Factory.", "m_newDetailsLine": "", "m_newUsage": "Mill", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670e504ccb288b0318c67fee", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON>", "m_name": "PickupTree", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 7, "m_icon": "ResearchPickup", "m_title": "Pickup Tree", "m_description": "tbd.", "m_position": "(1263.00, 134.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:15", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Pickup Tree", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "A simple premise, you see a tree, you want it, you pick it up, it's yours.", "m_newDetailsLine": "", "m_newUsage": "Hand Powers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670cff242fb8e202f7cc4fe1", "m_key": "Commoners::PiesAndTartsPack", "m_name": "PiesAndTartsPack", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "MA_RL_PiesAndTartsPack", "m_dollarCost": 250, "m_factionCost": 15, "m_icon": "ResearchPickup", "m_title": "Pies & Tarts Pack", "m_description": "tbd.", "m_position": "(-452.00, -210.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:16", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Pies & Tarts Pack", "m_newDescription": "tbd.", "m_newDetails": "With the addition of this Pie pack, you gain both sweet and savoury products to boost your sales and improve the quality of your fare.", "m_newDetailsLine": "Part:NGBusinessGift[MA_RL_PiesAndTartsPack].GetCardPowerParts", "m_newUsage": "Production & Manufacturing", "m_newHint": "Keep an eye on the sales price for each part.", "m_newWarning": "Beware the resource costs. Is everything as cheap as it first appears?", "m_newLore": ""}, {"id": "66e465e272ce6202c016cf30", "m_key": "Royal::PoorAxe", "m_name": "PoorAxe", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::MediocreAxe", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 150, "m_factionCost": 2, "m_icon": "ResearchStickers", "m_title": "<PERSON><PERSON>'s Axe", "m_description": "A skirmisher's weapon. Useful against bandits.", "m_position": "(287.00, 203.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:47", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "<PERSON><PERSON>'s Axe", "m_newDescription": "<b>Available during Early Access updates</b>\nA skirmisher's weapon. Useful against bandits.", "m_newDetails": "A skirmisher's weapon. Useful against bandits.", "m_newDetailsLine": "", "m_newUsage": "Combat", "m_newHint": "Does well against bandits.", "m_newWarning": "Would you give rotting flesh to your dog? Would you give weak weapons to your hero?", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b0429f", "m_key": "Lords::<PERSON><PERSON><PERSON>", "m_name": "Possess", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>|Lords::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>|Lords::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>|Lords::<PERSON>ssessWors<PERSON>pers", "m_unlock": "", "m_giftCard": "", "m_dollarCost": "", "m_factionCost": "", "m_icon": "ResearchFactoryLevel", "m_title": "Possession", "m_description": "Grants the ability to possess other things than just heroes.", "m_position": "(149.64, -45.31, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:26", "m_activatedAtStart": "True", "m_isLocked": "False", "m_newDisplayName": "Possession", "m_newDescription": "Grants the ability to possess other things than just heroes.", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6871092d0f4e0102e2529b19", "m_key": "Lords::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "PossessAnimals", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_possessAnimals=true", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 10, "m_icon": "ResearchFactoryLevel", "m_title": "Possess Animals", "m_description": "Ability to possess Animals.", "m_position": "(-68.76, -324.72, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:29", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Possess Animals", "m_newDescription": "Ability to possess Animals.", "m_newDetails": "tbd.", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "687109350f4e0102e2529b51", "m_key": "Lords::<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "PossessTurrets", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_possessBuildings=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 15, "m_icon": "ResearchFactoryLevel", "m_title": "Possess <PERSON>", "m_description": "Ability to possess Turrets.", "m_position": "(117.77, -214.37, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:29", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Possess <PERSON>", "m_newDescription": "Ability to possess Turrets.", "m_newDetails": "tbd.", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6871094053de8402cc5381db", "m_key": "Lords::<PERSON><PERSON>s<PERSON><PERSON><PERSON>", "m_name": "PossessWorkers", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_possessWorkers=true", "m_giftCard": "", "m_dollarCost": 100, "m_factionCost": 5, "m_icon": "ResearchFactoryLevel", "m_title": "Possess Workers", "m_description": "Ability to possess Workers.", "m_position": "(347.08, -310.6, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:29", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Possess Workers", "m_newDescription": "Ability to possess Workers.", "m_newDetails": "tbd.", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6871094b0f4e0102e2529bd5", "m_key": "Lords::PossessWors<PERSON><PERSON>", "m_name": "PossessWorshippers", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_possessTourists=true", "m_giftCard": "", "m_dollarCost": 2500, "m_factionCost": 25, "m_icon": "ResearchFactoryLevel", "m_title": "Possess Worshippers", "m_description": "Ability to possess Worshippers.", "m_position": "(346.69, -161.85, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:29", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Possess Worshippers", "m_newDescription": "Ability to possess Worshippers.", "m_newDetails": "tbd.", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b04295", "m_key": "Lords::<PERSON><PERSON><PERSON><PERSON>s", "m_name": "PriceIncreases", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::FoodSalesPricePlus1|Lords::WeaponsSalesPricePlus1|Lords::<PERSON>ourSalesPricePlus1|Lords::<PERSON><PERSON><PERSON>SalesPricePlus1", "m_unlock": "", "m_giftCard": "", "m_dollarCost": "", "m_factionCost": "", "m_icon": "ResearchFactoryLevel", "m_title": "Price Increases", "m_description": "Increase the amount of money gained by your product designs.", "m_position": "(-3.9, 140.27, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:26", "m_activatedAtStart": "True", "m_isLocked": "False", "m_newDisplayName": "Price Increases", "m_newDescription": "Increase the amount of money gained by your product designs.", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "68710df653de8402cc538e5c", "m_key": "Lords::Productivity", "m_name": "Productivity", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::<PERSON><PERSON><PERSON><PERSON><PERSON>|Lords::<PERSON><PERSON><PERSON><PERSON>t1|Lords::WorkerStationL1|Lords::BedroomL1|Lords::Factory<PERSON>lockL1", "m_unlock": "", "m_giftCard": "", "m_dollarCost": "", "m_factionCost": "", "m_icon": "ResearchFactoryLevel", "m_title": "Productivity", "m_description": "Selection of boosts that boost your productivity.", "m_position": "(154.1046, 315.0677, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:26", "m_activatedAtStart": "True", "m_isLocked": "False", "m_newDisplayName": "Productivity", "m_newDescription": "Selection of boosts that boost your productivity.", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b0426d", "m_key": "Commoners::Products", "m_name": "Products", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "Commoners::<PERSON><PERSON><PERSON><PERSON>|Commoners::PiesAndTartsPack|Commoners::SlicesPack|Commoners::Chefs|Commoners::<PERSON>|Commoners::<PERSON>|Commoners::WeirdDesigns", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 0, "m_factionCost": 0, "m_icon": "ResearchPickup", "m_title": "Products", "m_description": "Add a wealth of valuable product parts to your design drawers.", "m_position": "(116.95, -118.26, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:17", "m_activatedAtStart": "True", "m_isLocked": "False", "m_newDisplayName": "Products", "m_newDescription": "Add a wealth of valuable product parts to your design drawers.", "m_newDetails": "The gateway to a multitude of valuable and unique product parts.", "m_newDetailsLine": "", "m_newUsage": "Manufacturing", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "670cff24bd7d6702b497ab2c", "m_key": "Commoners::<PERSON><PERSON><PERSON><PERSON><PERSON>", "m_name": "SlicesPack", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "", "m_giftCard": "MA_RL_SlicesPack", "m_dollarCost": 100, "m_factionCost": 3, "m_icon": "ResearchPickup", "m_title": "Slices Pack", "m_description": "<b>Available during Early Access updates</b>", "m_position": "(-219.00, -238.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:17", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Slices Pack", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "Freshen up your sandwich selection with this delicious Slices Pack.", "m_newDetailsLine": "Part:NGBusinessGift[MA_RL_SlicesPack].GetCardPowerParts", "m_newUsage": "", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "66e465e45d2a8102ba3319d7", "m_key": "Royal::SteelMace", "m_name": "SteelMace", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::<PERSON><PERSON><PERSON><PERSON>", "m_unlock": "", "m_giftCard": "", "m_dollarCost": 350, "m_factionCost": 5, "m_icon": "ResearchPaints", "m_title": "Crack of Thunder", "m_description": "You'll hear it before you feel it. And by then it's all over.", "m_position": "(410.00, -174.00, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:48", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "Crack of Thunder", "m_newDescription": "<b>Available during Early Access updates</b>\nYou'll hear it before you feel it. And by then it's all over.", "m_newDetails": "If it were possible to imprison the lightning of your hand, this mace would be its cell.", "m_newDetailsLine": "", "m_newUsage": "Combat", "m_newHint": "Swing it as you like, let the mace do the work.", "m_newWarning": "It takes no prisoners…", "m_newLore": ""}, {"id": "673c748fbb0eb002c7e4e935", "m_key": "Commoners::<PERSON>lockBuildWall", "m_name": "UnlockBuildWall", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_designWalls = true", "m_giftCard": "", "m_dollarCost": 70, "m_factionCost": 2, "m_icon": "ResearchPickup", "m_title": "Wall Building", "m_description": "Unlock the ability to place and build walls. Walls are an excellent form of defence against the horrors of the night, use them to block and slow down your enemies, buying time for your townsfolk and heroes to prepare.", "m_position": "(704.58, 2.63, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:19", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Wall Building", "m_newDescription": "Unlock the ability to place and build walls.", "m_newDetails": "Walls are an excellent form of defence against the horrors of the night, use them to block and slow down your enemies, buying time for your townsfolk and heroes to prepare. Requires Wood!", "m_newDetailsLine": "", "m_newUsage": "Defence", "m_newHint": "Keep the walls in good repair.", "m_newWarning": "A weakened wall is no wall at all.", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b04318", "m_key": "Royal::<PERSON><PERSON>", "m_name": "Unlock Paints", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::<PERSON><PERSON>|Royal::<PERSON><PERSON><PERSON>", "m_unlock": "m_designPaints=true", "m_giftCard": "MA_PaintStarterPack", "m_dollarCost": 900, "m_factionCost": 1, "m_icon": "ResearchPaints", "m_title": "Paints", "m_description": "Give all of your designs a unique streak with <b>Paints</b>!", "m_position": "(-532.83, 282.41, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:49", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Paints", "m_newDescription": "Give all of your designs a unique streak with <b>Paints</b>!", "m_newDetails": "Give all of your designs a unique streak with <b>Paints</b>!", "m_newDetailsLine": "", "m_newUsage": "Product Design & Construction", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b04313", "m_key": "Royal::<PERSON><PERSON>", "m_name": "<PERSON><PERSON> Pat<PERSON>s", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::<PERSON><PERSON>|Royal::PoorA<PERSON>", "m_unlock": "m_designPatterns=true", "m_giftCard": "MA_PatternStarterPack", "m_dollarCost": 1000, "m_factionCost": 2, "m_icon": "ResearchPatterns", "m_title": "Patterns", "m_description": "Bring some new looks to your patented designs with <b>Patterns</b>!", "m_position": "(-152.87, 218.22, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:49", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Patterns", "m_newDescription": "Bring some new looks to your patented designs with <b>Patterns</b>!", "m_newDetails": "Bring some new looks to your patented designs with <b>Patterns</b>!", "m_newDetailsLine": "", "m_newUsage": "Product Design & Construction", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b0431d", "m_key": "Royal::<PERSON><PERSON>", "m_name": "Unlock Stickers", "m_index": "", "m_factionType": "Royal", "m_linkTo": "", "m_linkToString": "Royal::LeadMace", "m_unlock": "m_designStickers=true", "m_giftCard": "MA_StickerStarterPack", "m_dollarCost": 300, "m_factionCost": 2, "m_icon": "ResearchStickers", "m_title": "Runes", "m_description": "Mark your manufactured goods with the awesome power of magic. <b>Runes</b>", "m_position": "(-64.20, 51.71, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:49", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Runes", "m_newDescription": "Mark your manufactured goods with the awesome power of magic. <b>Runes</b>", "m_newDetails": "Mark your manufactured goods with the awesome power of magic. <b>Runes</b>", "m_newDetailsLine": "", "m_newUsage": "Product Design & Construction", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2c6a278802e3610be7", "m_key": "Lords::WeaponsSalesPricePlus1", "m_name": "WeaponsSalesPricePlus1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::WeaponsSalesPricePlus2", "m_unlock": "m_weaponsSalesPriceMarkup= 1.05", "m_giftCard": "", "m_dollarCost": 250, "m_factionCost": 2, "m_icon": "ResearchFactoryLevel", "m_title": "Weapons Sales Price Plus", "m_description": "increase by 5%", "m_position": "(-344.75, 291.35, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:29", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Weapons Sales Price Plus", "m_newDescription": "increase by 5%", "m_newDetails": "increase by 5%", "m_newDetailsLine": "", "m_newUsage": "Sales", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2c6a278802e3610bea", "m_key": "Lords::WeaponsSalesPricePlus2", "m_name": "WeaponsSalesPricePlus2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::WeaponsSalesPricePlus3", "m_unlock": "m_weaponsSalesPriceMarkup= 1.1", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 4, "m_icon": "ResearchFactoryLevel", "m_title": "Weapons Sales Price Plus", "m_description": "increase by 10%", "m_position": "(-589.2, 328.77, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:30", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Weapons Sales Price Plus", "m_newDescription": "increase by 10%", "m_newDetails": "increase by 10%", "m_newDetailsLine": "", "m_newUsage": "Sales", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2c6a278802e3610bed", "m_key": "Lords::WeaponsSalesPricePlus3", "m_name": "WeaponsSalesPricePlus3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::WeaponsSalesPricePlus4", "m_unlock": "m_weaponsSalesPriceMarkup= 1.15", "m_giftCard": "", "m_dollarCost": 750, "m_factionCost": 6, "m_icon": "ResearchFactoryLevel", "m_title": "Weapons Sales Price Plus", "m_description": "increase by 15%", "m_position": "(-809.92, 374.58, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:30", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Weapons Sales Price Plus", "m_newDescription": "increase by 15%", "m_newDetails": "increase by 15%", "m_newDetailsLine": "", "m_newUsage": "Sales", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2c6a278802e3610bf0", "m_key": "Lords::WeaponsSalesPricePlus4", "m_name": "WeaponsSalesPricePlus4", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::WeaponsSalesPricePlus5", "m_unlock": "m_weaponsSalesPriceMarkup= 1.25", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 8, "m_icon": "ResearchFactoryLevel", "m_title": "Weapons Sales Price Plus", "m_description": "increase by 25%", "m_position": "(-989.22, 445.68, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:31", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Weapons Sales Price Plus", "m_newDescription": "increase by 25%", "m_newDetails": "increase by 25%", "m_newDetailsLine": "", "m_newUsage": "Sales", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "67d42f2d6a278802e3610bf3", "m_key": "Lords::WeaponsSalesPricePlus5", "m_name": "WeaponsSalesPricePlus5", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_weaponsSalesPriceMarkup= 1.5", "m_giftCard": "", "m_dollarCost": 1500, "m_factionCost": 10, "m_icon": "ResearchFactoryLevel", "m_title": "Weapons Sales Price Plus", "m_description": "increase by 50%", "m_position": "(-1239.61, 513.39, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:31", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Weapons Sales Price Plus", "m_newDescription": "increase by 50%", "m_newDetails": "increase by 50%", "m_newDetailsLine": "", "m_newUsage": "Sales", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "685a7833c908561a54e37090", "m_key": "Commoners::WeirdDesigns", "m_name": "WeirdDesigns", "m_index": "", "m_factionType": "Commoners", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_weirdDesigns=true", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 5, "m_icon": "ResearchPickup", "m_title": "Weird Designs", "m_description": "Combine all manner of parts from any product line or building block set in your designs and construction. For cosmetic purposes only!", "m_position": "(-287.90, -106.40, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:19", "m_activatedAtStart": "False", "m_isLocked": "False", "m_newDisplayName": "Weird Designs", "m_newDescription": "This allows you to mix any product parts or building parts in your designs. This can create some weird designs, but, hey, you're the boss. ", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "", "m_newHint": "", "m_newWarning": "This is primarily for cosmetic purposes, mixing parts and building blocks is unlikely to have any benefits.", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b042b3", "m_key": "Lords::WorkerStationL1", "m_name": "WorkerStationL1", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::WorkerStationL2", "m_unlock": "m_blockWorkerStationLevel=1", "m_giftCard": "", "m_dollarCost": 500, "m_factionCost": 1, "m_icon": "ResearchBedroomCapacity", "m_title": "WorkerStationL1", "m_description": "tbd.", "m_position": "(402.886, 431.6284, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:31", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "WorkerStationL1", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "Workers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b042b8", "m_key": "Lords::WorkerStationL2", "m_name": "WorkerStationL2", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "Lords::WorkerStationL3", "m_unlock": "m_blockWorkerStationLevel=2", "m_giftCard": "", "m_dollarCost": 1000, "m_factionCost": 2, "m_icon": "ResearchBedroomCapacity", "m_title": "WorkerStationL2", "m_description": "tbd.", "m_position": "(475.1693, 560.1492, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:32", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "WorkerStationL2", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "Workers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}, {"id": "6666ec95182bfe1fd6b042bd", "m_key": "Lords::WorkerStationL3", "m_name": "WorkerStationL3", "m_index": "", "m_factionType": "Lords", "m_linkTo": "", "m_linkToString": "", "m_unlock": "m_blockWorkerStationLevel=3", "m_giftCard": "", "m_dollarCost": 2000, "m_factionCost": 4, "m_icon": "ResearchBedroomCapacity", "m_title": "WorkerStationL3", "m_description": "tbd.", "m_position": "(574.6815, 657.76, 0.00)", "m_scale": "(1.00, 1.00, 1.00)", "m_updateTime": "2025-07-11 11:56:32", "m_activatedAtStart": "False", "m_isLocked": "True", "m_newDisplayName": "WorkerStationL3", "m_newDescription": "<b>Available during Early Access updates</b>", "m_newDetails": "", "m_newDetailsLine": "", "m_newUsage": "Workers", "m_newHint": "", "m_newWarning": "", "m_newLore": ""}]