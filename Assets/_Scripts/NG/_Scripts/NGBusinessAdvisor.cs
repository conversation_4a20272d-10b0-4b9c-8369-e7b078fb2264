using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
[System.Serializable]
public class NGBusinessAdvisor
{
    public const string SpritePath = "_Art/Characters/Advisors";
    public string id;
    public bool m_debugChanged;
    public string m_name;
    public string m_givenName;
    public string m_title;
    public string m_firstName;
    public string m_portraitSprite;
    public string m_tinySprite;
    public string m_sprites;
    public string m_info;
    public static List<NGBusinessAdvisor> s_advisors = new();
    public static List<NGBusinessAdvisor> GetList=>s_advisors;  
    public string DebugDisplayName => m_name;
  
    Sprite m_cachedPortait;
    public List<string> Sprites { get
        {
            var results = new List<string>();
            var split = m_sprites.Split(';', '|');
            results.AddRange(split);
            return results;
        } }
    public Sprite PortaitSprite { 
        get
        {
            if (m_cachedPortait == null && m_portraitSprite.IsNullOrWhiteSpace() == false)
                m_cachedPortait = Resources.Load<Sprite>($"{SpritePath}/{m_portraitSprite}");
            return m_cachedPortait;            
        }
    }
    public Sprite TinySprite { 
        get
        {
            if (m_tinySprite.IsNullOrWhiteSpace() == false)
                return Resources.Load<Sprite>($"{SpritePath}/{m_tinySprite}");
            return null;
        }
    }

    public static NGBusinessAdvisor GetInfo(string _name)
    {
        return s_advisors.Find(o => o.m_name.Equals(_name, StringComparison.OrdinalIgnoreCase));
    }
    public static List<NGBusinessAdvisor> LoadInfo()
    {
        s_advisors = NGKnack.ImportKnackInto<NGBusinessAdvisor>();
        foreach(var s in s_advisors)
        {
            if (s.m_portraitSprite.IsNullOrWhiteSpace() == false)
                s.m_cachedPortait = Resources.Load<Sprite>($"{SpritePath}/{s.m_portraitSprite}");
        }

        return s_advisors;
    }

}
