using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Resources;
using Cans.Analytics;
//using MongoDB.Bson.Serialization.Serializers;
using UnityEngine;
//using static HoverGameManagerBase;
using AnalyticsEvent = Cans.Analytics.AnalyticsEvent;
using Debug = UnityEngine.Debug;

[System.Serializable]
public class NGBusinessFlow
{
    public static List<NGBusinessFlow> s_flows;
    public static Dictionary<string, List<NGBusinessFlow>> s_flowsDict = new ();
    public static Dictionary<string, List<NGBusinessFlow>> s_triggerFlows = new ();
    public static Dictionary<string, List<NGBusinessFlow>> s_choiceFlows = new Dictionary<string, List<NGBusinessFlow>>();
    
    public string id;
    public string m_indexer;
    public string m_blockName;
    public float m_index;
    public string m_blockIndex;
    public string m_section;
    public string m_flowType;
    [ScanField] public string m_businessAdvisor;
    public string m_message;
    [ScanField] public string m_messageType;
    public string m_staticPose;
    public string m_audioID;
    public float m_audioRepeatTime;
    [ScanField] public string m_businessDecision;
    [ScanField] public string m_takeAllGifts;
    [ScanField] public string m_chooseGifts;
    public int m_chooseMaxGifts; 
    public string m_advisorChoice;
    public string m_introMessage;
    public string m_enterWaitForTrigger;
    public string m_enterFunctions;
    public string m_exitFunctions;
    public string m_tutorialPhase;
    public float m_giftCostMultiplier;
    public float m_decisionValueMultiplier;
    public string m_comment;

    public MAMessage m_toMessage;
    [HideInInspector] public bool m_debugWindowSelected;
    [HideInInspector] public bool m_debugBreakHere;
    [HideInInspector] public bool m_pauseThisFlow;

    public static List<NGBusinessFlow> GetInfo(string _name)
    {
        if(s_flowsDict.TryGetValue(_name, out var result))
            return result;
        return null;
    }
    public List<NGBusinessDecision> GetDecisions()
    {
        var decisions = new List<NGBusinessDecision>();
        foreach (var d in m_businessDecision.Split('\n', '|', ';'))
        {
            var decision = NGBusinessDecision.GetInfo(d);
            if(decision != null)
                decisions.Add(decision);

        }

        return decisions;
    }
    public static bool PostImport(NGBusinessFlow _what)
    {
        if(s_flowsDict.ContainsKey(_what.m_blockName) == false)
        {
            s_flowsDict.Add(_what.m_blockName, new List<NGBusinessFlow>(){_what});
        }
        else
        {
            s_flowsDict[_what.m_blockName].Add(_what);
        }
        float.TryParse(_what.m_blockIndex, out _what.m_index);
        if (_what.m_chooseGifts.IsNullOrWhiteSpace())
        {
            if (_what.m_chooseMaxGifts != 0)
            {
                Debug.LogError($"ChooseGifts is Empty but MaxGiftsToChoose is {_what.m_chooseMaxGifts} should be 0 {_what.m_blockName}[{_what.m_index}]");
                _what.m_chooseMaxGifts = 0;
            }
        }
        else if (_what.m_chooseMaxGifts == 0)
        {
            Debug.LogError($"ChooseGifts is {_what.m_chooseGifts} but MaxGiftsToChoose is zero should be at least 1 {_what.m_blockName}[{_what.m_index}]");
            _what.m_chooseMaxGifts = 1;
        }
        if(_what.m_message.IsNullOrWhiteSpace() == false && _what.m_messageType.IsNullOrWhiteSpace() == false)
            _what.m_toMessage = MAMessage.Create(_what.m_messageType, _what.m_message, _what.m_audioID, _what.m_staticPose, _what.m_audioRepeatTime, _what.m_businessAdvisor);
        switch (_what.m_flowType)
        {
            case "Flow":
                break;
            case "TriggerRepeat":
            case "TriggerOnce":
                if (s_triggerFlows.ContainsKey(_what.m_blockName) == false)
                {
                    s_triggerFlows.Add(_what.m_blockName, new List<NGBusinessFlow>() {_what});
                }
                else
                {
                    s_triggerFlows[_what.m_blockName].Add(_what);
                }
                break;
            default:
                Debug.LogError($"Unknown FlowType '{_what.m_flowType}'");
                break;
        }
        return true;
    }

    public static List<NGBusinessFlow> LoadInfo()
    {
        s_flowsDict.Clear();
        s_triggerFlows.Clear();
        if (NGKnack.AlphaBusinessFlow)
            s_flows = NGKnack.ImportKnackInto<NGBusinessFlow>(PostImport, "NGBusinessFlow-Alpha");
        else if (NGKnack.IainFlow)
            s_flows = NGKnack.ImportKnackInto<NGBusinessFlow>(PostImport, "NGBusinessFlow-Iain");
        else if (NGKnack.PDMFlow)
            s_flows = NGKnack.ImportKnackInto<NGBusinessFlow>(PostImport, "NGBusinessFlow-PDM");
        else
            s_flows = NGKnack.ImportKnackInto<NGBusinessFlow>(PostImport, "NGBusinessFlow");

        foreach (var d in s_flowsDict)
        {
            d.Value.Sort((x, y) => (x.m_blockName+x.m_blockIndex).CompareTo(y.m_blockName+y.m_blockIndex));
        }
        s_flows.Sort((x, y) => (x.m_blockName+x.m_blockIndex).CompareTo(y.m_blockName+y.m_blockIndex));
// Now load  the Choices
 /*       var choiceFlow = NGKnack.ImportKnackInto<NGBusinessFlow>(PostImport, "NGBusinessChoiceFlow");
        if (choiceFlow != null && choiceFlow.Count > 0)
        {
            s_choiceFlows.Clear();
            foreach (var cf in choiceFlow)
            {
                if (s_choiceFlows.ContainsKey(cf.m_blockName) == false)
                {
                    s_choiceFlows.Add(cf.m_blockName, new List<NGBusinessFlow>() {cf});
                }
                else
                {
                    s_choiceFlows[cf.m_blockName].Add(cf);
                }
            }

            foreach (var dcf in s_choiceFlows)
            {
                dcf.Value.Sort((x, y) => x.m_index.CompareTo(y.m_index));
            }
        }
*/
        return s_flows;
    }
 

 
}
