using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NGBalance : MonoSingleton<NGBalance>
{
    public const string  NoneEntry = "None";
    //Rarity

    List<string> m_giftDropdownList = new List<string>();
    [Dropdown("m_giftDropdownList")]
    public string AddGift;
    public bool m_isChoiceCard = false;

    private void Update()
    {
        if (NGBusinessGift.s_gifts != null && NGBusinessGift.s_gifts.Count > m_giftDropdownList.Count)
        {
            m_giftDropdownList.Clear();
            var magifts = new List<string>();
            foreach (var g in NGBusinessGift.s_gifts)
            {
                if (g.m_name.StartsWith("MA") == false)
                    magifts.Add(g.m_name);
                else
                    m_giftDropdownList.Add(g.m_name);
            }
            m_giftDropdownList.Sort();
            magifts.Sort();
            m_giftDropdownList.AddRange(magifts);
            m_giftDropdownList.Insert(0, NoneEntry);
            AddGift = NoneEntry;
        }
        
        if (m_giftDropdownList.Count > 0 && AddGift.Equals(NoneEntry) == false)
        {
            var gift = NGBusinessGift.s_gifts.Find(o => o.m_name.Equals(AddGift));
            if (gift != null)
            {
                if(m_isChoiceCard)
                    NGBusinessGiftsPanel.CreateOrCall(null, new List<NGBusinessGift>() { gift }, 1);
                else
                    NGBusinessGiftsPanel.CreateOrCall(new List<NGBusinessGift>() { gift });
            }
            AddGift = NoneEntry;
        }
    }
    public enum RarityType
    {
        None,
        Common,
        Uncommon,
        Epic,
        Rare,
        Legendary,
        Premium,
    }

    public static ResearchLabRewardsController.Rarity ToRLRarity(RarityType _r)
    {
        switch (_r)
        {
            default:
            case RarityType.None:
            case RarityType.Common:
                return ResearchLabRewardsController.Rarity.Common;
            case RarityType.Uncommon:
            case RarityType.Rare:
                return ResearchLabRewardsController.Rarity.Rare;
            case RarityType.Epic:
                return ResearchLabRewardsController.Rarity.Epic;
            case RarityType.Legendary:
                return ResearchLabRewardsController.Rarity.Legendary;
            case RarityType.Premium:
                return ResearchLabRewardsController.Rarity.Premium;
        }
    }

    public static RarityType StringToRarity(string _rarity) {
        if (System.Enum.TryParse(_rarity, out RarityType r)) return r;
        return RarityType.None;
    }
  
    public static void Save(ref string _s) 
    {
        _s = ReactReflection.MakePropertyString<NGBalance>();
    }

    public static void Load(string _l)
    {
        ReactReflection.DecodePropertyStrings<NGBalance>(_l);
    }
    
    public static float Lerp(float _speed)
    {
        return Mathf.Lerp(0f, 1f, _speed);
    }
    public static float Smooth(float _speed)
    {
        return Lerp(_speed * 2f - _speed * _speed);
    }
    public static float SmoothStep(float _speed)
    {
        return _speed * _speed * (3 - _speed - _speed);
    }
}
