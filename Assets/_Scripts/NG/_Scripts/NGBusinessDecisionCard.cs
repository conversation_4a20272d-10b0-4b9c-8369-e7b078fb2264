using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
using UnityEngine.EventSystems;
using System.Reflection;
using UnityEngine.Serialization;

public class NGBusinessDecisionCard : MonoBehaviour, IDragCard, IBusinessCard
{
    public TMP_Text m_title;
    public Transform m_cardHolder;
    public INGDecisionCardHolder m_holder;
    public NGBusinessGift m_gift;
    public MAParserSection m_maParserSection;
    private NGDirectionCardBase m_cardTile;

    public float Cost => m_gift.CardPrice;
    
    public MAParserSection MaParserSection { get { return m_maParserSection; } }
    public NGBusinessGift Gift { get { return m_gift; } }

    public void DestroyMe()
    {
        Destroy(gameObject);
    }

    public void OnEndDragCustom(PointerEventData _eventData, bool _undo) {}
    
    public void Reactivate(INGDecisionCardHolder _holder)
    {
        DisableInteraction(false);
        Enable();
        
        transform.SetParent(_holder.GiftsHolder());

        transform.localScale = Vector3.one;
        m_holder = _holder;
    }
    void Activate(INGDecisionCardHolder _holder, NGBusinessGift _gift, MAParserSection _maParserSection)
    {
        m_holder = _holder;
        m_gift = _gift;
        m_maParserSection = _maParserSection;
        m_title.text = m_gift.m_cardTitle;
        m_cardHolder.DestroyChildren();

        m_cardTile = NGBusinessGiftsPanel.CreateGiftCard(m_holder, null, m_gift, _maParserSection, m_cardHolder);
    }

    public void DragCardConfirmed(GameObject _context) {
	/*	switch(m_gift.Type) {
			case NGBusinessGift.GiftType.Building:
				BuildingPlacementManager.Me.Toggle(true, m_gift.BuildingsToUpgrade[0], () => {
					GiftRecieved();
                });
				break;
		}*/
	}

    public void OnClick(bool _long) {
    }

    public void DisableInteraction(bool _disable)
    {
        if(m_cardTile != null)
            m_cardTile.m_disableInteraction = _disable;
    }

    public static NGBusinessDecisionCard Create(INGDecisionCardHolder _holder, NGBusinessGift _gift, MAParserSection _maParserSection)
    {
        var go = Instantiate(GlobalData.Me.m_decisionCardPrefab, _holder.GiftsHolder());
        go.transform.localPosition = -go.transform.parent.localPosition;
        var bdc = go.GetComponent<NGBusinessDecisionCard>();
        bdc.Activate(_holder, _gift, _maParserSection);
        return bdc;
    }
    
    public void Enable()
    {
        if (m_cardTile != null)
            m_cardTile.enabled = true;
    }

    public void Disable()
    {
        //const float up = 45f;
        //m_cardTile.transform.position += Vector3.up * up;
        if (m_cardTile != null)
            m_cardTile.enabled = false;
    }
}
