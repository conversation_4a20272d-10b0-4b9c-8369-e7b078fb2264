using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;
using UnityEngine.EventSystems;
using TMPro;

public interface INGDecisionCardHolder
{
    public enum ECardView
    {
        None,
        Simple,
        Detailed,
        Standard
    }
    public ECardView CardView { get; }
    public void ToggleHiding(bool _hide, bool _isDueToClick = false);
    public bool IsHidden { get; }
    public void GiftReceived(IBusinessCard _card);
    public Transform GiftsHolder();
    public Transform Root { get; }
    public bool EnableCardOnUpwardDrag();
    public Transform ParentForDraggingCard(NGDirectionCardBase _card);
    public Transform GetParentHolder(NGDirectionCardBase _card);
    public bool ShowOrderBoardGUIOnCardClick { get; }
    public void OnStartCardDrag();
    public void OnEndCardDrag();
    public bool DragIn3D { get; }
    public void OnCardClick();
}
public interface IBusinessCard
{
    NGBusinessGift Gift { get; }
    MAParserSection MaParserSection { get; }
    Transform transform { get; }
    GameObject gameObject { get; }
    void DisableInteraction(bool _disable);
    float Cost { get; }
    void DestroyMe();
}

public abstract class NGDecisionCardHolder<T> : MonoSingleton<T>, INGDecisionCardHolder, IBeginDragHandler, IDragHandler, IEndDragHandler, IPointerClickHandler where T : MonoSingleton<T>
{
    public INGDecisionCardHolder.ECardView CardView { get { return INGDecisionCardHolder.ECardView.Standard; } }
    public Transform Root { get { return this.transform; } }
    public bool IsHidden {  get { return m_isHidden;  }  }
    public bool EnableCardOnUpwardDrag() { return true; }
    public bool ShowOrderBoardGUIOnCardClick { get { return true; } }
    public bool DragIn3D { get { return true; } }
    public TMP_Text m_title;
//    public BuildBarDraggableTile m_buildBusinessTilePrefab;
//    public BuildBarDraggableTile m_buildCivicTilePrefab;
//    [SerializeField] BuildBarDraggableTile m_buildDecorationTilePrefab;
    public NGBusinessDecision m_decision;
    public NGBusinessDirection m_direction;
    public List<NGBusinessGift> m_gifts;
    public bool m_isHidden;
    public RectTransform m_rt;
    public Vector3 m_hiddenPosition = new Vector3(0f, -150f);
    public Vector3 m_shownPosition = new Vector3(0f, 100f);
    public Animator m_anim;
    public List<IBusinessCard> m_cards = new List<IBusinessCard>();
    public NGBusinessDecisionCard m_businessDecisionCardPrefab;
    public Transform m_giftsHolder;
    public float m_jiggleTime = 5f;
    float m_jiggleTimer;
    public bool m_shouldJiggle = true;
    private int m_openCardHolderWhenCashBalanceReached = -1;

    public NGBusinessDecisionCard BusinessDecisionCardPrefab() => m_businessDecisionCardPrefab;
    public Transform GiftsHolder() => m_giftsHolder;

    public Transform ParentForDraggingCard(NGDirectionCardBase _card) { return null; }
    public Transform GetParentHolder(NGDirectionCardBase _card) { return null; }

    virtual public void Activate()
	{
        m_cards.Clear();
        m_giftsHolder.DestroyChildren();
        m_rt = GetComponent<RectTransform>();
        ToggleHiding(m_cards.Count == 0);
    }

    override public void DestroyMe()
	{
        base.DestroyMe();
	}
    public void ToggleHiding(bool _hide, bool _isDueToClick = false)
    {
        if (_hide == m_isHidden)
            return;
        if(BuildingPlacementManager.Me.IsActive)
            return;
        var pos = m_isHidden ? m_shownPosition : m_hiddenPosition;
        m_rt.DOAnchorPos(pos, .2f).SetEase(Ease.OutQuart);
        m_isHidden = !m_isHidden;

        if(m_isHidden) OnHidden();

        if (GameManager.Me.IsOKToPlayUISound() && this != null)
        {
            if(m_isHidden)
                AudioClipManager.Me.PlaySoundOld("PlaySound_Card_PanelClosed", transform);
            else
                AudioClipManager.Me.PlaySoundOld("PlaySound_Card_PanelOpen", transform);
        }
    }

    public void OnStartCardDrag() {}
    public void OnEndCardDrag() {}
    public void OnCardClick() {}
    
    virtual protected void OnHidden() { }

    virtual public void GiftReceived(IBusinessCard _card)
	{
        Debug.Log("NGDecisionCardHolder - GiftRecieved - Gift Received");
    }
    
    void IPointerClickHandler.OnPointerClick(PointerEventData eventData)
    {
        ToggleHiding(!m_isHidden, true);
        SetJiggle(false);
        //NGTutorialManager.Me.FireExternalTrigger();
    }

    void IBeginDragHandler.OnBeginDrag(PointerEventData _eventData)
    {
//        SetJiggle(false);
        SetJiggle(true);
    }

    void IDragHandler.OnDrag(PointerEventData _eventData)
    {
        var dragVec = _eventData.position - _eventData.pressPosition;
        float ax = Mathf.Abs(dragVec.x), ay = Mathf.Abs(dragVec.y);
        if (ay > ax)
        {
            // vertical movement
            MoveTile(true, dragVec.y);
        }
    }

    void IEndDragHandler.OnEndDrag(PointerEventData _eventData)
    {
        var dragVec = _eventData.position - _eventData.pressPosition;
        MoveTile(false);
    }

    void MoveTile(bool _move, float _yDiff = 0f)
    {
        if (!_move)
        {
            float distToShow = Mathf.Abs(m_shownPosition.y - m_rt.anchoredPosition.y);
            float distToHidden = Mathf.Abs(m_hiddenPosition.y - m_rt.anchoredPosition.y);
            if (distToShow < distToHidden)
                ToggleHiding(false);
            else
                ToggleHiding(true);
        }
        else
        {
            Vector3 startPos = (m_isHidden) ? m_hiddenPosition : m_shownPosition;
            Vector3 newPos = startPos + new Vector3(0, _yDiff, 0);
            newPos.y = Mathf.Clamp(newPos.y, m_hiddenPosition.y, m_shownPosition.y);
            m_rt.anchoredPosition = newPos;
        }
    }

    void SetJiggle(bool _flag)
    {
        m_shouldJiggle = _flag;
    }

    protected void SetAutoOpenTriggerForUnafordableCards()
    {
        int cheapestUnaffordableCard = -1;
        // Check if there are cards we can't afford
        foreach(var card in m_cards)
        {
            var cost = card.Cost;
            if(cost > NGPlayer.Me.m_cash.Balance && (cheapestUnaffordableCard == -1 || cost < cheapestUnaffordableCard))
            {
                cheapestUnaffordableCard = (int)cost;
            }
        }
        m_openCardHolderWhenCashBalanceReached = cheapestUnaffordableCard;
    }

    private void UpdateAutoOpen()
    {
        if(m_openCardHolderWhenCashBalanceReached < 0) return;

        if(NGPlayer.Me.m_cash.Balance < m_openCardHolderWhenCashBalanceReached) return;

        ToggleHiding(false);
        m_shouldJiggle = true;
        
        m_openCardHolderWhenCashBalanceReached = -1;        
    }

	virtual public void Update()
	{
        UpdateAutoOpen();

        if (m_shouldJiggle && m_jiggleTimer < Time.time && m_anim)
        {
            m_jiggleTimer = Time.time + m_jiggleTime;
            m_anim.SetTrigger("JiggleTrigger");
        }
    }
}
