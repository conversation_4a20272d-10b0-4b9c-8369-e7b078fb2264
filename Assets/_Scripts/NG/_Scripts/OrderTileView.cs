using System;
using TMPro;
using UnityEditor.Rendering;
using UnityEngine;
using UnityEngine.UI;

public class OrderTileView : MonoBehaviour
{
    public bool m_enableGiverIcon;
    public TMP_Text m_action;
    public TMP_Text m_makeQuantityRemaining;
    public TMP_Text m_orderQuantityRemaining;
    public TMP_Text m_orderQuality;
    public TMP_Text m_productLine;
    public TMP_Text m_giverName;
    public TMP_Text m_needs;
    public TMP_Text m_reputation;
    public Image m_reputationProgressBar;
    public TMP_Text m_hint;
    public TMP_Text m_detailedText;
    public Image m_orderQualityImage;
    public Image m_giverIcon;
    
    public bool m_hideRewards = false;
    
    NGOrderTile m_orderTile;
    
    private float m_lastUpdateTime = 0;

    public void Start()
    {
        m_orderTile = GetComponentInParent<NGOrderTile>();
        Update();
    }

    public void Update()
    {
        if(Time.time - m_lastUpdateTime < 1f) return;
        
        m_lastUpdateTime = Time.time;
        
        if(m_orderTile == null || m_orderTile.Order == null || m_orderTile.Order == MAOrder.EmptyOrder || m_orderTile.Order.OrderInfo == null) return;
        
        var order = m_orderTile.Order;
        var info = order.OrderInfo;
        var quality = MADesignGuage.GetQualityEntry(order.m_productLine, order.m_orderQuality);
        
        if(m_makeQuantityRemaining != null)
        {
            m_makeQuantityRemaining.text = Mathf.Max(1,order.RemainingToManufacture).ToString();
        }
        
        if(m_hideRewards && m_orderTile.m_rewardHolder.gameObject.activeSelf)
        {
            m_orderTile.m_rewardHolder.gameObject.SetActive(false);
        }
        else if(m_hideRewards == false && m_orderTile.m_rewardHolder.gameObject.activeSelf == false)
        {
            m_orderTile.m_rewardHolder.gameObject.SetActive(true);
        }
        if(m_orderQuantityRemaining != null)
        {
            if(order.IsInfinateOrder)
            {
                if(m_action && m_action.gameObject.activeSelf) m_action.gameObject.SetActive(false);
                m_orderQuantityRemaining.text = "∞";
            }
            else
            {
                m_orderQuantityRemaining.text = order.RemainingQuantity.ToString();
            }
        }
        
        if(m_giverName != null)
        {
            if(info.OrderGiver == null || info.OrderGiver.HideGiver)
            {
                if(m_giverName.gameObject.activeSelf)
                    m_giverName.gameObject.SetActive(false);
            }
            else
            {
                m_giverName.text = info.OrderGiver?.m_displayName ?? "";    
            }
        }
        
        if(m_reputation != null)
        {
            if(info.OrderGiver == null || info.OrderGiver.HideGiver)
            {
                if(m_reputation.transform.parent.gameObject.activeSelf)
                    m_reputation.transform.parent.gameObject.SetActive(false);
            }
            else
            {
                m_reputationProgressBar.fillAmount = info.OrderGiver?.CalculateReputationRemainder() ?? 0;
                m_reputation.text = $"<size=150%>{info.OrderGiver?.GetReputationStars() ?? ""}</size>";   
            }
        }
        
        if(m_hint != null)
        {
            m_hint.text = $"<b>Hint</b>\n{info.GetHints()}";
        }
        
        if(m_needs != null)
        {
            m_needs.text = $"<b>{order.m_orderQuantity} {order.GetQualityString(true)} {order.ProductDisplayName}</b>";
        }
        
        if(m_orderQuality != null)
        {
            if(order.ShowQualityString())
                m_orderQuality.text = quality.m_name;
            else if(m_orderQuality.transform.parent.gameObject.activeSelf)  // Use parernt as this holds the background
                m_orderQuality.transform.parent.gameObject.SetActive(false); 
        }
        
        if(m_productLine != null)
        {
            m_productLine.text = info.m_useName ? order.DisplayName : order.ProductDisplayName;
        }
            
        if(m_orderQualityImage != null)
            m_orderQualityImage.color = quality.m_color;
            
        if(m_giverIcon != null)
        {
            if(info.OrderGiver?.PortraitSprite != null && m_enableGiverIcon)
                m_giverIcon.sprite = info.OrderGiver.PortraitSprite;
            else if(m_giverIcon.gameObject.activeSelf)
                m_giverIcon.gameObject.SetActive(false);
        }
        
        if(m_detailedText != null)
        {
            m_detailedText.text = order.GetDescription();
        }
    }
}
