using System;
using System.Collections;
using System.Collections.Generic;
using System.Globalization;
using UnityEngine;
using UnityEngine.UI;
using TMPro;
public class NGCurrencyTile : NGDirectionCardBase
{
    public TMP_Text m_quantity;
    public float m_amount;
    public TMP_Text m_currencyAmount;

    [System.Serializable]
    public class ImageToType
    {
        public string m_name;
        public Sprite m_icon;
    }
    public List<ImageToType> m_defaultIcons;

    protected override void Activate(NGBusinessGift _gift, MAParserSection _maParserSection, INGDecisionCardHolder _cardHolder)
    {
        base.Activate(_gift, _maParserSection, _cardHolder);
        m_currencyAmount.text = _gift.m_quantity.ToString();
        m_amount = _gift.m_quantity;
        // if (_gift.m_power.IsNullOrWhiteSpace() == false)
        // {
        //     var amount = ReactReflection.GetParameters(_gift.m_power);
        //     if (amount == null || amount.Length != 1)
        //     {
        //         Debug.LogError($"{_gift.m_power} incorrect syntax");
        //         return;
        //     }
        //
        //     m_amount = float.Parse(amount[0], NumberStyles.Integer);
        // }
        if (m_gift.m_spritePath.IsNullOrWhiteSpace() == false)
            m_image.sprite = m_gift.GetSprite;
        else
        {
            var defaultSprite = m_defaultIcons.Find(o => _gift.m_cardPower.ToLower() == o.m_name.ToLower());
            if (defaultSprite == null)
                Debug.LogError($"No sprite image to show for gift {_gift.m_giftTitle}");
            else
                m_image.sprite = defaultSprite.m_icon;
        }
        if(m_quantity != null)
            m_quantity.text = m_amount.ToString("F0");
    }
    override public void GiveReward()
    {
        base.GiveReward();
        var type = m_gift.m_cardPower.ToLower();
        if (m_gift.m_power.IsNullOrWhiteSpace() == false)
        {
            if(m_gift.m_power == "Cash")
            {
                NGPlayer.Me.m_cash.Add(CurrencyContainer.TransactionType.InGame, m_gift.m_quantity, "Gift", transform);
            }
            else if (Enum.TryParse<MAFactionInfo.FactionType>(m_gift.m_power, out var functionType))
            {
                switch (functionType)
                {
                    case MAFactionInfo.FactionType.Royal:
                        NGPlayer.Me.m_royalFavors.Add(CurrencyContainer.TransactionType.InGame, m_gift.m_quantity, "Gift", transform);
                        break;
                    case MAFactionInfo.FactionType.Commoners:
                        NGPlayer.Me.m_commonersFavors.Add(CurrencyContainer.TransactionType.InGame, m_gift.m_quantity, "Gift", transform);
                        break;
                    case MAFactionInfo.FactionType.Lords:
                        NGPlayer.Me.m_lordsFavors.Add(CurrencyContainer.TransactionType.InGame, m_gift.m_quantity, "Gift", transform);
                        break;
                    case MAFactionInfo.FactionType.Mystic:
                        NGPlayer.Me.m_mysticFavors.Add(CurrencyContainer.TransactionType.InGame, m_gift.m_quantity, "Gift", transform);
                        break;
                }
            }
            else
            {
                Debug.LogError($"Unknown function type {m_gift.m_power}");
            }
        }
    }
}
