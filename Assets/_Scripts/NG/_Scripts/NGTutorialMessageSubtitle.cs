//#if CHOICES
using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using TMPro;
using UnityEngine.UI;
public class NGTutorialMessageSubtitle : NGTutorialMessageBase
{
    public TMP_Text m_preBarMessage;
   //public NGTutorialInterface.TrackReturn m_trackResult =  new NGTutorialInterface.TrackReturn(0,-1, 0);
    public Animator m_animator;
    public Image m_advisorImage;
    public int hi;
    private string m_messageType;
    public GameObject m_panel;

    private GameObject m_visualsProp;

    override protected void OnDestroy()
    {
        EnableProps(false);
		
		base.OnDestroy();
    }

    void EnableProps(bool _enable)
    {
        if (m_visualsProp != null) m_visualsProp.SetActive(_enable);
    }
    
    override public bool IsSubtitle => true;

    void Update()
    {
    }

    override public void Reactivate(string _preBarText, string _postBarText, string _trackFuncString, string _type, string _pose)
    {
        string decoded = Decode(_preBarText);
        m_preBarMessage.text = decoded;
        m_preBarMessage.gameObject.SetActive(_preBarText.IsNullOrWhiteSpace() == false);
        
        if(m_panel != null)
        {
            m_panel.SetActive(_preBarText.IsNullOrWhiteSpace() == false);
        }
        
        if (m_advisorImage != null)
        {
            m_advisorImage.gameObject.SetActive(false);
            m_advisorImage.sprite = GetPoseSprite(_pose);
            if(m_advisorImage.sprite != null)
                m_advisorImage.gameObject.SetActive(true);
        }
        base.Reactivate( _preBarText, _postBarText, _trackFuncString, _type, _pose);
    }
    override protected void Activate(string _preBarText, string _postBarText, string _trackFuncString, string _type, string _pose)
    {
        base.Activate(_preBarText, _postBarText, _trackFuncString, _type, _pose);
        string decoded = Decode(_preBarText);
        if (SettingsUIController.Subtitles == false && _type == "Banner")
            m_preBarMessage.text = "";
        else
            m_preBarMessage.text = decoded;
        m_messageType = _type;
        m_preBarMessage.gameObject.SetActive(_preBarText.IsNullOrWhiteSpace() == false);
        m_animator.SetTrigger("open");

        if(m_panel != null)
        {
            m_panel.SetActive(_preBarText.IsNullOrWhiteSpace() == false);
        }
//        if(m_messageType == "SubtitlesMentor")
//           NGTownSatisfactionManager.Me.m_NGTownSatisfactionGUIHolder.gameObject.SetActive(false);
        if (m_advisorImage != null)
        {
            m_advisorImage.gameObject.SetActive(false);
            m_advisorImage.sprite = GetPoseSprite(_pose);
            if(m_advisorImage.sprite != null)
                m_advisorImage.gameObject.SetActive(true);
        }
    }

    override protected void SetVisualsCamera(Camera _cam, GameObject _prop)
    {
        m_visualsProp = _prop;
        EnableProps(true);
    }
    public override bool CanDestroy(string _type)
    {
        if (m_messageType != _type)
            return true;
        return false;
    }

    private string Decode(string _text)
    {
        NGCommanderBase cb;
        // NGTutorialInterface.TutorialDataType t;

        string output = "";
        var parts = _text.Split('[');
        if (parts.Length == 1)
        {
            output = _text;
        }
        return output;
    }
    
    public void ActivateDisplay(Sprite _image, float _displayTime)
    {
        m_animator.SetTrigger("open");
        m_advisorImage.sprite = _image;
        m_advisorImage.gameObject.SetActive(true);
        m_preBarMessage.text = "";
        StartCoroutine(DestroyAfterSeconds(_displayTime));
    }
    IEnumerator DestroyAfterSeconds(float seconds)
    {
        yield return new WaitForSeconds(seconds); // Wait for the specified time
        Destroy(gameObject); // Destroy this GameObject
    }
    public override bool IsOKToContinue()
    {
        return true;
    }

    override public bool IsFinished()
    {
        return m_buttonFinished;
    }
    public static NGTutorialMessageSubtitle Create(Sprite _image, string _type, float _displayTime)
    {
        var info = MAMessageType.GetInfo(_type.Trim());
        if (info == null)
        {
            if (_type == "JustAudio") return null;
            Debug.LogError($"Message type of {_type} not implemented in message :");
            return null;
        }
        if (info.m_prefab == null || info.m_type == null)
        {
            Debug.LogError($"Just show Message type not setup in NGTutorialManager.Me.m_NGTutorialMessagePrefabs");
            return null;
        }
        var go = Instantiate(info.m_prefab.gameObject, info.m_holder);
        var msg = go.GetComponent<NGTutorialMessageSubtitle>();
        msg.ActivateDisplay(_image, _displayTime);
        return msg;
    }
}
//#endif
