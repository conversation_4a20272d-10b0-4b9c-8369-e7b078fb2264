using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class NGPickupUpgradeCard : ReactPickupPersistent
{
    [Header("PickupUpgradeCard")]
    public Transform m_cardHolder;
    public NGBusinessGift m_gift;
    public NGCommanderBase m_applyToCommander;
    public NGDirectionCardBase m_card;
    // Raise the card so that the info isn't beneath the touch point
	private Vector3 m_cardOffset = new Vector3(0, 0f, 0);
    
    public void Start()
    {
        // Required to prevent stuff happening in base class
    }

    protected override void Update()
    {
        // Required to prevent stuff happening in base class
    }

    override public float GetDropScore(string _name)
    {
        var buildings = new List<string> { "Any", _name };
        var cost = m_gift.CardPrice;

        if (NGPlayer.Me.m_cash.Balance >= (long)cost)
        {
            if (m_gift.m_componentsToUpgradeInfos.Contains(buildings, _info => _info.m_classType.Name))
            {
                return 1f;
            }
            
            if(m_gift.Type == NGBusinessGift.GiftType.UpgradeDispatchVehicleCapacity)
            {
                return 1;
            }
        }
        
        return 0f;
    }
    override public bool IsOkayToCollide(NGCommanderBase _object) 
    {
        if (m_gift.Type == NGBusinessGift.GiftType.UpgradeDispatchVehicleCapacity)
            return true;

        if(m_gift.m_buildingsToUpgradeList.Contains(_object.Name))
            return true;
        
        MABuilding maBuilding = _object as MABuilding;
        if(maBuilding != null)
        {
            foreach(MAComponentInfo maComponentInfo in m_gift.m_componentsToUpgradeInfos)
            {
                if(maBuilding.GetBuildingComponentCount(maComponentInfo) > 0)
                {
                    return true;
                }
            }
        }
        
        return false;
    }

    override public void HasCollidedWith(NGCommanderBase _what) 
    {
        if (gameObject.activeSelf)
        {
            //NGTutorialManager.Me.FireRewardTrigger(m_gift.m_name);

            switch(m_gift.m_name)
            {
                case "UnlockTownSatisfaction":
                    break;
                case "Unlock Culture":
              //      NGTutorialManager.Me.FireCultureTrigger();
//                    NGTownSatisfactionManager.Me.ActivateCulture(true);
                    break;
                case "Unlock Pickup Options":
                    break;
                case "UnlockVault":
                    break;
                case "UnlockChoices":
                    break;
                case "Unlock Roads":
                    break;
                case "Unlock Design Competitions":
                    break;
                default:
                    break;
            }

            m_gift.ApplyGiftToBuilding(_what);
            m_card.m_destFactory = _what;
            m_card.GiveReward();
            gameObject.SetActive(false);
            Destroy(gameObject);
        }
    }

    private void DestroyCard(bool _wasAShake)
    {
        m_card?.ActionCancelled(_wasAShake);
        Destroy(gameObject);
    }

    public override bool DroppedByPlayerAt(NGCommanderBase _hitCommander)
	{
        if (_hitCommander == null)
        {
            ShakenFromHand();
            return false;
        }
        return base.DroppedByPlayerAt(_hitCommander);
	}

    public override void ShakenFromHand()
    {
        DestroyCard(true);
    }
    override public void ShakenFromHand(Vector3 _dropPos)
    {
        DestroyCard(true);
    }

    protected override void OnDestroy()
    {
        if(this.m_card as NGUpgradeTile == null)
            NGDemoManager.Me?.DestroyUpgradeLevelDialogue();
        base.OnDestroy();
    }

    public void ActivateExternal(NGDirectionCardBase _card, NGBusinessGift _gift)
    {
        m_gift = _gift;
        m_card = _card;
        switch(m_gift.Type)
        {
            case NGBusinessGift.GiftType.UpgradeBuilding:
                Name = "NGUpgrade";
                break;
            case NGBusinessGift.GiftType.Unlock:
                Name = "NGUnlock";
                break;
            case NGBusinessGift.GiftType.UpgradeDispatchVehicleCapacity:
                Name = "NGUpgradeDispatchVehicle";
                break;
            case NGBusinessGift.GiftType.UnlockNewDispatchVehicle:
                Name = "NGUnlockDispatchVehicle";
                break;
            default:
                Name = "NGUpgrade";
                break;
        }
        m_contents = NGCarriableResource.GetInfo("None");
    }

    void Activate(Vector3 _pos, NGDirectionCardBase _card, NGBusinessGift _gift)
    {
        ActivateExternal(_card, _gift);
        
        m_card.transform.SetParent(m_cardHolder);
        m_card.transform.localScale = new Vector3(1f, 1f, 1f);
        m_card.transform.localEulerAngles = Vector3.zero;
        m_card.transform.localPosition = m_cardOffset;
        transform.forward = -Camera.main.transform.forward;
        transform.position = _pos;
    }
    public static NGPickupUpgradeCard Create(NGDirectionCardBase _card, NGBusinessGift _gift, Vector3 _pos)
    {
        var go = Instantiate(NGBusinessDecisionManager.Me.m_pickupUpgradeCardPrefab, NGBusinessDecisionManager.Me.m_pickupUpgradeCardHolder);
        var puc = go.GetComponent<NGPickupUpgradeCard>();
        puc.Activate(_pos, _card, _gift);
        return puc;
    }
    
    override public void ShowInHandDetails(GameObject _commander, NGMovingObject _object) 
    {
        m_applyToCommander = _commander?.GetComponent<NGCommanderBase>();
        if (m_applyToCommander)
            m_card.ShowDetailsCard(m_applyToCommander, _object);
        else
            m_card.ShowBasicCard();
    }

    override public bool DropNoBezier()
    {
        if (m_gift != null && m_gift.m_componentsToUpgrade.IsNullOrWhiteSpace())
        {
            return true;
        }

        return false;
    }

    public override void ShowUpgradeCelebration(Transform _holder)
    {
        switch(m_gift.Type)
        {
            case NGBusinessGift.GiftType.Order:
                break;
            default:
                if (NGManager.Me.m_upgradeCelebrationPrefab == null)
                {
#if UNITY_EDITOR
                    Debug.LogError("No upgrade celebration prefab found in NGManager m_upgradeCelebrationPrefab");
#endif
                }
                else
                    Instantiate(NGManager.Me.m_upgradeCelebrationPrefab.gameObject, _holder);
                break;
        }
    }
}
