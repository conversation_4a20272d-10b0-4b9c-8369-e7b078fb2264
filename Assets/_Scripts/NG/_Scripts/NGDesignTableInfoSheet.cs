using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;

public class NGDesignTableInfoSheet : MonoSingleton<NGDesignTableInfoSheet>
{
    public Color m_positiveColor;
    public Color m_negativeColor;
    public Transform m_materialsPanel;
    public Transform m_workersPanel;
    public Transform m_tapsPanel;
    public Transform m_buffsPanel;
    public TMP_Text m_noWorkersLine;

    public GameObject m_capacityPanel;
    public TMP_Text m_factoryCapacity;

    public NGDesignTableMaterialsLine m_materialsLinePrefab;
    public NGDesignTableWorkerLine m_workerLinePrefab;
    public NGDesignTableTapsLine m_tapsLinePrefab;
    public NGDesignTableBuffsLine m_buffsLinePrefab;
    public TMP_Text m_residentsLine;
    public GameObject m_residentsPanel;

    public Dictionary<string, int> m_currentMaterials = new Dictionary<string, int>();
    public int m_currentNumWorkers = -1;
    public float m_currentTimePerBuild = -1f;
    public int m_currentNumTaps = -1;

    public List<NGDesignTableMaterialsLine> m_materialLines;
    public NGDesignTableWorkerLine m_workerLine;
    public NGDesignTableTapsLine m_tapsLine;
    public NGDesignTableBuffsLine m_buffsLine;

    void Awake()
    {
        Activate();
    }

    public void FactoryStorageInfo(bool _on, string _capacity)
    {
        m_capacityPanel.SetActive(_on);
        m_factoryCapacity.text = _capacity;
    }

    public void Activate()
    {
        m_currentMaterials.Clear();
        m_currentNumWorkers = -1;
        m_currentTimePerBuild = -1;
        m_currentNumTaps = -1;
        m_workersPanel.gameObject.SetActive(false);
        m_tapsPanel.gameObject.SetActive(false);
        m_workerLine = m_workersPanel.GetComponentInChildren<NGDesignTableWorkerLine>(true);
        m_tapsLine = m_tapsPanel.GetComponentInChildren<NGDesignTableTapsLine>(true);
        m_buffsLine = m_buffsPanel.GetComponentInChildren<NGDesignTableBuffsLine>(true);
        m_materialsPanel.DetachChildren();
    }

    /*public void UpdateValues(Dictionary<NGCarriableResource, float> _materials, string _materialError, int _numWorkers, int _numResidents, float _timePerBuild, float _numTaps)
    {
        foreach (var m in m_materialLines) m.m_active = false;
        if (_materials != null)
        {
            foreach (var m in _materials)
            {
                var matName = m.Key.m_title;
                var materialLine = m_materialLines.Find(o => o.m_resource == matName);
                if (materialLine)
                {
                    materialLine.SetValue(matName, m.Value);
                }
                else
                {
                    materialLine = NGDesignTableMaterialsLine.Create(m_materialsLinePrefab, m_materialsPanel);
                    m_materialLines.Add(materialLine);
                    materialLine.SetValue(matName, m.Value);
                }
            }
        }
        if (!string.IsNullOrEmpty(_materialError))
        {
            var errorLine = NGDesignTableMaterialsLine.Create(m_materialsLinePrefab, m_materialsPanel);
            m_materialLines.Add(errorLine);
            errorLine.SetError($"<color=#C00000>{_materialError}</color>");
        }

        for (var i = m_materialLines.Count - 1; i >= 0;i--)
        {
            var m = m_materialLines[i];
            if (m.m_active == false)
            {
                m_materialLines.RemoveAt(i);
                m.DeleteMe();
            }
        }
        
        if (_numWorkers == 0)
        {
            m_noWorkersLine.gameObject.SetActive(true);
            m_workerLine.gameObject.SetActive(false);
        }
        else if (_numWorkers < 0)
        {
            m_noWorkersLine.gameObject.SetActive(false);
            m_workerLine.gameObject.SetActive(false);
        }
        else
        {
            m_workerLine.gameObject.SetActive(true);
            m_noWorkersLine.gameObject.SetActive(false);
            m_workerLine.SetValue(_numWorkers, _timePerBuild);
        }

        if (_numTaps <= 0)
        {
            m_tapsLine.gameObject.SetActive(false);
        }
        else
        {
            m_tapsLine.gameObject.SetActive(true);
            m_tapsLine.SetValue((int)_numTaps);
        }

        if (_numResidents > 0)
        {
            m_residentsLine.text = "Living Space: " + _numResidents;
        }
        else
        {
            m_residentsPanel.SetActive(false);
        }
    }*/
    
    static public bool SetAnimText(TMP_Text _text, float _value, string _format)
    {
        if (_value.IsZero()) return false;
    
        var plusMinus = "+";
        var color = NGDesignTableInfoSheet.Me.m_positiveColor;
        var text = _value.ToString(_format);
        if (_value < 0)
        {
            plusMinus = " ";
            color = NGDesignTableInfoSheet.Me.m_negativeColor;
            if (GameManager.Me.IsOKToPlayDesignTableSound())
                AudioClipManager.Me.PlaySoundOld("PlaySound_Decrease", GameManager.Me.transform);
        }
        else
        {
            if (GameManager.Me.IsOKToPlayDesignTableSound())
                AudioClipManager.Me.PlaySoundOld("PlaySound_Increase", GameManager.Me.transform);
        }
        _text.color = color;
        _text.text = $"{plusMinus}{text}";
        
        return true;
    }



}
