using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

[System.Serializable]
public class NGBusinessDirection
{
    public enum State
    {
        Activate,
        ShowInMessage,
        UpdateDecisions,
        TriggeredDecision,
        ShowOutMessage,
        FinishedDirection,
		ShowDirectionDecision
    }
    public NGBusinessDirection() { }
    public NGBusinessDirection(int _chapterNumber, float _partNumber) { m_chapterNumber = _chapterNumber; m_partNumber = _partNumber;  }
    public string m_indexer;
    public int m_index;
    public string m_advisor;
    public string m_inMessage;
    public string m_outMessage;
    public string m_nextAdvisor;
    public int m_currentDecision = -1;
    public  int m_maxSteps;
    public int m_stepCount;
    public int m_chapterNumber;
    public float m_partNumber;
    public bool m_hasShownInMessage;
    public State m_state =  State.Activate;
    public List<NGBusinessGift> m_gifts = new List<NGBusinessGift>();
    public List<NGBusinessDecision> m_businessDecisions = new List<NGBusinessDecision>();
    //NGDirectionDialog m_directionDialog;
   // [HideInInspector] public NGBusinessObjective m_showingObjective;

    public NGBusinessDecision CurrentDecision => (m_currentDecision >= 0 && m_currentDecision < m_businessDecisions.Count) ? m_businessDecisions[m_currentDecision] : null;
    public bool HasShownInMessage => m_hasShownInMessage || m_inMessage.IsNullOrWhiteSpace();
	public bool IsLastDecision => m_currentDecision == m_businessDecisions.Count;
    public List<NGBusinessDirection> NextAdvisors
    {
        get
        {
            var results = new List<NGBusinessDirection>();
            var nextAdvisors = m_nextAdvisor.Split('|', ';');
            foreach (var na in nextAdvisors)
            {
                var a = s_advisors.Find(o => o.m_indexer == na);
                if (a != null)
                {
                    results.Add(a);
                }
            }
            return results;
        }
    }
    public static List<NGBusinessDirection> s_advisors = new List<NGBusinessDirection>();

    public static List<NGBusinessDirection> LoadInfo()
    {
/*        if(NGBusinessDecisionManager.s_loadInfoFromCSV)
            s_advisors = ReactCSVImport.NGLoadCSVFile<NGBusinessDirection>(XLSName, (s) => { return new NGBusinessDirection(); });
        else
            s_advisors = NGKnack.ImportKnackInto<NGBusinessDirection>((s) => { return new NGBusinessDirection(); });*/
    //    s_advisors = NGKnack.ImportKnackInto<NGBusinessDirection>();
        return s_advisors;
    }
    public void ActivateDecisions()
    {
        //Count number of steps
        m_maxSteps = 0;
        foreach (var d in m_businessDecisions)
            m_maxSteps += 1;
        if(m_inMessage.IsNullOrWhiteSpace())
            m_state = State.UpdateDecisions;
        else
            m_state = State.ShowInMessage;
        TriggerDialogs();
    }

    public bool Update()
    {
        switch(m_state)
        {
            case State.Activate:
                ActivateDecisions();
                break;
            case State.ShowInMessage:
   //             if (m_directionDialog == null)
                    m_state = State.UpdateDecisions;
                break;
            case State.ShowOutMessage:
	//			if (m_directionDialog == null)
					m_state = State.FinishedDirection;
                break;
			case State.ShowDirectionDecision:
	//			if (m_directionDialog == null)
					m_state = State.UpdateDecisions;
				break;
            case State.UpdateDecisions:
                UpdateDecisions();
                break;
            case State.TriggeredDecision:
                if (NGBusinessDecisionDialog.Me == null || NGBusinessDecisionDialog.Me.m_cards.Count == 0)
                    m_state = State.UpdateDecisions;
                break;
            case State.FinishedDirection:
                return true;
        }
        return false;
    }
    public void UpdateDecisions()
    {
        if (m_currentDecision >= m_businessDecisions.Count)
            FinishedDecisions();
		if (CurrentDecision == null) return;
        if(CheckForTypeTrigger(CurrentDecision.m_type))
            TriggerCurrentDecision();

    }

    void FinishedDecisions()
    {
        if (m_outMessage.IsNullOrWhiteSpace())
        {
            m_state = State.FinishedDirection;
            return;
        }
        m_state = IsLastDecision ? State.ShowDirectionDecision : State.ShowOutMessage;
        TriggerDialogs();
    }

    public bool CheckForTypeTrigger(string _type)
    {
        return CurrentDecision.IsTriggered;
    }

    public bool NextBusinessDecision()
    {
        Debug.Log("Business direction activate decision");
        m_currentDecision++;
        
        if (m_currentDecision >= m_businessDecisions.Count)
        {
            FinishedDecisions();
        }
        else
        {
 //           m_state = State.UpdateDecisions;
            CurrentDecision.Activate();
         //   m_showingObjective = NGBusinessObjective.Create(m_advisor, CurrentDecision, m_stepCount, m_maxSteps);
        }

        return false;
    }

    public void TriggerCurrentDecision()
    {
        //if (m_showingObjective)
       // {
       //     m_showingObjective.DestroyMe();
       //     m_showingObjective = null;
       // }
        m_state = State.TriggeredDecision;
        TriggerDialogs();
    }

    public void AcceptDirectionGift(NGBusinessGift _gift)
    {
#if OldBusinessFlow
        if (m_directionDialog)
        {
            m_directionDialog.DestroyMe();
            m_directionDialog = null;
        }
#endif
        ClickedGift(_gift);
    }

    public void ClickedGift(NGBusinessGift _gift)
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        string advisorName = "";
        var direction = _gift.GetFirstDirection(out advisorName);
        if (direction == null) { Debug.LogError($"Cant find direction for {_gift.m_cardPower}"); return; }
        var characterIndex = NGDirectionCharacter.FindIndex(advisorName);
        if (characterIndex == -1) { Debug.LogError($"Advisor '{advisorName}' not found"); return; }
        NGDirectionCharacter.m_directionCharacter = characterIndex;
        NGDirectionCharacter.DirectionCharacter.m_currentDirection = NGDirectionCharacter.DirectionCharacter.m_directions.IndexOf(direction);

 /*       var colonSplit = _gift.m_cardPower.Split(':', '|');
        var advisorName = colonSplit[0].Trim();
        float partNumber = -1f;
        if(colonSplit.Length > 1)
        {
            var partSplit = colonSplit[1].Split(' ', '>');
            if (partSplit.Length > 1) float.TryParse(partSplit[1].Trim(), out partNumber);
            else float.TryParse(partSplit[0].Trim(), out partNumber);
        }
        var characterIndex = NGDirectionCharacter.FindIndex(advisorName);
        if(characterIndex == -1) { Debug.LogError($"Advisor '{_gift.m_cardPower}' not found"); return; }
        NGDirectionCharacter.m_directionCharacter = characterIndex;
        if (partNumber != -1f)
        {
            bool foundPart = false;
            for(int i = 0; i < NGDirectionCharacter.DirectionCharacter.m_directions.Count; i++)
            {
                if(NGDirectionCharacter.DirectionCharacter.m_directions[i].m_partNumber == partNumber)
                {
                    foundPart = true;
                    NGDirectionCharacter.DirectionCharacter.m_currentDirection = i;
                    break;
                }
                if(foundPart == false) { Debug.LogError($"Invalid Part {foundPart} in {_gift.m_cardPower}"); }
            }
        }*/
    }

    int m_recievedGifts;
    public bool DecisionGiftRecived(int _maxRecievedGifts)
    {
        m_recievedGifts++;
        if (m_recievedGifts == _maxRecievedGifts)
        {
            NextBusinessDecision();
            m_recievedGifts = 0;
            return true;
        }
        return false;
    }

    public void ClickedGo()
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        //m_directionDialog = null;
        //m_state = State.UpdateDecisions;
		NextBusinessDecision();
    }

    public void ClickedGiftAccept(NGBusinessDirection _direction)
    {
        HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
        var index = NGBusinessDecisionManager.Me.m_businessDirections.IndexOf(_direction);
        m_currentDecision = index;
    }

    void TriggerDialogs()
    {
        switch (m_state)
        {
            case State.Activate:
                break;
            case State.ShowInMessage:
 //               if (m_directionDialog == null)
 //                   m_directionDialog = NGDirectionDialog.Create(this, true);
                break;
            case State.ShowOutMessage:
			case State.ShowDirectionDecision:
  //              if (m_directionDialog == null)
   //                 m_directionDialog = NGDirectionDialog.Create(this, false);
                break;
            case State.UpdateDecisions:
                //if(m_showingObjective == null)
                //    m_showingObjective = NGBusinessObjective.Create(m_advisor, CurrentDecision, m_stepCount, m_maxSteps);
                break;
            case State.TriggeredDecision:
                NGBusinessGiftsPanel.CreateOrCall(CurrentDecision.m_gifts);
/*                if (NGBusinessDecisionDialog.Me == null)
                    NGBusinessDecisionDialog.Create(this, CurrentDecision);
                else
                    NGBusinessDecisionDialog.Me.SetUpForBusinessDecision(this, CurrentDecision, null, CurrentDecision.m_gifts);*/
                break;
            case State.FinishedDirection:
                break;
        }
    }

    public void Save(ref SaveContainers.SaveCountryside _s)
    {
        _s.m_saveBusinessDecision.m_currentDecision = m_currentDecision;
        _s.m_saveBusinessDecision.m_currentMaxSteps = m_maxSteps;
        _s.m_saveBusinessDecision.m_characterState = m_state.ToString();
		_s.m_saveBusinessDecision.m_currentBusinessLevel = NGBusinessDecisionManager.Me.m_currentLevel;

        if (CurrentDecision != null)
        {
            CurrentDecision.Save(ref _s);
        }
    }

    public void SetupFromLoad(SaveContainers.SaveCountryside _l)
    {
        m_currentDecision = _l.m_saveBusinessDecision.m_currentDecision;
        m_maxSteps = _l.m_saveBusinessDecision.m_currentMaxSteps;
        Enum.TryParse(_l.m_saveBusinessDecision.m_characterState, out m_state);
		NGBusinessDecisionManager.Me.m_currentLevel = _l.m_saveBusinessDecision.m_currentBusinessLevel;


        if (CurrentDecision != null)
        {
            CurrentDecision.SetupFromLoad(_l);
   //         if(CheckForTypeTrigger(CurrentDecision.Type) == false && m_showingObjective == null)
     //           m_showingObjective = NGBusinessObjective.Create(m_advisor, CurrentDecision, m_stepCount, m_maxSteps);
        }
    }


}
