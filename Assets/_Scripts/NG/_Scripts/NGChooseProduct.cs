using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using TMPro;
using UnityEngine.UI;

using Product = GameState_Product;
/*
public class NGChooseProduct : MonoBehaviour
{
    public TMP_Text m_title;
    public TMP_Text m_explain;
    public Transform m_productHolder;
    public Transform m_salesmanHolder;
	public static bool s_tutorialHideButtons = false;
	public int m_goldCost = 20;
	NGFactory m_building;
	Product m_selectedProduct;

	[SerializeField]
	private TMP_Text adButtonText;

	private int m_numDesignsAvail = 0;// analytics

	private List<ProductUIController> cachedProductUIControllers = new List<ProductUIController>();

	void Start()
	{
		ActivateProducts();

	}

	void	ActivateProducts()
	{
		bool firstProductFlag = true, needClear = true;
		ProductUIController firstProductCard = null;

		

		{
			List<Product> productsToShow = new List<Product>();

			var pp = ProductHelper.AllProducts; 

			for( int i = 0; i < pp.Count; ++i )
			{
				Product ps = pp[i];
				//Match pp.ProductLineType with factory
				if(needClear)
				{
					needClear = false;
					m_productHolder.DestroyChildren();
				}
				productsToShow.Add(ps);
			}

			productsToShow.Sort((x, y) => x.Stock().CompareTo(y.Stock()));
			foreach(var product in productsToShow)
			{
				var productCard = InfoPlaqueManager.Me.LoadUI<ProductUIController>();
				productCard.Activate(product, false, m_productHolder);
				if(firstProductFlag)
				{
					firstProductFlag = false;
					firstProductCard = productCard;
				}
				if(m_selectedProduct == product) firstProductCard = productCard;

				cachedProductUIControllers.Add(productCard);
			}

			m_numDesignsAvail = productsToShow.Count;
		}
		if(firstProductCard != null) ClickedProduct(firstProductCard);
		else if (needClear) ClickedProduct(m_productHolder.GetComponentInChildren<ProductUIController>());
	}

    public void DestroyMe()
    {
        Destroy(gameObject);
    }

    public void ClickedProduct(ProductPanelDialog _what)
    {
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
		m_selectedProduct = _what.m_product;
		var button = _what.GetComponentInChildren<Button>(); 
        ToggleButton(m_productHolder, button);
    }

	public void ClickedProduct(HoverProductCard _what)
    {
		if(_what == null) return;
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
		m_selectedProduct = _what.m_product;
		var button = _what.GetComponentInChildren<Button>(); 
        ToggleButton(m_productHolder, button);
    }

	public void ClickedProduct(ProductUIController _what)
    {
		if(_what == null) return;
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
		m_selectedProduct = _what.m_product;
		var button = _what.GetComponentInChildren<Button>(); 
        ToggleButton(m_productHolder, button);
    }

    public void ToggleButton(Transform _holder, Button _button)
    {
        var buttons = _holder.GetComponentsInChildren<Button>();
        for(int i = 0; i < buttons.Length; i++)
            buttons[i].interactable = true;
        if(_button) _button.interactable = false;         
    }

    public void ClickedGo()
    {
		//		AudioClipManager.PlaySoundAndForgetS("SMALLCLICK");
		HapticInterface.PlayHaptic(HapticInterface.EHapticPreset.ButtonTap);
		m_building.SetProduct(m_selectedProduct);
		DestroyMe();
    }

	public void EnterDesignTable (NGFactory _factory) 
	{
		if (m_selectedProduct != null)
			m_selectedProduct.OpenInDesignTable(_factory);
		else 
			DesignTableManager.LoadProductLaunch(_factory);
	}


    void Activate(NGFactory _building)
    {
		m_building = _building;

		m_selectedProduct = m_building.GetProduct();
    }

    public static NGChooseProduct Create(NGFactory _building)
    {
        var go = Instantiate(NGManager.Me.m_NGChooseProductPrefab.gameObject, GameManager.Me.TownCanvas.transform);
        var cpd = go.GetComponent<NGChooseProduct>();
        cpd.Activate(_building);
        return cpd;
    }
}
*/