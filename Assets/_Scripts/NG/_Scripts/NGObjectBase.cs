using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;

using ProductDesign = GameState_Design;
using Product = GameState_Product;

#region Enums

public enum PeepActions
{
	None,
	Idle,
	Working,
	DeliverToInput,
	DeliverToVan,
	ReturnToWork,
	ReturnToRest,
	Resting,
	MoveToReaction,
	Reacting,
	WaitingForWork,
	WaitingForHome,
	Ejected,
	CollectingPay,
	CollectPickup,
	Throw,
	Flee,
	Chop,
	DeliverToOutput,
	CollectingItem,
	Hodor,
	HangOut,
	Despawn,
	DespawnRun,
	Last,
}

public enum PeepInsideActions
{
	None,
	FromInside,
	FromOutside,
}

	#endregion Enums

public class NGObjectBase : NGLegacyBase 
{
	#region Static Properties
	
	protected static Dictionary<int, NGObjectBase> s_idLookup = new Dictionary<int, NGObjectBase>();	
	protected static int s_objectCount = 1;
	
	#endregion Static Properties
	
	#region Virtual Properties
	virtual public string WhatAmIText { get { return Name.MakeNice(); } }       
	virtual public string WhoAmIText { get { return WhatAmIText; } }
	#endregion Virtual Properties
	
	#region Properties
	[Header("NGMovingObject")]
	public bool m_debugMe = false;
	public int m_ID;
	[Header("NGObjectBase")]
	[HideInInspector] public NGCommanderBase m_originator;
	#endregion Properties

	#region Virtual Functions

	virtual protected void OnDestroy() {}
	protected virtual void OnEnable() {}
	protected virtual void OnDisable() {}
	virtual public bool IsOkayToGrab() { return false; }
	
	#endregion Virtual Functions
	
	#region Functions
	
	public void	SetObjectName(string _name) { Name = _name; }
	
	#endregion Functions

}
