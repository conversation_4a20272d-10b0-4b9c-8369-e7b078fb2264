using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BallooRope : MonoBehaviour
{

	[SerializeField]
	GameObject partPrefab, parentObject;

	[SerializeField]
	[Range(1, 1000)]
	int length = 1;

	[SerializeField]
	float partDistance = .21f; 

	[SerializeField]
	bool reset, spawn, snapFirst, snapLast;

    void Update()
    {
		if(reset)
		{
			foreach(var tmp in GameObject.FindGameObjectsWithTag("Player"))
			{
				Destroy(tmp);
			}
			reset = false;
		}
		if(spawn)
		{
			Spawn();
			spawn = false;
		}
    }

	public void Spawn()
	{
		var count = (int) (length/partDistance);
		for( int i = 0; i < count; i++)
		{
			var go = Instantiate(partPrefab, new Vector3 (transform.position.x, transform.position.y + partDistance * (i +1), transform.position.z), Quaternion.identity, parentObject.transform);
			go.transform.eulerAngles = new Vector3(180, 0 ,0);
			go.name = parentObject.transform.childCount.ToString();
			if(i == 0) 
				Destroy(go.GetComponent<CharacterJoint>());
			else
				go.GetComponent<CharacterJoint>().connectedBody = parentObject.transform.Find((parentObject.transform.childCount -1).ToString()).GetComponent<Rigidbody>();

		}
	}
}
