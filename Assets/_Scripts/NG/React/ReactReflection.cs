using System;
using System.Reflection;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.Globalization;
using System.Runtime.InteropServices;

public class ReactReflection {

	public static PropertyInfo GetProperty(object _object, string _name)
    {
		var property = _object.GetType().GetProperty(_name, BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public | BindingFlags.Static | BindingFlags.IgnoreCase);
		return property;
	}

	public static FieldInfo GetField(object _object, string _name)
    {
		var field = _object.GetType().GetField(_name, BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public | BindingFlags.Static | BindingFlags.IgnoreCase);
		if(field == null)
			field = _object.GetType().GetField("m_"+_name, BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public | BindingFlags.Static | BindingFlags.IgnoreCase);
		return field;
	}

	public static object NGDecodeAndSetField(object _object, string _field, string _value, string _location = "", bool _debugError = true)
	{
		if (_field.IsNullOrWhiteSpace()) return null;
		try
		{
			var field = GetField(_object, _field);
			if(field == null) 
			{
				var fields = _object.GetType().GetFields();
				if(_debugError)
					Debug.LogError($"Cell {_object.ToString()}.{_field}={_value} not valid Field({_location})"); 
				return null; 
			}

			var fieldValue = field.GetValue(_object);
			var isList = fieldValue is IList;
			var isArray = fieldValue is Array;
			var isEnum = field.FieldType.IsEnum;
			if (isArray)
			{
				var iArray = fieldValue as Array;
				var prams = _value.Split(';', '|');
				for (int i = 0; i < iArray.Length; i++)
				{
					if (i < prams.Length)
						iArray.SetValue(prams[i], i);
				}
				return iArray;
			}
			else if (isList)
			{
				var iList = fieldValue as IList;
				var prams = _value.Split(';', '|');
				foreach (var p in prams)
				{
					iList.Add(p);
				}
				return iList;
			}
			else if (isEnum)
			{
				var evalue = Enum.Parse(field.FieldType, _value);
				field.SetValue(_object, evalue);
				return evalue;
			}
			else
			{
				if (_value.Contains("%"))
				{
					var split = _value.Split('%');
					var value = (float)Convert.ChangeType(split[0], field.FieldType, CultureInfo.InvariantCulture) / 100f;
					field.SetValue(_object, value);
					return value;
				}
				else
				{
#if UNITY_EDITOR
					if (field.FieldType.Name == "Single" && _value.Contains(','))
						Debug.LogError($"Comma in float string, probably a locale issue");
#endif
					var value = Convert.ChangeType(_value, field.FieldType, CultureInfo.InvariantCulture);
					field.SetValue(_object, value);
					return value;
				}
			}
		}
		catch (Exception e)
		{
			var message = $"Cell {_object.ToString()}.{_field}={_value} not valid ";
			if (_location.IsNullOrWhiteSpace() == false)
				message += $"Cell({_location}) ";
			Debug.LogError($"{message}::{e}");
			return null;
		}
	}
	public static object NGDecodeAndSetProperty(object _object, string _property, string _value, string _location = "")
    {
		return NGDecodeAndSetProperty(_object.GetType(), _object, _property, _value, _location);
    }
	public static object NGDecodeAndSetProperty(System.Type _type, object _object, string _property, string _value, string _location = "")
	{
		var type = (_object == null) ? _type : _object.GetType();
		if (_property.IsNullOrWhiteSpace()) return null;
		var property = type.GetProperty(_property, BindingFlags.Instance | BindingFlags.NonPublic | BindingFlags.Public | BindingFlags.Static | BindingFlags.IgnoreCase);
		if (property == null) { return null; }

		var propertyValue = property.GetValue(_object);
		var isList = propertyValue is IList;
		var isArray = propertyValue is Array;
		var isEnum = property.PropertyType.IsEnum;
		if (isArray)
		{
			var iArray = propertyValue as Array;
			var prams = _value.Split(';', '|');
			for (int i = 0; i < iArray.Length; i++)
			{
				if (i < prams.Length)
					iArray.SetValue(prams[i], i);
			}
			return iArray;
		}
		else if (isList)
		{
			var iList = propertyValue as IList;
			var prams = _value.Split(';', '|');
			foreach (var p in prams)
			{
				iList.Add(p);
			}
			return iList;
		}
		else if (isEnum)
		{
			var evalue = Enum.Parse(property.PropertyType, _value);
			property.SetValue(_object, evalue);
			return evalue;
		}
		else
		{
			if (_value.Contains("%"))
			{
				var split = _value.Split('%');
				var value = (float)Convert.ChangeType(split[0], property.PropertyType, CultureInfo.InvariantCulture) / 100f;
				property.SetValue(_object, value);
				return value;
			}
			else
			{
				var value = Convert.ChangeType(_value, property.PropertyType, CultureInfo.InvariantCulture);
				property.SetValue(_object, value);
				return value;
			}
		}
	}

	public static object NGDecodeAndSetAny(object _object, string _propertyOrField, string _value, string _location = "")
	{
		var returnObject = NGDecodeAndSetProperty(_object.GetType(), _object, _propertyOrField, _value, _location);
		if(returnObject == null)
			returnObject = NGDecodeAndSetField(_object, _propertyOrField, _value, _location);
		return returnObject;
	}	


	public static string MakePropertyString<T>(bool _includeType = true)
	{
		var properties = typeof(T).GetProperties(BindingFlags.Static | BindingFlags.Public);
		var propertiesString = "";
		foreach (var p in properties)
		{
			//if(p.PropertyType.Name.Equals(“Bool”))
			if(_includeType)
				propertiesString += $"{ p.PropertyType.Name}:{ p.Name}:{p.GetValue(null).ToString()}\n";
			else
				propertiesString+=  $"{ p.Name}:{p.GetValue(null).ToString()}\n";
		}
		propertiesString = propertiesString.TrimEnd('\n');
		return propertiesString;
	}
	
	
	public static void DecodePropertyStrings<T>(string _properties)
	{
		if (_properties.IsNullOrWhiteSpace()) return;
		var split = _properties.Split('\n');
		foreach (var s in split)
		{
			var colonSplit = s.Split(':', '=');
			string left, right;
			switch (colonSplit.Length)
            {
	            case 1:
		            left = colonSplit[0].Trim();
		            var prop = typeof(T).GetProperty(left);
					if (prop == null)
					{
						Debug.LogError("The value entered is not handled. Check your typing!");
						right = "";
					}
					else
					{
						var t = (bool)prop.GetValue(null);
						right = (!t).ToString();
					}
		            break;
				case 2: left = colonSplit[0].Trim(); right = colonSplit[1].Trim(); break;
				case 3: left = colonSplit[1].Trim(); right = colonSplit[2].Trim(); break;
				default:
					Debug.LogError($"NGUnlocks: Bad Load string of '{s}'"); 
					continue;
			}
			ReactReflection.NGDecodeAndSetProperty(typeof(T), null, left, right, $"NGSaveImport::{s}");
		}
	}

	public static void CopyClass<T>(T copyFrom, T copyTo)
	{
		if (copyFrom == null || copyTo == null)
			Debug.LogError("Must not specify null parameters");

		var fields = copyFrom.GetType().GetFields();
		foreach (var p in fields)
		{
			object copyValue = p.GetValue(copyFrom);
			p.SetValue(copyTo, copyValue);
		}
	}

	public static object DecodeLine(List<object> _source, string _line)
	{
		object ret = null;
		foreach (var s in _source)
		{
			if(s == null) continue;
			ret = DecodeLine(s, _line);
			if (ret != null)
				return ret;
		}

		return null;
	}

	public static void DecodeLines(object _source, string _lines)
	{
		if (_lines.IsNullOrWhiteSpace()) return;
		var lfSplit = _lines.Split('\n');
		foreach (var s in lfSplit)
		{
			if (s.IsNullOrWhiteSpace()) continue;
			ReactReflection.DecodeLine(_source, s);
		}
	}

	private static Dictionary<string, MethodInfo> s_methodInfoCache = new(); 
	public static MethodInfo LookupMethod(object _source, string _name)
	{
		var type = _source.GetType();
		var key = $"{type}::{_name}";
		if (s_methodInfoCache.TryGetValue(key, out var info)) return info;
		var method = _source.GetType().GetMethod(_name, BindingFlags.Public|BindingFlags.Instance);
		s_methodInfoCache[key] = method;
		return method;
	}
	
	/// <summary>
	/// Decodes any string into Method, Property or field. These must be public
	/// NOTE: Does not support Ambiguous functions
	/// </summary>
	/// <returns>The value.</returns>
	/// <param name="_source">Source where Method, Property or field is found </param>
	/// <param name="_line">String supports '=' & ().</param>
    public static object DecodeLine(object _source, string _line)
    {
        var bsplit = _line.SplitFirst('(', ')');
        var name = bsplit[0].Trim();
        if (bsplit.Count < 2)
        {
	        return SetAnyFieldOrProperty(_source, _line);
        }
        var methord = LookupMethod(_source, name);
        if (methord != null)
        {
            var prames = methord.GetParameters();
            if (prames.Length > 0)
            {
                if (bsplit.Count > 1)
                {
                    var csplit = bsplit[1].Split(',');
                    object[] pram = new object[prames.Length];
                    for (var i = 0; i < prames.Length; i++)
                    {
                        var value = Convert.ChangeType(csplit[i].Trim(), prames[i].ParameterType, CultureInfo.InvariantCulture);
                        pram[i] = value;
                    }

                    if (methord.IsStatic == false)
                    {
	                    var ret =methord.Invoke(_source, pram);
	                    return ret;
                    }
                    else
                    {
	                    var ret =methord.Invoke(null, pram);
	                    return ret;
                    }
                }
                else
                {
                    Debug.LogError($"{name} takes {prames.Length} parameters but {_line} has none");
                    return null;
                }
                
            }
            else
            {
	            if (methord.IsStatic == false)
	            {
		            var ret = methord.Invoke(_source, null);
		            return ret;
	            }
	            else
	            {
		            var ret = methord.Invoke(null, null);
		            return ret;
	            }
            }
        }
        else
        {
            Debug.LogError($"{name} no such method, Property or Field");
            return null;
        }        
    }

	public static object SetFieldOrProperty(object _source, string _name, object _value, bool _showErrors = true)
	{
		var type = _source.GetType();
		var field = type.GetField(_name);

		if (field != null)
		{
			field.SetValue(_source, _value);
		}
		else
		{
			var property = type.GetProperty(_name);	
			if (property != null)
			{
				property.SetValue(_source, _value);
			}
			else
			{
				if(_showErrors)
					Debug.LogError($"{_source.ToString()}.{_name} Not Found");
				return null;
			}
		}

		return _value;
	}

	public static object SetAnyFieldOrProperty(object _source, string _name, Type _requiredAttribute = null)
	{
		var ostring = _name.Split('=');
		var name = ostring[0].Trim();
		Type type;
		var isField = true;
		PropertyInfo _propertyInfo = null;
		if(_source == null || _source.GetType() == null)
			Debug.LogError("Bad Source");
		var _fieldInfo = _source.GetType().GetField(name);
		
		if (_fieldInfo == null)
		{
			isField = false;
			_propertyInfo = _source.GetType().GetProperty(name);
			if (_propertyInfo == null)
			{
				Debug.LogError($"{_source} no such property as {_name}");
				return null;
			}
			type = _propertyInfo.PropertyType;
		}
		else
		{
			type = _fieldInfo.FieldType;
		}

		if(_requiredAttribute != null)
		{
			if(isField)
			{
				if(_fieldInfo.GetCustomAttribute(_requiredAttribute) == null)
				{
					Debug.LogError($"Trying to set field: {_fieldInfo.Name} without necessary attribute: {_requiredAttribute.Name}");
					return null;
				}
			}
			else
			{
				if(_propertyInfo.GetCustomAttribute(_requiredAttribute) == null)
				{
					Debug.LogError($"Trying to set property: {_propertyInfo.Name} without necessary attribute: {_requiredAttribute.Name}");
					return null;
				}
			}
		}
		
		var osplit = _name.Split('=');
		var isEnum = type.IsEnum;
		var isList = type.IsGenericType && type.GetGenericTypeDefinition() == typeof(List<>);
		if (osplit.Length > 1)
		{
			if (isEnum)
			{
				var enumValue = Enum.Parse(type, osplit[1]);
				if(isField)
					_fieldInfo.SetValue(_source, enumValue);
				else
					_propertyInfo.SetValue(_source, enumValue);
				return enumValue;
			}

			if (isList)
			{
				var csplit = osplit[1].Split('{', '}');
				var elements = csplit[0];
				if (csplit.Length > 1) elements = csplit[1];
				var commaSplit = elements.Split(',');
				var lvalue = (isField) ? _fieldInfo.GetValue(_source) as IList : _propertyInfo.GetValue(_source) as IList;
				var ga = type.GetGenericArguments();
				foreach (var c in commaSplit)
				{
					var lfvalue = Convert.ChangeType(c, ga[0], CultureInfo.InvariantCulture);
					lvalue.Add(lfvalue);
				}
				return lvalue;
			}

			object fvalue;
			if (osplit[1].Contains("%"))
			{
				var split = osplit[1].Split('%');
				fvalue = (float)Convert.ChangeType(split[0], type, CultureInfo.InvariantCulture) / 100f;
			}
			else
			{
				fvalue = Convert.ChangeType(osplit[1], type, CultureInfo.InvariantCulture);
			}
			if(isField)
				_fieldInfo.SetValue(_source, fvalue);
			else
				_propertyInfo.SetValue(_source, fvalue);
			return fvalue;
		}
		else
		{
			var fret = (isField) ? _fieldInfo.GetValue(_source) : _propertyInfo.GetValue(_source);
			return fret;
		}
	}

	public static string GetFunction(string _from)
	{
		var bSplit = _from.Split('(', ')');
		if (bSplit.Length != 3)
		{
			Debug.LogError($"incorrect brackets in {_from}");
			return _from;
		}
		return bSplit[0];
	}
	public static string[] GetParameters(string _from)
	{
		var bSplit = _from.Split('(', ')');
		if (bSplit.Length != 3)
		{
			Debug.LogError($"incorrect brackets in {_from}");
			return null;
		}

		var commaSplit = bSplit[1].Split(',');
		return commaSplit;
	}
	
	
	public static float GetSetValueWithOperator(object _source, string _command, bool _setValue = false)
	{
		//EG Upgrade(MaxWorkers + 1)
		var operators = new string[] {"/", "*", "-", "+", "%"};
		var prams = ReactReflection.GetParameters(_command);
		if (prams.Length != 1)
		{
			Debug.LogError($"{_command} bad syntax");
			return -9999f;
		}

		foreach (var o in operators)
		{
			var s = prams[0].Split(o);
			if (s.Length == 3)
			{
				var propertyName = s[0].Trim();
				var pi = _source.GetType().GetProperty(propertyName);
				if (pi == null) continue;
				var originalValue = (float)pi.GetValue(_source);
				float.TryParse(s[2].Trim(), out var byValue);
				var newValue = 0f;
				switch (o)
				{
					case "/":
						newValue = originalValue / byValue;
						break;
					case "*":
						newValue = originalValue * byValue;
						break;
					case "-":
						newValue = originalValue - byValue;
						break;
					case "+":
						newValue = originalValue + byValue;
						break;
					case "%":
						newValue = originalValue * byValue/100f;
						newValue += originalValue;
						break;
					default:
						Debug.LogError($"in {_command} operator {o} not supported");
						return -9999f;
				}
				if(_setValue)
					pi.SetValue(_source, newValue);
				return newValue;
			}
		}
		return -9999f;
	}

}

