using System;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class MAQuestWaspProblem : MAQuestBase
{
    protected const string c_questCharacterName = "Lumberjack";
    protected const string c_stockBuildingName = "QuestWaspProblemPile";

    [SerializeField][Range(0,50)]
    private int m_woodResourceRewardQuantity;
    
    [SerializeField][ReadOnlyInspector]
    private SaveLoadQuestWaspProblemContainer m_saveData = null;
    
    [SerializeField]
    private Transform m_treeHolder;
   
    private List<ExplodeInteraction> m_waspNests;
    private MACharacterBase m_usedHero;
    private bool m_isFirstAttempt = true;
    private MAFlowCharacter m_lumberJack = null;
    private MABuilding m_woodSpot = null;
    
    private enum LumberJackState { Waiting, Gathering, Done }
    private LumberJackState m_lumberJackState = LumberJackState.Waiting;
    private List<string> m_choppedTreeNames = new();
    
    public AkSwitchHolder m_onAcceptQuestMusic = new();
    public AkSwitchHolder m_onHeroRunningMusic = new();
    public AkSwitchHolder m_onHeroWalkingMusic = new();
    public AkSwitchHolder m_onHeroDispossessedMusic = new();
    
    // private bool m_isHeroWalkingMusic = false;
    private bool m_isHeroRunningMusic = false;
    private bool m_isHeroPossessedMusic = false;
    private bool m_suspendHeroMusic = false;
    
    protected override void Awake()
    {
        base.Awake();
        AddWaspNests();
    }

    private List<int> m_treeIds = new List<int>();

    public void LumberJack()
    {
        // End quest music
        m_onHeroDispossessedMusic.SetAsOverride();

        InitReferences();

        if (m_lumberJack == null || m_woodSpot == null)
        {
            return;
        }

        var bcGather = Gatherer;
        if (bcGather != null)
        {
            m_lumberJack.m_optionalInteractionBuilding = m_woodSpot;

            bcGather.m_characters.Add(m_lumberJack);
            bcGather.m_treesParent = m_treeHolder.gameObject;
            
            List<Vector3> positions = new();
            if (bcGather.m_treesParent != null)
            {
                foreach (Transform tr in bcGather.m_treesParent.transform)
                {
                    positions.Add(tr.transform.position);
                }
            }

            var treeObjects = TreeFinder.FindTreeHolderObjectsAt(1.5f, positions.ToArray());
            foreach (GameObject go in treeObjects)
            {
                TreeHolder th = go.GetComponentInChildren<TreeHolder>();
                if (th != null && bcGather.m_treeHolders.Contains(th) == false)
                {
                    bcGather.m_treeHolders.Add(th);
                }
            }

            if (bcGather.Building == null)
            {
                Debug.LogError($"{GetType().Name} - BcGather.Building is null. Are we using the actual building + save data or the placeholder accidentally?");
            }
            else
            {
                bcGather.m_treeHolders.Sort((x, y) =>
                {
                    Vector3 bPos = bcGather.Building.transform.position;
                    float dist1 = (x.transform.position - bPos).sqrMagnitude;
                    float dist2 = (y.transform.position - bPos).sqrMagnitude;
                    if (dist1 < dist2) return -1;
                    if (dist1.Equals(dist2)) return 0;
                    return 1;
                });
            }
            //bcGather.AskForInputStock();

            // foreach (var componentsInChild in m_treeHolder.GetComponentsInChildren<BCChopObject>())
            // {
            //     componentsInChild.m_collectableResource =
            //         componentsInChild.GetComponentInChildren<MACollectableResource>();
            //     componentsInChild.RefreshTreeObjectActive();
            // }
        }
        m_lumberJackState = LumberJackState.Gathering;
    }

    public void DebugAddResources()
    {
        InitReferences();
        var stock = GetStockComponent();
        AddWood(stock, 10);
    }

    protected override void OnPostLoad()
    {
        // do not call base OnPostLoad(), .MOA file will create quest giver
        InitReferences();
    }
    
    public override void OnAcceptChallenge()
    {
        m_isFirstAttempt = false;
        InitReferences();
        m_onAcceptQuestMusic.SetAsOverride();
        m_isHeroRunningMusic = false;
    }
    
    public BCQuestActionGatherer Gatherer
    {
        get
        {
            var bcGather = m_woodSpot.m_components.Find(x => x is BCQuestActionGatherer) as BCQuestActionGatherer;
            if (bcGather == null)
            {
                bcGather = m_woodSpot.GetComponentInChildren<BCQuestActionGatherer>();

                if (bcGather == null)
                {
                    bcGather = m_woodSpot.m_components.Find(x => x is BCQuestActionGatherer) as BCQuestActionGatherer;
                }
            }
            return bcGather;
        }
    }
    
    public List<TreeHolder> TreeHolders => Gatherer?.m_treeHolders;
    
    private void Update()
    {
        if (m_lumberJackState == LumberJackState.Gathering)
        {
            if (m_lumberJack != null)
            {
                if (m_lumberJack.m_state == NGMovingObject.STATE.MA_DECIDE_WHAT_TO_DO)
                {
                    OnDelivered();
                }
            }
        }

        if (m_status == QuestStatus.InProgress && !m_suspendHeroMusic)
        {
            CheckHeroMusicState();
        }
    }

    private void OnDelivered()
    {
        var treeHolders = TreeHolders;
        if (treeHolders == null || treeHolders.Count == 0)
        {
            Finish();
            return;
        }
        // var trees = m_treeHolder.GetComponentsInChildren<BCChopObject>(true);
        // if (trees == null || trees.Length == 0)
        // {
        //     m_treeIds
        //     Finish();
        //     return;
        // }
        //
        List<TreeHolder> trees = new();
        List<TreeHolder> choppedTrees = new();
        List<TreeHolder> actualTrees = new();
        foreach (var tree in treeHolders)
        {
            var chop = tree.GetComponentInChildren<BCChopObject>();
            trees.Add(tree);
            if (chop != null && chop.IsChoppedDown)
            {
                //tree.m_dawnsUntilUprootedTreeRegrowth = -1;
                choppedTrees.Add(tree);
                string treeName = chop.GetComponent<TreeHolder>().Name;
                if (m_choppedTreeNames.Contains(treeName) == false)
                {
                    m_choppedTreeNames.Add(treeName);
                }
            }
            else
            {
                actualTrees.Add(tree);
            }
        }
                    
        if (actualTrees.Count == 0)
        {
            Finish();
        }
        else if(actualTrees.Count < trees.Count)
        {
            if (m_woodResourceRewardQuantity > 0)
            {
                // int perDelivery = Mathf.FloorToInt(m_woodResourceRewardQuantity / (float)trees.Length);
                // if (m_woodSpot.m_componentsDict.TryGetValue(typeof(BCQuestStockOut), out var stockOuts))
                // {
                //     BCQuestStockOut stock = GetStockComponent();
                //     AddWood(stock, perDelivery);
                // }
            }
        }
    }

    protected void Finish()
    {
        m_lumberJack.SetMoveToPosition(m_startingPosLumberJack);

        if (m_woodResourceRewardQuantity > 0)
        {
            if (m_woodSpot.m_componentsDict.TryGetValue(typeof(BCQuestStockOut), out var stockOuts))
            {
                BCQuestStockOut stock = GetStockComponent();
                int stockExists = stock.GetStock().GetTotalStock();
                int quantityLeft = m_woodResourceRewardQuantity - stockExists;
                AddWood(stock, quantityLeft);
            }
        }

        m_lumberJackState = LumberJackState.Done;
    }

    private BCQuestStockOut GetStockComponent()
    {
        if (m_woodSpot.m_componentsDict.TryGetValue(typeof(BCQuestStockOut), out var stockOuts))
        { 
            return stockOuts[0] as BCQuestStockOut;
        }
        return null;
    }

    private void AddWood(BCQuestStockOut _stock, int _quantityLeft)
    {
        for (int i = 0; i < _quantityLeft; i++)
        {
            _stock.CreateStock(NGCarriableResource.GetInfo("Wood"));
        }
    }
    
    private Vector3 m_startingPosLumberJack;
    
    public void InitReferences()
    {
        if (m_lumberJack == null)
        {
            m_lumberJack = MAFlowCharacter.FindCharacter(c_questCharacterName);
            if (m_startingPosLumberJack == Vector3.zero && ((m_saveData?.m_startingPosLumberJack ?? Vector3.zero) != Vector3.zero))
            {
                m_startingPosLumberJack = m_lumberJack != null ? m_lumberJack.transform.position : Vector3.zero;
            }
        }
        
        if (m_lumberJack != null)
        {
            m_lipSyncers["Lumberjack"] = m_lumberJack;

            var ls = m_lumberJack.SetLipSync(false);
            ls.m_deltaRot = -24.0f;
        }

        if (m_woodSpot == null)
        {
            m_woodSpot = MABuilding.FindBuilding(c_stockBuildingName, true);
            if (m_woodSpot == null && GameManager.Me.LoadComplete) Debug.LogError($"{GetType().Name} - InitWaspQuestReferences - Cannot find wood spot building'{c_stockBuildingName}'");
        }
    }
    
    private void AddWaspNests()
    {
        m_waspNests = new List<ExplodeInteraction>();

        foreach (Transform child in m_treeHolder)
        {
            ExplodeInteraction[] explodeInteractions = child.GetComponentsInChildren<ExplodeInteraction>();
            foreach (ExplodeInteraction explodeInteraction in explodeInteractions)
            {
                m_waspNests.Add(explodeInteraction);
            }
        }
    }
    
    private int WaspNestCount()
    {
        int count = 0;
        foreach (var waspNest in m_waspNests)
        {
            if (waspNest.isActiveAndEnabled) count++;
        }
        return count;
    }
    
    private void SetWaspNestsAttackable(bool _isAttackable)
    {
        foreach (var waspNest in m_waspNests)
        {
            waspNest.m_isAttackable = _isAttackable;
            
            Rigidbody rb = waspNest.GetComponent<Rigidbody>();
            if (rb != null)
            {
                rb.isKinematic = false;
            }
        }
    }

    private int WaspCount()
    {
        return FindObjectsOfType<MAQuestWasp>().Length;
    }

    private bool IsHeroDead()
    {
        // save the current possessed character
        if (GameManager.Me.PossessedCharacter != null && GameManager.Me.PossessedCharacter is MAHeroBase)
        {
            m_usedHero = (MAHeroBase)GameManager.Me.PossessedCharacter;
        }
        
        return m_usedHero != null && m_usedHero.IsAnyDeadState;
    }


    private void CheckHeroMusicState()
    {
        // Get current hero state values
        bool isHeroPossessed = false;
        bool isHeroRunning = false;

        if (GameManager.Me.PossessedCharacter != null && GameManager.Me.PossessedCharacter is MAHeroBase)
        {
            // Get current hero states
            isHeroPossessed = true;

            // Get movement thresholds
            float runSpeedThreshold = GameManager.Me.PossessedCharacter.m_possessionFwdWalkSpeed + ((GameManager.Me.PossessedCharacter.m_possessionFwdRunSpeed -
                                      GameManager.Me.PossessedCharacter.m_possessionFwdWalkSpeed) * 0.3f);
            
            // Check current speed against thresholds
            if (GameManager.Me.GetPossessedMoveSpeed() > runSpeedThreshold)
            {
                isHeroRunning = true;
            }

            // Check against previous hero states
            if (!isHeroRunning && m_isHeroRunningMusic)
            {
                m_onHeroWalkingMusic.SetAsOverride();
                m_isHeroRunningMusic = false;
            }
            
            if (isHeroRunning && !m_isHeroRunningMusic)
            {
                m_onHeroRunningMusic.SetAsOverride();
                m_isHeroRunningMusic = true;
            }
        }

        if (!isHeroPossessed && m_isHeroPossessedMusic)
        {
            m_onHeroDispossessedMusic.SetAsOverride();
            m_isHeroRunningMusic = true;
        }
        
        m_isHeroPossessedMusic = isHeroPossessed;
    }
    
    public override float QuestObjectiveValue(string _objective)
    {
        if (_objective.Contains("SmashAll"))
        {
            return WaspNestCount();
        }
        if (_objective.Contains("IsFirstAttempt"))
        {
            return m_isFirstAttempt ? 0.0f : 1.0f;
        }
        return 1.0f;
    }
    
    public override bool WaitForQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');

        if (split[0].Contains("AllWaspsGone"))
        {
            return WaspCount() < 1;
        }
        if (split[0].Contains("IsHeroDead"))
        {
            return IsHeroDead();
        }
        if (split[0].Contains("LumberJackComplete"))
        {
            foreach (ExplodeInteraction explodeInteraction in m_waspNests)
            {
                if (explodeInteraction != null)
                {
                    Destroy(explodeInteraction.gameObject);
                }
            }

            m_waspNests.Clear();
            return m_lumberJackState == LumberJackState.Done;
        }

        return false;
    }

    public override void TriggerQuestEvent(string _event)
    {
        var split = _event.Split(';', '|', ':', '\n');
        
        if (split[0].Contains("SetWaspNestsAttackable"))
        {
            bool isAttackable = false;

            if (bool.TryParse(split[1], out isAttackable))
            {
                SetWaspNestsAttackable(isAttackable);
            }
        }
        else if (split[0].Contains("DoLumberJack"))
        {
            LumberJack();
        }
        else if (split[0].Contains("DisableHeroMusic"))
        {
            m_suspendHeroMusic = true;
        }
    }
    
    [Serializable]
    public class SaveLoadQuestWaspProblemContainer : SaveLoadQuestBaseContainer
    {
        public SaveLoadQuestWaspProblemContainer() : base() { }
        public SaveLoadQuestWaspProblemContainer(MAQuestBase _base) : base(_base) { }
        [Save] public int m_isFirstAttempt;
        [Save] public int m_lumberJackState;
        [Save] public List<string> m_choppedTreeNames;
        [Save] public Vector3 m_startingPosLumberJack;
        [Save] public int m_suspendHeroMusic;
    }
    
    public override SaveLoadQuestBaseContainer Save()
    {
        var saveContainer = new SaveLoadQuestWaspProblemContainer(this);
        saveContainer.m_isFirstAttempt = m_isFirstAttempt  ? 1 : 0;
        saveContainer.m_suspendHeroMusic = m_suspendHeroMusic  ? 1 : 0;
        saveContainer.m_lumberJackState = (int)m_lumberJackState;
        saveContainer.m_startingPosLumberJack = m_startingPosLumberJack;
        if (m_choppedTreeNames != null)
        {
            saveContainer.m_choppedTreeNames = m_choppedTreeNames;
        }
        m_saveData = saveContainer;
        return saveContainer;
    }
    
    public override void Load(SaveLoadQuestBaseContainer _l)
    {
        base.Load(_l);
        var saveContainer = _l as SaveLoadQuestWaspProblemContainer;
        if (saveContainer != null)
        {
            m_isFirstAttempt = saveContainer.m_isFirstAttempt == 1;
            m_suspendHeroMusic = saveContainer.m_suspendHeroMusic == 1;
            m_lumberJackState = (LumberJackState)saveContainer.m_lumberJackState;
            m_startingPosLumberJack = saveContainer.m_startingPosLumberJack;
            if (saveContainer.m_choppedTreeNames != null)
            {
                m_choppedTreeNames = new(saveContainer.m_choppedTreeNames);
            }

            foreach (var componentsInChild in m_treeHolder.GetComponentsInChildren<BCChopObject>())
            {
                TreeHolder tree = componentsInChild.GetComponent<TreeHolder>();
                if (tree != null)
                {
                    if (m_choppedTreeNames != null && m_choppedTreeNames.Contains(tree.Name))
                    {
                        componentsInChild.m_collectableResource = componentsInChild.GetComponentInChildren<MACollectableResource>();
                      //  componentsInChild.m_dawnsUntilUprootedTreeRegrowth = -1;
                        //componentsInChild.HitObject(Single.MaxValue);
                        //componentsInChild.RefreshTreeObjectActive();
                    }
                }
            }
            m_saveData = saveContainer;


            switch (m_lumberJackState)
            {
                case LumberJackState.Gathering:
                    LumberJack();
                    break;
            }
        }
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(MAQuestWaspProblem))]
public class MAQuestWaspProblemEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        var b = target as MAQuestWaspProblem;
        if (GUILayout.Button("LumberJack"))
        {
            b.LumberJack();
        }   
        if (GUILayout.Button("add Stuff"))
        {
            b.DebugAddResources();
        }   
    }
}
#endif