using System.Collections.Generic;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Splines;

public class MAScentTrail : MonoBehaviour
{
    public bool m_debugRender = false;
    public float m_groundHeight = 1.0f;
    public int m_setGroundHeightIndex = -1;
    public bool m_setToGroundHeight = false;

    public SplineContainer m_splineContainer;

    public Transform m_lineRendererHolder;
    public MATrailSection m_trailSectionPrefab;
    public float m_trailSectionLength = 10.0f;
    public float m_trailSectionGapLength = 3.0f;

    public Transform m_particleHolder;
    public MAScentParticle m_particlePrefab;
    public float m_particleGapLength = 3.0f;

    public float m_trailLength = 30.0f;
    public float m_maxDistance = 5.0f;
    public float m_minProgressSpeed = 10.0f;
    public float m_maxProgressSpeed = 20.0f;
    public float m_trailLengthGrowSpeed = 10.0f;
    private GameObject m_target;
    private int m_currentSplineIndex = -1;
    private Spline m_currentSpline = null;
    private float m_invSplineLength = 0.0f;
    private float m_currentSplineProgressStart = 0.0f;
    private float m_currentSplineProgressEnd = 0.0f;
    private float m_currentTrailLength = 0.0f;

    private float m_nextTrailSectionProgress = 0.0f;
    private List<MATrailSection> m_trailSections = new List<MATrailSection>();

    private float m_nextParticleProgress = 0.0f;
    private List<MAScentParticle> m_particles = new List<MAScentParticle>();
    private List<MAScentParticle> m_debugParticles = new List<MAScentParticle>();
    private bool m_isDebugRenderOn = false;

    private void Update()
    {
        if(m_target != null)
        {
            if(UpdateSplineProgress())
            {
                //DisableTrailSections();
                //AddTrailSections();

                DisableParticles();
                AddParticles();
            }
        }

        if(m_debugRender)
        {
            if (!m_isDebugRenderOn)
            {
                ShowDebugRender(true);
                m_isDebugRenderOn = true;
            }
        }
        else if(m_isDebugRenderOn)
        {
            ShowDebugRender(false);
            m_isDebugRenderOn = false;
        }

        if (m_setToGroundHeight)
        {
            SetToGroundHeight(m_groundHeight, m_setGroundHeightIndex);
            m_setToGroundHeight = false;
        }
    }

    public void Show(GameObject _target, int _splineIndex = 0, bool _forceReset = false, float _startProgress = 0.0f)
    {
        m_target = _target;

        if(m_target != null)
        {
            if (_forceReset || _splineIndex != m_currentSplineIndex)
            {
                SetCurrentSpline(_splineIndex, _startProgress);
            }
            else
            {
                m_nextTrailSectionProgress = m_currentSplineProgressStart;
                m_nextParticleProgress = m_currentSplineProgressStart;
                m_currentTrailLength = 0.0f;
            }
        }
        else
        {
            //DisableAllTrailSections();
            DisableAllParticles();
        }    
    }

    public float GetProgress()
    {
        return m_currentSplineProgressStart;
    }

    private void SetCurrentSpline(int _splineIndex, float _progressStart = 0.0f)
    {
        m_currentSplineIndex = Mathf.Clamp(_splineIndex, 0, m_splineContainer.Splines.Count);
        m_currentSpline = m_splineContainer.Splines[m_currentSplineIndex];
        m_invSplineLength = 1.0f / m_currentSpline.GetLength();
        m_currentTrailLength = 0.0f;
        m_currentSplineProgressStart = _progressStart;
        m_currentSplineProgressEnd = Mathf.Clamp01(m_currentSplineProgressStart + m_currentTrailLength * m_invSplineLength);

        m_nextTrailSectionProgress = m_currentSplineProgressStart;
        //DisableAllTrailSections();
        //AddTrailSections();

        m_nextParticleProgress = m_currentSplineProgressStart;
        DisableAllParticles();
    }

    private bool UpdateSplineProgress()
    {
        bool wasTrailLengthUpdated = false;

        if(m_currentTrailLength < m_trailLength)
        {
            m_currentTrailLength += m_trailLengthGrowSpeed * Time.deltaTime;
            m_currentTrailLength = Mathf.Clamp(m_currentTrailLength, 0.0f, m_trailLength);
            m_currentSplineProgressEnd = Mathf.Clamp01(m_currentSplineProgressStart + m_currentTrailLength * m_invSplineLength);
            wasTrailLengthUpdated = true;
        }

        if (m_currentSplineProgressStart < m_currentSplineProgressEnd)
        {
            float3 targetPos = m_splineContainer.transform.InverseTransformPoint(m_target.transform.position);
            float3 nearest = Vector3.zero;
            float t = 0.0f;
            float distance = SplineUtility.GetNearestPoint(m_currentSpline, targetPos, out nearest, out t);

            if(t > m_currentSplineProgressEnd)
            {
                nearest = m_currentSpline.EvaluatePosition(m_currentSplineProgressEnd);
            }

            if (t > m_currentSplineProgressStart)
            {
                Vector2 offsetXZ = new Vector2(nearest.x - targetPos.x, nearest.z - targetPos.z);

                if (offsetXZ.sqrMagnitude < (m_maxDistance * m_maxDistance))
                {
                    float progressRatio = (t - m_currentSplineProgressStart) / (m_currentSplineProgressEnd - m_currentSplineProgressStart);
                    float progressSpeed = m_minProgressSpeed + progressRatio * (m_maxProgressSpeed - m_minProgressSpeed);
                    m_currentSplineProgressStart += (progressSpeed * m_invSplineLength) * Time.deltaTime;
                    m_currentSplineProgressStart = Mathf.Clamp01(m_currentSplineProgressStart);
                    m_currentSplineProgressEnd = Mathf.Clamp01(m_currentSplineProgressStart + m_currentTrailLength * m_invSplineLength);

                    return true;
                }
            }
        }

        return wasTrailLengthUpdated;
    }

    private MATrailSection GetTrailSection()
    {
        foreach(var trailSection in m_trailSections)
        {
            if(!trailSection.gameObject.activeSelf)
            {
                return trailSection;
            }
        }

        var newTrailSection = Instantiate(m_trailSectionPrefab, m_lineRendererHolder);
        m_trailSections.Add(newTrailSection);
        return newTrailSection;
    }

    private void DisableAllTrailSections()
    {
        foreach (var trailSection in m_trailSections)
        {
            trailSection.Hide();
        }
    }

    private void DisableTrailSections()
    {
        foreach (var trailSection in m_trailSections)
        {
            if (trailSection.m_endProgress < m_currentSplineProgressStart)
            {
                trailSection.Hide();
            }
        }
    }

    private void AddTrailSections()
    {
        while(m_nextTrailSectionProgress < m_currentSplineProgressEnd)
        {
            var newTrailSection = GetTrailSection();
            newTrailSection.Init(m_currentSpline, m_nextTrailSectionProgress, Mathf.Clamp01(m_nextTrailSectionProgress + m_trailSectionLength * m_invSplineLength));
            m_nextTrailSectionProgress += (m_trailSectionLength + m_trailSectionGapLength) * m_invSplineLength;
        }
    }

    private MAScentParticle GetParticle(bool _debug = false)
    {
        List<MAScentParticle> particles = _debug ? m_debugParticles : m_particles;

        foreach (var particle in particles)
        {
            if (!particle.gameObject.activeSelf)
            {
                return particle;
            }
        }

        var newParticle = Instantiate(m_particlePrefab, m_particleHolder);
        particles.Add(newParticle);
        return newParticle;
    }

    private void DisableAllParticles()
    {
        foreach (var particle in m_particles)
        {
            particle.Hide();
        }
    }

    private void DisableParticles()
    {
        foreach (var particle in m_particles)
        {
            if (particle.Progress < m_currentSplineProgressStart)
            {
                particle.Hide();
            }
        }
    }

    private void AddParticles()
    {
        while (m_nextParticleProgress < m_currentSplineProgressEnd)
        {
            var newParticle = GetParticle();
            newParticle.Init(m_currentSpline, m_nextParticleProgress);
            m_nextParticleProgress += m_particleGapLength * m_invSplineLength;
        }
    }

    private void ShowDebugRender(bool _show)
    {
        if(_show)
        {
            for(int i = 0; i < m_splineContainer.Splines.Count; i++)
            {
                Spline spline = m_splineContainer.Splines[i];
                float invSplineLength = 1.0f / spline.GetLength();
                float progress = 0.0f;

                while(progress < 1.0f)
                {
                    var newParticle = GetParticle(true);
                    newParticle.Init(spline, progress);
                    progress += m_particleGapLength * invSplineLength;
                }
            }
        }
        else
        {
            foreach(MAScentParticle particle in m_debugParticles)
            {
                Destroy(particle.gameObject);
            }

            m_debugParticles.Clear();
        }
    }

    private void SetToGroundHeight(float _groundHeight, int _splineIndex)
    {
        if (_splineIndex < 0)
        {
            for (int i = 0; i < m_splineContainer.Splines.Count; i++)
            {
                SetToGroundHeight(_groundHeight, m_splineContainer.Splines[i]);
            }
        }
        else if (_splineIndex < m_splineContainer.Splines.Count)
        {
            SetToGroundHeight(_groundHeight, m_splineContainer.Splines[_splineIndex]);
        }
    }

    private void SetToGroundHeight(float _groundHeight, Spline _spline)
    {
        BezierKnot[] knots = _spline.ToArray();

        for(int i = 0; i < knots.Length; i++)
        {
            BezierKnot knot = knots[i];
            Vector3 worldPos = m_splineContainer.transform.TransformPoint(knot.Position).GroundPosition(_groundHeight);
            knot.Position = m_splineContainer.transform.InverseTransformPoint(worldPos);
            _spline.SetKnot(i, knot);
        }
    }
}
