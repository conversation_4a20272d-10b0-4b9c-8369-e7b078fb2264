using System.Collections;
using UnityEngine;
using UnityEngine.Playables;

public class MAQuestCutscene : MonoBehaviour
{
    public PlayableDirector m_playableDirector;
    public Camera m_cutsceneCamera;
    public Camera m_transitionCamera;
    public float m_transitionInTime = 0.0f;
    public float m_transitionOutTime = 2.0f;
    public bool m_playEarly = false;
    public bool m_moveCurrentCamera = false;
    public bool m_useOverrideEndCamera = false;
    public Vector3 m_overrideEndCameraPos;
    public Quaternion m_overrideEndCameraRot;
    private bool m_useCameraOverrideTransform = true;

    public static bool s_isAnyActive = false;

    public IEnumerator Co_Play(System.Action _onSkip = null)
    {
        // Reset timeline to make sure it is in the correct start state
        double totalDuration = m_playableDirector.duration;
        m_playableDirector.time = 0.0f;
        m_playableDirector.Evaluate();
        Camera currentCamera = FindCurrentCamera();
        Vector3 startCameraPos = currentCamera.transform.position;
        Quaternion startCameraRot = currentCamera.transform.rotation;
        float startCameraFOV = currentCamera.fieldOfView;

        s_isAnyActive = true;

        if (m_transitionInTime > 0.0f)
        {
            if(m_playEarly) m_playableDirector.Play();
            LerpCamera(m_transitionCamera, currentCamera, m_cutsceneCamera, 0.0f);

            if (m_useCameraOverrideTransform)
            {
                GameManager.Me.SetCameraOverrideTransform(m_transitionCamera.transform);
            }
            else
            {
                SwitchCamera(currentCamera, m_transitionCamera);
            }
            
            float t = 0.0f;

            while (t < 1.0f)
            {
                t += Time.deltaTime / m_transitionInTime;
                t = Mathf.Clamp01(t);
                float easedT = Ease(t);
                LerpCamera(m_transitionCamera, startCameraPos, startCameraRot, startCameraFOV, m_cutsceneCamera, easedT);
                yield return null;
            }

            if (m_useCameraOverrideTransform)
            {
                GameManager.Me.SetCameraOverrideTransform(m_cutsceneCamera.transform);
            }
            else
            {
                SwitchCamera(m_transitionCamera, m_cutsceneCamera);
            }

            if (!m_playEarly) m_playableDirector.Play();
        }
        else
        {
            Crossfade.Me.Fade(() =>
            {
                if (m_playEarly) m_playableDirector.Play();

                if (m_useCameraOverrideTransform)
                {
                    GameManager.Me.SetCameraOverrideTransform(m_cutsceneCamera.transform);
                }
                else
                {
                    SwitchCamera(currentCamera, m_cutsceneCamera);
                }

                return true;
            },
            () =>
            {
                if (!m_playEarly) m_playableDirector.Play();
            });
        }

        const string c_skipLabel = "QuestCutscene";
        bool abort = false;
        SkipManager.Me.EnableSkip(c_skipLabel, true, () => abort = true);

        while (m_playableDirector.time < totalDuration)
        {
            if(abort)
            {
                m_playableDirector.time = totalDuration;
                m_playableDirector.Evaluate();
                _onSkip?.Invoke();
            }

            yield return null;
        }

        SkipManager.Me.EnableSkip(c_skipLabel, false);

        if (m_moveCurrentCamera)
        {
            startCameraPos = m_useOverrideEndCamera ? m_overrideEndCameraPos : m_cutsceneCamera.transform.position;
            startCameraRot = m_useOverrideEndCamera ? m_overrideEndCameraRot : m_cutsceneCamera.transform.rotation;
            startCameraRot = GameManager.Me.GetValidCameraRotation(startCameraPos, startCameraRot);

            if (!m_useCameraOverrideTransform)
            {
                GameManager.Me.CameraTransition(startCameraPos, startCameraRot, 0.0f, null);
                yield return null;
            }   
        }

        if(m_transitionOutTime > 0.0f)
        {
            LerpCamera(m_transitionCamera, m_cutsceneCamera, startCameraPos, startCameraRot, startCameraFOV, 0.0f);

            if (m_useCameraOverrideTransform)
            {
                GameManager.Me.SetCameraOverrideTransform(m_transitionCamera.transform);
            }
            else
            {
                SwitchCamera(m_cutsceneCamera, m_transitionCamera);
            }

            float t = 0.0f;

            while (t < 1.0f)
            {
                t += Time.deltaTime / m_transitionOutTime;
                t = Mathf.Clamp01(t);
                float easedT = Ease(t);
                LerpCamera(m_transitionCamera, m_cutsceneCamera, startCameraPos, startCameraRot, startCameraFOV, easedT);
                yield return null;
            }

            if (m_useCameraOverrideTransform)
            {
                GameManager.Me.SetCameraOverrideTransform(null);
                GameManager.Me.CameraTransition(startCameraPos, startCameraRot, 0.0f, null);
            }
            else
            {
                SwitchCamera(m_transitionCamera, currentCamera);
            }
        }
        else
        {
            Crossfade.Me.Fade(() =>
            {
                if (m_useCameraOverrideTransform)
                {
                    GameManager.Me.SetCameraOverrideTransform(null);
                    GameManager.Me.CameraTransition(startCameraPos, startCameraRot, 0.0f, null);
                }
                else
                {
                    SwitchCamera(m_cutsceneCamera, currentCamera);
                }

                return true;
            },
            () =>
            {
            });
        }

        s_isAnyActive = false;
    }

    public void Pause()
    {
        m_playableDirector.Pause();
    }

    public void Resume()
    {
        m_playableDirector.Resume();
    }

    private Camera FindCurrentCamera()
    {
        // Not sure if this is always the current camera, possibly expand
        return Camera.main;
        //return GameManager.Me.m_camera;
    }

    private void LerpCamera(Camera _outCamera, Camera _fromCamera, Camera _toCamera, float _t)
    {
        _outCamera.transform.position = Vector3.Lerp(_fromCamera.transform.position, _toCamera.transform.position, _t);
        _outCamera.transform.rotation = Quaternion.Lerp(_fromCamera.transform.rotation, _toCamera.transform.rotation, _t);
        _outCamera.fieldOfView = Mathf.Lerp(_fromCamera.fieldOfView, _toCamera.fieldOfView, _t);
    }

    private void LerpCamera(Camera _outCamera, Vector3 _fromPos, Quaternion _fromRot, float _fromFOV, Camera _toCamera, float _t)
    {
        _outCamera.transform.position = Vector3.Lerp(_fromPos, _toCamera.transform.position, _t);
        _outCamera.transform.rotation = Quaternion.Lerp(_fromRot, _toCamera.transform.rotation, _t);
        _outCamera.fieldOfView = Mathf.Lerp(_fromFOV, _toCamera.fieldOfView, _t);
    }

    private void LerpCamera(Camera _outCamera, Camera _fromCamera, Vector3 _toPos, Quaternion _toRot, float _toFOV, float _t)
    {
        _outCamera.transform.position = Vector3.Lerp(_fromCamera.transform.position, _toPos, _t);
        _outCamera.transform.rotation = Quaternion.Lerp(_fromCamera.transform.rotation, _toRot, _t);
        _outCamera.fieldOfView = Mathf.Lerp(_fromCamera.fieldOfView, _toFOV, _t);
    }

    private float Ease(float t)
    { 
        t *= 2.0f;

        if (t < 1)
        {
            return 0.5f * t * t;
        }

        t--;

        return -0.5f * (t * (t - 2) - 1);
    }

    private void SwitchCamera(Camera _cameraOld, Camera _cameraNew)
    {
        _cameraOld.gameObject.SetActive(false);
        _cameraNew.gameObject.SetActive(true);
    }
}
