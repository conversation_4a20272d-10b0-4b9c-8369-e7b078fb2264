using UnityEngine;
using UnityEngine.UI;

public class MAIconAnimated : MonoBehaviour
{
    [SerializeField] private Image m_background = null;
    [SerializeField] private Image m_icon = null;
    [SerializeField] private Animator m_animator = null;
    
    [Range(0, 5f)]
    [SerializeField] private float m_minScale = 1f;
    [Range(0, 5f)]
    [SerializeField] private float m_maxScale = 1.5f;
    
    [SerializeField] private bool m_disableAtMinimumValue = true;

    [Header("Animation Trigger")]
    [SerializeField] private string m_animateFloatTrigger = "Animate";
    private bool m_shouldBeActive = false;
    
    private void Awake()
    {
        gameObject.SetActive(m_shouldBeActive || m_disableAtMinimumValue == false);
        transform.localScale = Vector3.one * m_minScale;
        
        if (m_animator == null)
        {
            Debug.LogError($"{GetType()} - Animator is missing at {transform.Path()}", gameObject);
            return;
        }

        if (m_animator.HasParameter(m_animateFloatTrigger) == false)
        {
            Debug.LogError($"{GetType()} - Animator is missing the '{m_animateFloatTrigger}' parameter. You can set the param string at {transform.Path()} or change it in the animator", gameObject);
        }

        m_animator.StopPlayback();     
    }
    
    public void Set(float _value, float _valueMin, float _valueMax)
    {
        m_shouldBeActive = _value > _valueMin;
        gameObject.SetActive(m_shouldBeActive || m_disableAtMinimumValue == false);
        float norm = Mathf.Abs(_value) / (_valueMax - _valueMin);
        m_animator.SetFloat(m_animateFloatTrigger, norm);        
        transform.localScale = Vector3.one * (m_minScale + norm * (m_maxScale - m_minScale));
    }
}
