using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[System.Serializable]
public class MAComponentInfo
{
    public const string IconPath = "MA/_Art/Icons/";
    public static Dictionary<string, MAComponentInfo> s_componentInfos = new (); 
    public static List<MAComponentInfo> GetList=> s_componentInfoList;
    public string DebugDisplayName => m_name;

    public static List<MAComponentInfo> s_componentInfoList = new List<MAComponentInfo>();
    public static bool IsLoaded => s_componentInfos.Count > 0;
    public string DisplayTitle => m_buildingTitle.IsNullOrWhiteSpace() ? m_title : m_buildingTitle;
    public string id;
    public bool m_debugChanged;
    public string m_name;
    public string m_field;
    public string m_class;
    [ScanField] public string m_required;
    public string m_blockSprite;
    [ScanField] public string m_defaultDrawerSet;
    public string m_title;
    public string m_buildingTitle;
    public string m_description;
    public float m_usageMultiplier = 1;
    public float m_basePrice = 1;
    public bool m_showInInfo;
    
    public Sprite m_sprite;
    public Type m_classType;
    public List<(string t_field, string t_value)> m_fields = new ();
    [NonSerialized]public List<MAComponentInfo> m_requiredComponents = new();
    //[NonSerialized]public List<MAComponentInfo> m_requiredBy = new();
    
    public float GetPrice(int _totalUsages)
    {
        return m_basePrice + (_totalUsages * m_usageMultiplier);
        return m_basePrice * _totalUsages * m_usageMultiplier;
    }
    
    public class ComponentDetails
    {
        public ComponentDetails(System.Type _type, string _spriteName)
        {
            m_classType = _type;
            m_sprite = Resources.Load<Sprite>($"{IconPath}{_spriteName}");
        }
        public System.Type m_classType;
        public Sprite m_sprite;
    }

    public string GetFieldEntry(string _name)
    {
        var field = m_fields.Find(o => o.t_field.Equals(_name, StringComparison.OrdinalIgnoreCase));
        if (field.t_field.IsNullOrWhiteSpace() == false)
        {
            return field.t_value;
        }
        else
        {
            return "Field not found";
        }
    }
    
    /*public static string GetActionChoice(MAComponentInfo _component)
    {
        string actionComponents = "";
        foreach(var c in _component.m_requiredBy)
        {
            actionComponents += (actionComponents.IsNullOrWhiteSpace() ? "" : " / ") + c.m_title;
        }
        return actionComponents;
    }*/
    
    public static bool PostImport(MAComponentInfo _what)
    {
        if (s_componentInfos.ContainsKey(_what.m_name) == false)
        {
            s_componentInfos.Add(_what.m_name, _what);
            if (_what.m_class.IsNullOrWhiteSpace() == false)
            {
                _what.m_classType = Type.GetType(_what.m_class);
                if (_what.m_classType == null)
                {
                    Debug.LogError(
                        $"Component '{_what.m_name} has a m_class of '{_what.m_class} which is not implemented");
                }
            }

            if (_what.m_blockSprite.IsNullOrWhiteSpace() == false)
                _what.m_sprite = Resources.Load<Sprite>($"{IconPath}{_what.m_blockSprite}");
        }

        if (_what.m_field.IsNullOrWhiteSpace() == false)
        {
            var fieldSplit = _what.m_field.Split(';', '|', ',', '\n');
            for (int i = 0, j = 0; i < fieldSplit.Length; i++)
            {
                var field = fieldSplit[i];
                var equalsSplit = field.Split('=');
                if (equalsSplit.Length > 1)
                {
                    if (equalsSplit[0].IsNullOrWhiteSpace() == false)
                        s_componentInfos[_what.m_name].m_fields.Add((equalsSplit[0].Trim(), equalsSplit[1].Trim()));
                }
                else
                {
                    Debug.LogError($"Field[{field}] has no equivalent in m_value or '=' {_what.m_name}");
                }
            }
        }

        return true;
    }

    public static void PostLoad()
    {
        foreach(var info in s_componentInfoList)
        {
            if (info.m_required.IsNullOrWhiteSpace() == false)
            {
                char[] splitChars = new [] {';', '|', ',', '\n'};
                var requiredInfos = info.m_required.Split(splitChars, StringSplitOptions.RemoveEmptyEntries);
                foreach(var r in requiredInfos)
                {
                    if(s_componentInfos.TryGetValue(r, out var value))
                        info.m_requiredComponents.Add(value);
                }
            }
            
            /*foreach(var otherInfo in s_componentInfoList)
            {
                if(otherInfo == info) continue;
            
                foreach(var r in otherInfo.m_requiredComponents)
                {
                    if(r != info) continue;
                
                    info.m_requiredBy.Add(otherInfo);
                }
            }*/
        }
    }
    
    public static List<MAComponentInfo> LoadInfo()  // Must be loaded after blocks
    {
        s_componentInfos = new();
        s_componentInfoList = NGKnack.ImportKnackInto<MAComponentInfo>(PostImport);
        PostLoad();
        return s_componentInfoList;
    }

    public static MAComponentInfo GetInfo(string _name)
    {
        MAComponentInfo result = null;
        s_componentInfos.TryGetValue(_name, out result);
        return result;
    }
    public static List<MAComponentInfo> GetInfos(params string[] _names)
    {
        List<MAComponentInfo> infos = new();
        foreach (var infoName in _names)
        {
            var info = GetInfo(infoName);
            if(info != null) infos.Add(info);
        }
        return infos;
    }
    public static MAComponentInfo GetInfoByClass(Type _type)
    {
        var result = s_componentInfoList.FindAll(o => o.m_classType == _type);
        if(result.Count == 0) return null;
        return result[0];
    }
    public static List<MAComponentInfo> GetInfosByClass(params Type[] _classes)
    {
        List<MAComponentInfo> infos = new();
        foreach (var classType in _classes)
        {
            var info = GetInfoByClass(classType);
            if(info != null) infos.Add(info);
        }
        return infos;
    }
}
