#if UNITY_EDITOR
using UnityEditor;
#endif

public class BCActionCrypt : MACreatureSpawnPointActionBase
{
    override public bool Arrive(MACharacterBase _character)
    {
        bool result = base.Arrive(_character);
        return result || _character is MACreatureBase;
    }
}

#if UNITY_EDITOR
[CanEditMultipleObjects]
[CustomEditor(typeof(BCActionCrypt))]
public class BCActionCryptEditor : MACreatureSpawnPointActionBaseEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        // MACreatureSpawnPointActionBase spawnPoint = (MACreatureSpawnPointActionBase)target;
        //
        // if(Application.isPlaying)
        // {
        //     foreach(string creatureType in spawnPoint.m_spawnCreatures)
        //     {
        //         if(GUILayout.Button($"Spawn {creatureType}"))
        //         {
        //             spawnPoint.EditorSpawnCreature(creatureType);
        //         }
        //     }
        // }
    }
}
#endif