using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using UnityEngine.UIElements;
using Image = UnityEngine.UI.Image;

public class MAOrderBoardUI : MAGUIBase, INGDecisionCardHolder
{
	public bool DragIn3D { get { return true; } }
	public INGDecisionCardHolder.ECardView CardView { get { return INGDecisionCardHolder.ECardView.Standard; } }
	public void ToggleHiding(bool _hide, bool _isDueToClick = false) {}
	public bool IsHidden { get { return false; } }
	public void GiftReceived(IBusinessCard _card) {}
	public Transform GiftsHolder() { return transform; }
	public bool EnableCardOnUpwardDrag() { return false; } 
	public Transform ParentForDraggingCard(NGDirectionCardBase _card) => null;
	public Transform GetParentHolder(NGDirectionCardBase _card) => null;
	public bool ShowOrderBoardGUIOnCardClick { get { return false; } }
	public void OnStartCardDrag() {}
	public void OnEndCardDrag() {}
	public Transform Root { get { return transform; } }
	
	public Transform m_cardHolder;
	
	public Transform m_orderGiverLineHolder;
	public TMP_Text m_reputationProgressText;
	public Transform m_reputationBar;
	public Image m_reputationProgressBar;
	public Image m_giverIcon;
	public TMP_Text m_orderFrom;
	public Transform m_orderStatsLineHolder;
	public TMP_Text m_title;
	public TMP_Text m_description;
	public ScrollRect m_scrollRect;
	public GameObject m_giverHolder;
	
	private MAOrder m_order;
	
	public void OnCardClick() {}

	public void Activate(MAOrder _order)
    {
		base.Activate();
		
		m_order = _order;
		
		if(_order.IsNullOrEmpty())
		{
			Close();
			return;
	    }

	    m_title.text = _order.DisplayName;
	    m_description.text = _order.GetDescription();
	    
	    m_cardHolder.DestroyChildren();
	    
	    var tile = NGOrderTile.Create(NGBusinessDecisionManager.Me.m_ngOrderTilePrefab.gameObject, _order.TemplateBusinessGift, null, this, m_cardHolder.transform);
	    tile.SetupOrderUI(_order);
	    
	    RefreshView();
	    m_scrollRect.verticalNormalizedPosition = 1f;
    }
    
    public static string GetStars(int _count)
    {
		string result = "";
		for(int i = 1; i <= _count; i++)
		{
			result += MAMessageManager.GetFullStar();
		}
		return result;
    }
    
    private void RefreshView()
    {
	    m_orderStatsLineHolder.DestroyChildren();
		m_orderGiverLineHolder.DestroyChildren();
		
		var info = m_order.OrderInfo;
		
		MADesignInfoSheetLine.Create(m_orderStatsLineHolder, $"<b>State</b>", m_order.GetState(), false);
		MADesignInfoSheetLine.Create(m_orderStatsLineHolder, $"<b>Order For</b>", $"{m_order.m_orderQuantity} <b>{m_order.GetQualityString(false)}</b> {m_order.ProductDisplayName}", false);
		MADesignInfoSheetLine.Create(m_orderStatsLineHolder, $"<b>Hints</b>", info.GetHints(), false);
		MADesignInfoSheetLine.Create(m_orderStatsLineHolder, $"<b>Rewards</b>", m_order.GetRewardString(), false);
		
		if(m_order.HasDesign)
			MADesignInfoSheetLine.Create(m_orderStatsLineHolder, $"<b>Current Selling Price</b>", $"{GlobalData.CurrencySymbol}{m_order.GetProductPrice():F2}", false);
		MADesignInfoSheetLine.Create(m_orderStatsLineHolder, $"<b>Made</b>", $"{m_order.Manufactured} of {m_order.m_orderQuantity}", false);
		MADesignInfoSheetLine.Create(m_orderStatsLineHolder, $"<b>Dispatched</b>", $"{m_order.Dispatched} of {m_order.m_orderQuantity}", false);
		
		if(info.OrderGiver != null && info.OrderGiver.HideGiver == false)
		{
			m_orderFrom.text = $"<b>From</b> {info.OrderGiver.m_displayName}\n{info.OrderGiver.m_description}";
			
			MADesignInfoSheetLine.Create(m_orderGiverLineHolder, $"<b>Faction</b>", info.Faction.m_title, false);
			var progressLine = MADesignInfoSheetLine.Create(m_orderGiverLineHolder, $"<b>Level Progress</b>", "", false);
			MADesignInfoSheetLine.Create(m_orderGiverLineHolder, $"<b>Reputation</b>", info.OrderGiver.GetReputationStars(), false);
			
			progressLine.m_count.gameObject.SetActive(false);
			m_reputationBar.transform.parent = progressLine.m_count.transform.parent;
			m_reputationBar.transform.localPosition = Vector3.zero;
			var reputationRemainder = info.OrderGiver.CalculateReputationRemainder();
			m_reputationProgressBar.fillAmount = reputationRemainder;
			m_reputationProgressText.text = reputationRemainder.ToString("P0");
			
			int rLevel = info.OrderGiver.CalculateReputation();
			var r1 = MADesignInfoSheetLine.Create(m_orderGiverLineHolder, "   " + GetStars(1), info.OrderGiver.Reputation1Bonus.GetRewardDescription(), false);
			var r2 = MADesignInfoSheetLine.Create(m_orderGiverLineHolder, "   " + GetStars(2), info.OrderGiver.Reputation2Bonus.GetRewardDescription(), false);
			var r3 = MADesignInfoSheetLine.Create(m_orderGiverLineHolder, "   " + GetStars(3), info.OrderGiver.Reputation3Bonus.GetRewardDescription(), false);
			var r4 = MADesignInfoSheetLine.Create(m_orderGiverLineHolder, "   " + GetStars(4), info.OrderGiver.Reputation4Bonus.GetRewardDescription(), false);
			var r5 = MADesignInfoSheetLine.Create(m_orderGiverLineHolder, "   " + GetStars(5), info.OrderGiver.Reputation5Bonus.GetRewardDescription(), false);
			
			if(rLevel < 1) r1.gameObject.AddComponent<CanvasGroup>().alpha = 0.5f;
			if(rLevel < 2) r2.gameObject.AddComponent<CanvasGroup>().alpha = 0.5f;
			if(rLevel < 3) r3.gameObject.AddComponent<CanvasGroup>().alpha = 0.5f;
			if(rLevel < 4) r4.gameObject.AddComponent<CanvasGroup>().alpha = 0.5f;
			if(rLevel < 5) r5.gameObject.AddComponent<CanvasGroup>().alpha = 0.5f;
		}
		else
		{
			m_giverHolder.SetActive(false);
			m_reputationBar.gameObject.SetActive(false);
		}
		
		if(info.OrderGiver?.PortraitSprite != null)
			m_giverIcon.sprite = info.OrderGiver.PortraitSprite;
		else if(m_giverIcon.gameObject.activeSelf)
			m_giverIcon.gameObject.SetActive(false);
    }
	
	private void Close()
    {
		if(s_currentInstance == this)
			s_currentInstance = null;
	    gameObject.SetActive(false);
	    Destroy(this.gameObject);
    }

	private static MAOrderBoardUI s_currentInstance;
	
	public static void Create(MAOrder _order)
    {
		if(s_currentInstance)
		{
			s_currentInstance.Close();
		}
		
	    AudioClipManager.Me.PlaySound("PlaySound_OrdersBoard_Open", GameManager.Me.gameObject);
	    s_currentInstance = Instantiate(MAOrderDataManager.Me.m_maOrderBoardUIPrefab, NGManager.Me.m_orderBoardUI);

	    s_currentInstance.gameObject.SetActive(true);
	    s_currentInstance.Activate(_order);
    }
    
    public void OnCloseButtonClicked()
    {
	    AudioClipManager.Me.PlaySound("PlaySound_OrdersBoardClose", GameManager.Me.gameObject);
	    Close();
    }
}
