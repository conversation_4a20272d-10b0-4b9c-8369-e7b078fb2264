using System;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
using System.Linq;
using UnityEngine.UI;

#endif
public class MACreatureBase : MACharacterBase, IDamager
{
	public override Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary() { return MACreatureStateLibrary.StateLibrary; }
	
	[Header("MACreatureBase")]
	
	[Range(0f, 5f)][SerializeField] protected float m_jumpBackOffAmount = .8f;
	//[Range(0, 20f)][SerializeField] protected float m_directionalForceOnDestroyedBlocks = 10f;
	[Range(0, 20f)][SerializeField] protected float m_generalForceOnDestroyedBlocks = 10f;

	/******** non-override properties *******/
	protected GameState_CreatureBase CreatureGameState { get { return m_gameState as GameState_CreatureBase; } }

	public override bool IsHuman => false;
	
	protected override float NavigationCorridor => CharacterSettings.m_directionCorridorWidth;
	
	protected override float[] NavigationCosts
	{
		get
		{
			bool isCalm = IsHuman && IsFleeing == false && IsInAttackState == false;
			return isCalm ? GlobalData.s_pedestrianCosts : NavCostsThreat;
		}
	}

	protected virtual float[] NavCostsThreat =>
		(m_settings.m_canDestroyWalls ? GlobalData.s_threatWithSmashCosts : GlobalData.s_threatCosts);
		
	public override bool ShowHealthBar
	{
		get
		{
			if (NGManager.Me.m_alwaysShowCreatureHealthBars) return CharacterUpdateState.State != CharacterStates.Dying && CharacterUpdateState.State != CharacterStates.Dead;
			MACharacterBase possessed = GameManager.Me.PossessedCharacter;
			if(possessed != null && possessed != this)
			{
				if(CharacterUpdateState.State != CharacterStates.Dead)
				{
					Vector3 pos = transform.position;
					float visionRadius = CharacterSettings.m_possessedHealthBarVisionRadius;
					return visionRadius * visionRadius >= (possessed.transform.position - pos).xzSqrMagnitude();
				}
				return false;
			}
			return IsInCombat || IsBeingTargeted;
		}
	}

	public virtual bool IsEnemy
	{
		get { return true; }
	}
	
	protected override void Awake()
	{
		base.Awake();
	}

	private MATimer m_noNavCheckTimer = new();
	private bool ShouldCheckForStuck
	{
		get { return (CharacterSettings.m_noNavCheckTime >= 0f); }
	}

	private HashSet<string> invalidPosStates = new HashSet<string>
	{
		CharacterStates.Idle,
		CharacterStates.RoamForTarget,
		CharacterStates.LookForTarget,
		CharacterStates.ChaseTarget,
		CharacterStates.PatrolTownWalls,
		CharacterStates.GoingHome,
		CharacterStates.PatrolToWaypoint,
		CharacterStates.Waiting,
		CharacterStates.ReturnToPatrol,
		CharacterStates.GoToWaypointIgnoringTargets,
		CharacterStates.GuardLocation,
		CharacterStates.ReturnToGuardLocation,
		CharacterStates.Flee,
	};
	private Vector3? prevInvalidPos = null;

	protected override void Update()
	{
		base.Update();

		GameManager.Me.ClearGizmos("cantreach");
		Vector3? lastPathPos = m_nav.LastPathPos;
		if (lastPathPos != null)
		{
			GameManager.Me.AddGizmoLine("cantreach", transform.position.GroundPosition(0.7f),
				((Vector3)lastPathPos).GroundPosition(0.7f), Color.magenta);
		}

		if (m_targetObject != null)
		{
			GameManager.Me.ClearGizmos("target");
			GameManager.Me.AddGizmoLine("target", transform.position,
				transform.position + (m_targetObject.transform.position - transform.position), Color.cyan);

			if (GameManager.Me.LoadComplete && Input.GetMouseButtonDown(0))
			{
				// Ray ray = new Ray(transform.position + Vector3.up * 2f,
				// 	transform.position + (m_destObject.transform.position - transform.position) + Vector3.up * 2f);
				// //var ray = Camera.main.ScreenPointToRay(Input.mousePosition);
				// MABloodControl.Me.EmitBlood(ray, 3f);
			}
		}

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		CheckForceDespawn();
#endif
	}

	private void CheckForceDespawn()
	{
		if (!ShouldCheckForStuck || IsIncapacitated)
			return;

		var state = CharacterUpdateState;
		if ((state == null) || (state.State == CharacterStates.Despawn) || (state.State == CharacterStates.Dying))
			return;
		
		if (!m_noNavCheckTimer.IsFinished)
			return;
		
		m_noNavCheckTimer.Set(CharacterSettings.m_noNavCheckTime);
		
		if (invalidPosStates.Contains(state.State))
		{
			if (m_nav.IsStuck)
			{
				Debug.LogErrorFormat("<color=#00ff00>FORCE DESPAWN STUCK: {0}  POSITION: {1}</color>", name, transform.position);
				MACharacterStateFactory.ApplyCharacterState(CharacterStates.Despawn, this);

				return;
			}
			
			var navCostTypeAtPoint = GlobalData.Me.GetNavAtPoint(transform.position);
			var navCostTypeAtAheadPoint = GlobalData.Me.GetNavAtPoint(m_nav.m_lookAheadPosition);
			if (IsValidNavPoint(navCostTypeAtPoint) == false &&
				IsValidNavPoint(navCostTypeAtAheadPoint) == false)
			{
				if (prevInvalidPos.HasValue)
				{
					Vector3 deltaPos = transform.position - prevInvalidPos.Value;
					if (deltaPos.sqrMagnitude <= 2f * 2f)
					{
						Debug.LogErrorFormat("<color=#00ff00>FORCE DESPAWN INVALID: {0}  POSITION: {1}</color>", name, transform.position);
						MACharacterStateFactory.ApplyCharacterState(CharacterStates.Despawn, this);
					}
				}
				prevInvalidPos = transform.position;

				return;
			}
		}

		prevInvalidPos = null;
	}

	private bool IsValidNavPoint(GlobalData.NavCostTypes _navCostTypeAtPoint)
	{
		switch (_navCostTypeAtPoint)
		{
			case GlobalData.NavCostTypes.Road:
			case GlobalData.NavCostTypes.Pavement:
			case GlobalData.NavCostTypes.Rail:
			case GlobalData.NavCostTypes.OffRoad:
			case GlobalData.NavCostTypes.Gate:
			case GlobalData.NavCostTypes.NoNavCanBreak:
				return true;
		}
		return false;
	}
	
	protected override void InitialiseGameState()
	{
		base.InitialiseGameState();

		SetGameStateSaveData(new GameState_CreatureBase());
		
		GameState_CreatureBase gameStateCreature = CreatureGameState;
		GameManager.Me.m_state.m_creatures.Add(gameStateCreature);
		
		Transform tr = transform;
		Vector3 position = tr.position;
		Quaternion rotation = tr.rotation;

		gameStateCreature.m_creatureInfoName = m_creatureInfo.m_name;
		gameStateCreature.m_id = m_ID;
		gameStateCreature.m_type = GetType().Name;
		gameStateCreature.m_bodyType = FindBodyType();
		gameStateCreature.m_savedUpdateState = InitialState;
		gameStateCreature.m_pos = position;
		gameStateCreature.m_rotation = rotation.eulerAngles;
		gameStateCreature.m_destPos = position;
		gameStateCreature.m_aliveDuration = 0f;
		gameStateCreature.m_health = m_creatureInfo.m_health;
		gameStateCreature.m_walkSpeed = m_creatureInfo.GetNewWalkingSpeed();
		gameStateCreature.m_attackSpeed = m_creatureInfo.GetNewAttackSpeed();
		gameStateCreature.m_spawnPos = position;
		gameStateCreature.m_characterExperience = new CharacterExperience();
		bool isGroupValid = GroupBehaviour != null;
		bool canGroupPatrol = isGroupValid && GroupBehaviour.CanPatrol;
		gameStateCreature.m_groupId = isGroupValid ? GroupBehaviour.ID : -1;
		gameStateCreature.m_groupPatrolPosition = canGroupPatrol ? GroupBehaviour.PatrollingInfo.centerPos : Vector3.zero;
		gameStateCreature.m_groupPatrolRadius = canGroupPatrol ? GroupBehaviour.PatrollingInfo.radius : 0f;
		gameStateCreature.m_groupStationaryPosition = canGroupPatrol ? GroupBehaviour.GetStationaryPosition(this) : Vector3.zero;
		gameStateCreature.m_characterExperience.m_level = m_creatureInfo.m_initialLevel;
	}
	
	public override void Activate(MACreatureInfo _creatureInfo, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation, bool _addToCharacterLists = true)
	{
		if(m_settings == null)
		{
			Debug.LogError($"{GetType().Name} - Activate - CreatureSettings == null for {_creatureInfo.m_name} - {_creatureInfo.m_prefabName}, please apply characterSettings");
		}
		if (_init)
		{
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
			for(int i = 0; i < RoadManager.Me.m_roadSets.Length; i++)//TS - knack!
			{
				RoadSet roadSet = RoadManager.Me.m_roadSets[i];
				if(roadSet.m_isPerimeterType && roadSet.m_blocksNavigation)
					m_creatureInfo.m_canSmashWallTypes.Add(i);
			}
#endif
		}
		
		base.Activate(_creatureInfo, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);

		if (_init) { }
		if(_addToCharacterLists)
			NGManager.Me.AddCreature(this);
		
		if (ShouldCheckForStuck)
			m_noNavCheckTimer.Set(CharacterSettings.m_noNavCheckTime);
	}

	protected override void SetHumanoidType()
	{
		AudioClipManager.Me.SetHumanoidType(this, HumanoidType, Level);
	}

	protected override void ApplyInitialCharacterState()
	{	
		MACharacterStateFactory.ApplyInitialState(CreatureGameState.m_savedUpdateState, this);
	}

	override public void DestroyMe()
	{
		CreatureGameState.m_aliveDuration = -1f;
		
		base.DestroyMe();
		if (!m_dontRemoveFromGameState)
		{
			GameManager.Me.m_state.m_creatures.Remove(CreatureGameState);
		}
		NGManager.Me.RemoveCreature(this);
	}

	protected override void OnDestroy()
	{
		base.OnDestroy();

		if (NGManager.Me != null)
		{
			NGManager.Me.RemoveCreature(this);
		}

		if (GameManager.Me && !m_dontRemoveFromGameState)
		{
			GameManager.Me.m_state.m_creatures.Remove(CreatureGameState);
		}
	}

	public override void PostLoad()
	{
		base.PostLoad();

		if(MACreatureControl.Me.IsValidTimeOfDay(this) == false)
		{
			if(CharacterUpdateState.State != CharacterStates.GoingHome)
				CharacterUpdateState.ApplyState(CharacterStates.GoingHome);
		}
	}

	public override void SetTargetObj(TargetResult _obj)
	{
		base.SetTargetObj(_obj);

		MAWorker currentTargetWorker = null;
		if(m_targetObject != null)
		{
			currentTargetWorker = m_targetObject.GetComponent<MAWorker>();
		}

		MAWorker newTargetWorker = (_obj != null && _obj.m_targetObject != null) ? _obj.m_targetObject.GetComponent<MAWorker>() : null;
		MABuilding newTargetBuilding = null;

		if (newTargetWorker != null)
		{
			if (newTargetWorker.m_ignoreCreatureTargetStates.Contains(newTargetWorker.m_state))
			{
				newTargetWorker = null;
			}
			else
			{
				newTargetWorker.TargettedBy.Add(this);
			}
		}
		else
		{
			newTargetBuilding  = (_obj != null && _obj.m_targetObject != null) ? _obj.m_targetObject.GetComponent<MABuilding>() : null;
		}

		//remove ye
		if (currentTargetWorker && (currentTargetWorker != newTargetWorker || newTargetBuilding != null))
		{
			currentTargetWorker.RemoveAttacker(this);
		}
	}

	public override bool IsTargetAliveAndValid(Transform _target)
	{
		if (_target == null)
			return false;
		
		MAWorker worker = _target.GetComponent<MAWorker>();
		GameObject targetGo = _target.gameObject;
		bool isActive = targetGo.activeInHierarchy;
		if (worker)
			return isActive && worker.Health > 0 && worker.m_ignoreCreatureTargetStates.Contains(worker.m_state) == false;
		
		NGMovingObject movingObject = _target.GetComponent<NGMovingObject>();
		if (movingObject)
		{
			bool isValid = isActive && movingObject.m_state != STATE.MA_DEAD &&
			               movingObject.Health > 0 &&
			               movingObject.m_state != STATE.HELD_BY_PLAYER;
			if (isValid)
			{
				MACharacterBase character = _target.GetComponent<MACharacterBase>();
				return character != null && character.IsAnyDeadState == false;
			}
			return isValid;
		}
		
		MABuilding building = _target.GetComponent<MABuilding>();
		if (building)
			return isActive && building.Health > 0;
		
		TargetObject targetObject = _target.GetComponent<TargetObject>();
		if(targetObject != null && targetObject.m_targetResultOnActivation != null &&
		   targetObject.m_targetResultOnActivation.m_targetLocationState == TargetResult.TargetState.IsWall)
			return isActive && m_targetObject.GetHealth() > 0;

		return false;
	}

	public override void DoAttackOnTarget()
	{
		base.DoAttackOnTarget();

		// if (m_targetObject == null)
		// 	return;
		//
		// m_targetObject.DoDamage(ref _potentialDamage, transform.position);
	}

	public override bool HitByCart(Collision _collision)
	{
		if (base.HitByCart(_collision))
		{
			// m_lastHitTime = Time.time;
			if (!InState(CharacterStates.KnockedDown))
				MACharacterStateFactory.ApplyCharacterState(CharacterStates.KnockedDown, this);
			
			return true;
		}

		return false;
	}
}

public class ReadOnlyInspectorAttribute : PropertyAttribute {}

#if UNITY_EDITOR
[CustomPropertyDrawer(typeof(ReadOnlyInspectorAttribute))]
public class ReadOnlyDrawer : PropertyDrawer
{
	public override float GetPropertyHeight(SerializedProperty property,
		GUIContent label)
	{
		return EditorGUI.GetPropertyHeight(property, label, true);
	}

	public override void OnGUI(Rect position,
		SerializedProperty property,
		GUIContent label)
	{
		GUI.enabled = false;
		EditorGUI.PropertyField(position, property, label, true);
		GUI.enabled = true;
	}
}

[CustomEditor(typeof(MACreatureBase))]
public class MACreatureBaseEditor : MACharacterBaseEditor
{
	protected override List<string> GetStates()
	{
		MACreatureBase cb = target as MACreatureBase;
		return new List<string>(cb.StateLibrary().Keys.ToArray());
	}

	protected override void ApplyState(string _state, MACharacterBase _character)
	{
		MACharacterStateFactory.ApplyCharacterState(_state, _character);
	}
}
#endif