using System;
using System.Collections.Generic;
using UnityEngine;

public class MAHoodedCharacter : MAWorker
{
    public override bool CanAssignHome => false;
    public override bool CanAssignJob => false;
    
    public override void DoPossessedAction()
    {
    }

    public override void PostLoad()
    {
        if (GameState.m_isPossessed)
        {
            this.DoNextFrame(() => {
                DestroyMe();
            });
        }
        base.PostLoad();
    }

    public static MAHoodedCharacter Create(MAWorkerInfo _workerInfo, Vector3 _pos)
    {
        return MAWorker.Create(_workerInfo, _pos) as MAHoodedCharacter;
    }
}
