using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using Random = UnityEngine.Random;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class MADog : MAAnimal, IDamager
{
    [Header("MADog")]
    override public string HumanoidType => "Dog";

    public override Dictionary<string, Func<MACharacterBase, CharacterBaseState>> StateLibrary() => MADogStateLibrary.StateLibrary;
    public override bool IsNotBusy => (IsOnPatrol || IsRoamingFreely || IsFollowing) && TargetObject == null && (CharacterSettings.m_isBodyGuard || NGManager.Me.IsUnderAttack());

    private MATimer m_checkForFoodTimer = new MATimer();
    public float m_eatProbability = 0.05f;
    public float m_eatFrequency = 4f;
    
    private MATimer m_checkToPooTimer = new MATimer();
    public float m_pooProbability = 0.05f;
    public float m_pooFrequency = 3f;
    
    private MATimer m_checkToPeeTimer = new MATimer();
    public float m_peeProbability = 0.05f;
    public float m_peeFrequency = 2f;		
    
    private Coroutine m_switchRoutine = null;
    
    public override string InitialState
    {
        get => string.IsNullOrWhiteSpace(m_gameState?.m_initialState) ? (m_isQuestCharacter ? CharacterStates.Idle : CharacterStates.RoamForTarget) : m_gameState.m_initialState;
        set => m_gameState.m_initialState = value;
    }

    public override string DefaultState
    {
        get => string.IsNullOrWhiteSpace(m_gameState?.m_defaultState) ? (m_isQuestCharacter ? CharacterStates.Idle : CharacterStates.RoamForTarget) : m_gameState.m_defaultState;
        set => m_gameState.m_defaultState = value;
    }

    protected override void Awake()
    {
        base.Awake();
        m_contentRootName = "";
        m_headMainBoneName = "head";
        m_headBoneName = "head";
        m_neckBoneName = "neck";
        m_toeBoneName = "claws_b.R";
    }

    protected override void Update()
    {
        base.Update();
        ThreatCheck();
        UpdateDistanceTravelled();
    }

    public override TargetResult GetBestTarget(float _persistence = 1)
    {
        TargetResult bestTarget = base.GetBestTarget(_persistence);
        // if (bestTarget != null)
        // {
        //     if (bestTarget.m_targetObject.TargetObjectType == TargetObject.TargetType.CharacterType &&
        //         bestTarget.m_targetObject.GetComponent<MACharacterBase>() is MAChicken chicken)
        //     {
        //         var pos = transform.position;
        //         NGMovingObject leader = Leader;
        //         float visSq = VisionRadiusSq;
        //         if (leader != null && (IsLeaderAvailable ||
        //                             chicken.transform.position.DistanceSq(pos) > visSq || 
        //                             leader.transform.position.DistanceSq(pos) > visSq))
        //         {
        //             return null;
        //         }
        //     }
        // }
        return bestTarget;
    }

    public void ThreatCheck()
    {
        if (IsLeaderAvailable) return;
        TargetObject selfAsTarget = GetComponent<TargetObject>();
        if (selfAsTarget != null)
        {
            TargetObject.Attacker attacker = selfAsTarget.GetClosestAttacker();
            if (attacker != null && attacker.m_attacker.Transform.position.DistanceSq(transform.position) < 8 * 8 && IsFleeing == false)
            {
                if (m_switchRoutine != null) StopCoroutine(m_switchRoutine);
                SwitchStates(CharacterStates.Flee, 0f);
                return;
            }
        }
    }

    public void FoodCheck()
    {
        if (m_switchRoutine != null) return;
        if (m_checkForFoodTimer.IsFinished)
        {
            m_checkForFoodTimer.Set(m_eatFrequency);
            if (CharacterSettings.m_canEatPickups && DayNight.Me.m_isFullDay &&
                UnityEngine.Random.Range(0, 1f) < m_eatProbability)
            {
                Transform closestTransform = FindClosestTransform(GlobalData.Me.m_pickupsHolder, transform.position,
                    CharacterSettings.m_eatRadius);
                if (closestTransform != null)
                {
                    SetTargetObj(new MACharacterBase.TargetResult()
                    {
                        m_targetObject = TargetObject.Create(closestTransform, TargetObject.TargetType.Pickup)
                    });
                    SwitchStates(CharacterStates.EatPickup, 0f);
                    return;
                }
            }
        }
    }

    public void PooCheck()
    {
        if (m_switchRoutine != null) return;
        if (m_checkToPooTimer.IsFinished)
        {
            m_checkToPooTimer.Set(m_pooFrequency);
            if (CharacterSettings.m_canPoo && DayNight.Me.m_isFullDay && UnityEngine.Random.Range(0, 1f) < m_pooProbability &&
                m_gameState.m_lastDayPooed != DayNight.Me.m_day && IsInTown() == false)
            {
                SwitchStates(CharacterStates.PooAtPosition, 0f);
                return;
            }
        }
    }

    public const float c_checkForNewLeaderHitProbability = 0.2f;
    public void LeaderCheck()
    {
        if (CharacterSettings.m_isBodyGuard)
        {
            if (Leader == null && Random.Range(0, 1f) < c_checkForNewLeaderHitProbability)
            {
                var randomPick = NGManager.Me.m_MAHumanList.PickRandom();
                Leader = randomPick;
            }
        }
    }

    public void PeeCheck()
    {
        if (m_switchRoutine != null) return;
        if (m_checkToPeeTimer.IsFinished)
        {
            m_checkToPeeTimer.Set(m_peeFrequency);
            if (CharacterSettings.m_canPee && DayNight.Me.m_isFullDay && UnityEngine.Random.Range(0, 1f) < m_peeProbability && m_gameState.m_lastDayPeed != DayNight.Me.m_day)
            {
                Transform closestTransform = FindClosestTransform(NGManager.Me.m_decorationHolder, transform.position, CharacterSettings.m_peeRadius);
                if (closestTransform != null)
                {
                    SetTargetObj(new MACharacterBase.TargetResult() { m_targetObject = TargetObject.Create(closestTransform, TargetObject.TargetType.InteractionPointType) });
                    SwitchStates(CharacterStates.PeeOnDecoration, 0f);
                    return;
                }
            }
        }
    }
    private void SwitchStates(string _toState, float _delay = 0f)
    {
        if (_delay > 0f)
        {
            m_switchRoutine = StartCoroutine(SwitchStatesDelayed(CharacterStates.PeeOnDecoration, _delay));
            return;
        }
        MACharacterStateFactory.ApplyCharacterState(_toState, this);
        m_switchRoutine = null;
    }

    private IEnumerator SwitchStatesDelayed(string _toState, float _delay = 1.5f)
    {
        StopCurrentAnimation(); //wait for dog sit pose to finish and dog to stand up
        yield return new WaitForSeconds(_delay);
        MACharacterStateFactory.ApplyCharacterState(_toState, this);
        m_switchRoutine = null;
        yield break;
    }

    protected static Transform FindClosestTransform(Transform _trParent, Vector3 _toPos, float _withinRange)
    {
        int checkCount = 3;
        Transform closestTr = null;
        var bestv = Vector3.zero;
        float bestDistSq = _withinRange * _withinRange;
        foreach (Transform trChild in _trParent)
        {
            var v = (trChild.position - _toPos);
            float distSq = v.xzSqrMagnitude();
            if (distSq < bestDistSq)
            {
                bestv = v;
                checkCount--;
                bestDistSq = distSq;
                closestTr = trChild;
                if (checkCount == 0)
                    break;
            }
        }
        return closestTr;
    }

    public override void Activate(MACreatureInfo _info, MABuilding _optionalOwner, bool _init, Vector3 _position, Vector3 _rotation,
        bool _addToCharacterLists = true)
    {
        base.Activate(_info, _optionalOwner, _init, _position, _rotation, _addToCharacterLists);
        CharacterGameState.m_immortal = true;
    }

    protected override void InitialiseGameState()
    {
        base.InitialiseGameState();
    }
    
    public override bool CanInteract(NGMovingObject _chr)
    {
        return MAQuestManager.Me.RequiredPossessableMovingObjects().Contains(this);// GameManager.Me.CanPossess(this);
    }

    public override string GetInteractLabel(NGMovingObject _chr)
    {
        return "Possess";

        //if (GameManager.Me.CanPossess(this))
        //{
        //    return "Possess";
        //}
        //else
        //{
        //    return base.GetInteractLabel(_chr);
        //}
    }

    public override void DoInteract(NGMovingObject _chr)
    {
        GameManager.Me.Unpossess(false);
        GameManager.Me.PossessObject(this);

        //if (GameManager.Me.CanPossess(this))
        //{
        //    GameManager.Me.Unpossess(false);
        //    GameManager.Me.PossessObject(this);
        //}
        //else
        //{
        //    base.DoInteract(_chr);
        //}
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(MADog))]
public class MADogEditor : MACharacterBaseEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
    }
}
#endif