using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MABuildingWorkerPanelLine : MonoBehaviour
{
    public TMP_Text m_name;
    public TMP_Text m_action;
    public TMP_Text m_energy;
    public Button m_actionButton;
    public Button m_viewButton;

    //public TMP_Text m_actionText;
    
    private MACharacterBase m_character;
    
    public void OnClickAction()
    {
        var action = GetWorkerActionButton(m_character);
        
        if(action.action == null) return;
        
        action.action();
    }
    
    public void OnClickLine()
    {
        if(m_character == null) return;
        
        NGBuildingInfoGUI.DestroyCurrent();
        
        UIManager.Me.m_centralInfoPanelManager.OpenInfoPanel("Character", m_character.transform);
    }
    
    public void OnClickView()
    {
        if(m_character == null) return;
        
        NGBuildingInfoGUI.DestroyCurrent();
        
        UIManager.Me.m_centralInfoPanelManager.OpenInfoPanel("Character", m_character.transform);
    }

    public static (string description, System.Action action) GetWorkerActionButton(MACharacterBase _character)
    {
        if(_character == null) return (null,null);
        
        if(_character is MAWorker worker)
        {
            switch(_character.PeepAction)
            {
                case PeepActions.Resting:
                case PeepActions.ReturnToRest:
                    if(_character.Job != null)
                    {
                        return ("Work", worker.RecallToWork);
                    }
                    break;
                    
                default:
                    if(_character.Home != null)
                    {
                        return ("Rest", worker.RecallToHome);
                    }
                    break;
            }
        }
        return (null, null);
    }
    
    void Activate(MACharacterBase _character)
    {
        m_character = _character;
        
        var action = GetWorkerActionButton(_character);
        
        /*if(action.description.IsNullOrWhiteSpace() == false)
        {
            m_actionText.text = action.description;
        }*/
        //m_actionButton.gameObject.SetActive(action.description.IsNullOrWhiteSpace() == false);
         
        
        m_name.text = m_character.Name;
        m_action.text = m_character.PeepAction.ToString();
        if(MACharacterBase.c_useEnergy)
            m_energy.text = $"{m_character.Energy:F1}/{m_character.MaxEnergy:F1}";
        else
            m_energy.text = $"{(m_character.Job ? m_character.Job.m_info.m_title : "None")}";
    }
    
    public static MABuildingWorkerPanelLine Create(Transform _holder, MACharacterBase _character)
    {
        var prefab = Resources.Load<MABuildingWorkerPanelLine>("_Prefabs/Dialogs/MABuildingWorkerPanelLine");
        var go = Instantiate(prefab, _holder);
        var instance = go.GetComponent<MABuildingWorkerPanelLine>();
        instance.Activate(_character);
        return instance;
    }
    
    public static MABuildingWorkerPanelLine CreateTitle(Transform _holder)
    {
        var prefab = Resources.Load<MABuildingWorkerPanelLine>("_Prefabs/Dialogs/MABuildingWorkerPanelLine");
        var go = Instantiate(prefab, _holder);
        var instance = go.GetComponent<MABuildingWorkerPanelLine>();
        
        instance.m_name.text = "<b>Name</b>";
        instance.m_action.text = "<b>Action</b>";
        if(MACharacterBase.c_useEnergy)
            instance.m_energy.text = "<b>Energy</b>";
        else
            instance.m_energy.text = "<b>Job</b>";
            
        //instance.m_actionButton.gameObject.SetActive(false);
        
        return instance;
    }
}
