using System;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class HeldByPlayer : CommonState
	{
		public HeldByPlayer(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnEnter()
		{
			m_character.CustomStop = false;
			base.OnEnter();
			m_character.SetCanBePushed(true);
			m_character.SetTargetObj(null);
			m_character.m_ragdollController.AddRagDollNotMovingListener(OnNotMoving);
		}

		private void OnNotMoving()
		{

		}

		public override void OnExit()
		{
			base.OnExit();

			m_character.m_ragdollController.RemoveRagDollStationaryListener(OnNotMoving);
		}
	}
}