using System;
using System.Collections.Generic;
using UnityEngine;

public interface IDamager
{
	public string Name { get; } 
	public TargetObject TargetObject { get; }
	public void ClearTarget(); 
	public Transform Transform { get; }
}

public class TargetObject : MonoBehaviour
{
	public static TargetObject Create(GameObject _targetGameObject, TargetType _targetType = TargetType.None)
	{
		TargetObject targetObject = _targetGameObject.GetComponent<TargetObject>();
		if (targetObject == null) targetObject = _targetGameObject.AddComponent<TargetObject>();
		if (_targetType != TargetType.None)
			targetObject.m_targetType = _targetType;
		return targetObject;
	}

	public Vector3 ClosestTargetPoint(MACharacterBase _attacker, float _maxRange = 50f, float _attackerHeight = 2.0f)
	{
		Vector3 attackerPos = _attacker.transform.position;
		Vector3 attackSourcePosition = attackerPos;
		Vector3 targetPos = transform.position;
		switch (TargetObjectType)
		{
			case TargetType.BuildingType:
			{
				float minXZDistSqd = float.MaxValue;
				MABuilding building = gameObject.GetComponent<MABuilding>();
				var blocks = building.Visuals.GetComponentsInChildren<Block>();
				foreach (Block b in blocks)
				{
					var blockPos = b.transform.position;
					float xzDistSqd = (attackerPos-blockPos).xzSqrMagnitude();
					if (xzDistSqd < minXZDistSqd)
					{
						minXZDistSqd = xzDistSqd;
						targetPos = blockPos;
					}
				}

				attackSourcePosition = attackerPos + Vector3.up * _attackerHeight;
				targetPos.y = attackSourcePosition.y;

				bool containsAreas =
					m_buildingSelf.ContainsDamageInteractionArea(_attacker, out MADamageArea damageAreaOut);
				if (containsAreas)
				{
					if (damageAreaOut == null) return Vector3.zero; //check
					var damageAreasSortedByPointDistance = m_buildingSelf.GetDamageInteractionAreas(_attacker);
					if (damageAreasSortedByPointDistance.FindIndex(x => x.isInside) == -1)
						return Vector3.zero; //_attacker.m_triggersOverlapping
				}

				Vector3 direction = targetPos - attackSourcePosition;
				Ray rayToTarget = new Ray(attackSourcePosition, direction);
				if (Physics.Raycast(rayToTarget, out RaycastHit hit, _maxRange + 1f, LayerMask.GetMask("Default"),
					    QueryTriggerInteraction.Ignore))
				{
					if (hit.collider.gameObject.GetComponentInParent<MABuilding>() == m_buildingSelf)
					{
						return hit.point;
					}
				}

				return targetPos;
			}
			case TargetType.CharacterType:
			{
				attackSourcePosition =
					attackerPos + Vector3.up * Mathf.Min(_attackerHeight, m_movingTargetSelf.m_neckHeight);
				targetPos.y = attackSourcePosition.y;
				Vector3 direction = targetPos - attackSourcePosition;
				float sqDist = direction.sqrMagnitude;
				float minDist = m_movingTargetSelf.m_bodyToBodyCollider.radius + _attacker.m_bodyToBodyCollider.radius;
				if (minDist * minDist >= sqDist) // TS - check if one one character inside another (raycast would fail)
					//knockdown ragDolling can launch characters into each other's bodyCollider areas. bodyColliders are off during that time)
				{
					return targetPos + direction.normalized * (-1) * m_movingTargetSelf.m_bodyToBodyCollider.radius;
				}

				Ray rayToTarget = new Ray(attackSourcePosition, direction);
				LayerMask layerMask = LayerMask.GetMask(NGMovingObject.c_bodyToBodyCollisionLayer);
				if (Physics.Raycast(rayToTarget, out RaycastHit hit, _maxRange + 0.1f, layerMask,
					    QueryTriggerInteraction.Ignore) &&
				    hit.collider.gameObject.GetComponentInParent<MACharacterBase>() == m_movingTargetSelf)
				{
					return hit.point;
				}

				return targetPos;
			}
			case TargetType.WallType:
				return targetPos; //PathBreak.FindAt(targetPos)?.transform.position ?? targetPos;//
			case TargetType.InteractionPointType:
				return transform.position; //m_maCharacterInteract.transform.position;//
		}

		return transform.position;
	}

	public static TargetObject Create(Component _component, TargetType _targetType = TargetType.None)
	{
		TargetObject targetObject = _component.GetComponent<TargetObject>();
		if (targetObject == null) targetObject = _component.gameObject.AddComponent<TargetObject>();
		if (_targetType != TargetType.None)
			targetObject.m_targetType = _targetType;
		return targetObject;
	}

	[NonSerialized] public MACharacterBase.TargetResult m_targetResultOnActivation;

	private MACharacterBase m_movingTargetSelf = null;
	private PathBreak m_pathBreak = null;
	public MACharacterBase MovingTargetSelf => m_movingTargetSelf;
	private MABuilding m_buildingSelf = null;
	public MABuilding BuildingSelf => m_buildingSelf;
	public IDamageReceiver DamageReceiver => m_movingTargetSelf as IDamageReceiver ?? m_buildingSelf;
	private ExplodeInteraction m_explodeInteractionSelf = null;
	private MACharacterInteract m_interactionPointSelf = null;
	public ExplodeInteraction ExplodeInteractionSelf => m_explodeInteractionSelf;

	public float Radius
	{
		get
		{
			switch (TargetObjectType)
			{
				case TargetType.BuildingType:
					return m_buildingSelf.Radius;
				case TargetType.CharacterType:
					return m_movingTargetSelf.m_bodyToBodyCollider != null
						? m_movingTargetSelf.m_bodyToBodyCollider.radius
						: 0f;
				case TargetType.WallType:
					return 0.5f;
				case TargetType.InteractionPointType:
					return 0.5f;
				case TargetType.ExplodeInteraction:
					return 0.5f;
			}

			return 0f;
		}
	}

	private HashSet<IDamager> m_targetedBy = new();
	private HashSet<IDamager> m_targetedByEnemies = new();
	public HashSet<IDamager> TargetedBy => m_targetedBy;
	public HashSet<IDamager> TargetedByEnemies => m_targetedByEnemies;

	private List<Attacker> m_distanceSortedListOfAttackers = new();
	private uint m_lastUpdateFrame = 0; //we only ever sort the attacker list once a frame max.

	private TargetType m_targetType = 0;

	public TargetType TargetObjectType
	{
		get
		{
			switch(m_targetType)
			{
				case TargetType.BuildingType:
					if (m_buildingSelf == null) m_targetType = TargetType.None;
					break;
				case TargetType.CharacterType:
					if (m_movingTargetSelf == null) m_targetType = TargetType.None;
					break;
				case TargetType.WallType:
					if (m_pathBreak == null)
					{
						m_pathBreak = GetComponent<PathBreak>();
						if (m_pathBreak == null)
						{
							m_targetType = TargetType.None;
						}
					}
					break;
				case TargetType.InteractionPointType:
					if (m_interactionPointSelf == null) m_targetType = TargetType.None;
					break;
				case TargetType.ExplodeInteraction:
					if (m_explodeInteractionSelf == null) m_targetType = TargetType.None;
					break;
			}
			return m_targetType;
		}
	}
    
    [System.Serializable]
    public class Attacker
    {
	    public IDamager m_attacker = null;
	    public float m_sqDistanceFurtherThanPreviousAttacker = 0;
	    // public int m_index;
	    // public TargetType m_targetType;
    }

    public enum TargetType
    {
        None = 0,
        BuildingType,
        CharacterType,
        WallType,
        InteractionPointType,
        Pickup,
		ExplodeInteraction
    }

    public bool IsBeingTargeted => m_targetedBy.Count > 0;
    public bool IsBeingTargetedByEnemies => m_targetedByEnemies.Count > 0;
    
    public bool AddTargeter(IDamager _damager)
    {
	    if (_damager == null) return false;
	    bool added = m_targetedBy.Add(_damager);
	    if (added)
	    {
		    m_distanceSortedListOfAttackers.Add(new Attacker(){ m_attacker = _damager });

				// RW-16-APR-25: Check if the character is actually capable of causing harm to us. This filters out characters like the 
				// Lost Boy who target the hero for the purposes of following them.
				MACharacterBase character = _damager as MACharacterBase;
				if (character != null)
				{
					if (m_movingTargetSelf != null && character.CreatureInfo.CreatureTargets.Contains(m_movingTargetSelf.CreatureInfo))
					{
						m_targetedByEnemies.Add(_damager);
					}
				}
	    }
	    return added;
    }
    
    public bool RemoveTargeter(IDamager _damager)
    {
	    bool removed = false;
		if (m_movingTargetSelf != null)
		{
			removed = m_movingTargetSelf.RemoveAttacker(_damager);
		}
		else
		{
			removed = m_targetedBy.Remove(_damager);
			m_targetedByEnemies.Remove(_damager);
		}
	    if (removed)
	    {
		    int iChar = m_distanceSortedListOfAttackers.FindIndex(x => x.m_attacker == _damager);
		    m_distanceSortedListOfAttackers[iChar].m_attacker.ClearTarget();
		    m_distanceSortedListOfAttackers.RemoveAt(iChar);
	    }
	    return removed;
    }

    public void PrepareToTakeDamage()
    {
	    switch(TargetObjectType)
        {
        	case TargetType.BuildingType:
        		//if(m_building != null);s
        		break;
        	case TargetType.CharacterType:
	            if (m_movingTargetSelf != null) m_movingTargetSelf.PrepareToTakeDamage();
        		break;
        	case TargetType.WallType:
	            break;
            case TargetType.InteractionPointType:
	            break;
			case TargetType.ExplodeInteraction:
				break;
		}
    }
    
    public void ResetTakeDamage()
    {
	    switch(TargetObjectType)
        {
        	case TargetType.BuildingType:
        		break;
        	case TargetType.CharacterType:
	            if (m_movingTargetSelf != null) m_movingTargetSelf.ResetTakeDamage();
        		break;
        	case TargetType.WallType:
	            break;
            case TargetType.InteractionPointType:
	            break;
			case TargetType.ExplodeInteraction:
				break;

		}
    }
    
    public void ClearAllAttackers()
    {
	    for(int i = m_distanceSortedListOfAttackers.Count -1; i >= 0; i--)
	    {
		    m_distanceSortedListOfAttackers[i].m_attacker.ClearTarget();
	    }
    }
    
    public Attacker GetClosestAttacker()
    {
	    if (m_distanceSortedListOfAttackers.Count == 0) return null;
	    if(m_lastUpdateFrame != Time.frameCount)
	    {
		    m_lastUpdateFrame = (uint)Time.frameCount;
		    m_distanceSortedListOfAttackers.Sort((a, b) =>
		    {
			    Vector3 pos = transform.position;
			    a.m_sqDistanceFurtherThanPreviousAttacker = (pos - a.m_attacker.Transform.position).xzSqrMagnitude();
			    b.m_sqDistanceFurtherThanPreviousAttacker = (pos - b.m_attacker.Transform.position).xzSqrMagnitude();
			    return a.m_sqDistanceFurtherThanPreviousAttacker.CompareTo(b.m_sqDistanceFurtherThanPreviousAttacker);
		    });
	    }
	    return m_distanceSortedListOfAttackers[0];
    }

    public float GetHealth()
    {
		switch(TargetObjectType)
		{
			case TargetType.BuildingType:
				return m_buildingSelf.Health;
			case TargetType.CharacterType:
				return m_movingTargetSelf.GameState.m_health;
			case TargetType.WallType:
				return m_pathBreak.RepairLevel;
			case TargetType.InteractionPointType:
				return 1f;
			case TargetType.ExplodeInteraction:
				return 1f;
		}
		return 0f;
    }

    public (Vector3 destinationPos, bool isInsideValidDamageArea) GetDestinationPosition(MACharacterBase _attacker, float _delay = 0f)
    {
        MABuilding building = GetComponent<MABuilding>();
        if (building != null)
        {
	        var areas = building.GetDamageInteractionAreas(_attacker);
	        if (areas.Count > 0)
	        {
		        foreach (var inArea in areas)
		        {
			        if (inArea.isInside) //_attacker.m_triggersOverlapping.Contains(inArea.area))
			        {//watch out we go into building this way how do we stop
				        return (building.transform.position, true);
			        }
		        }

				foreach (var areasValueTuple in areas)
		        {
			        MADamageArea damageArea = areasValueTuple.area.GetComponent<MADamageArea>();
			        if (damageArea != null)
			        {
				        if (damageArea.IsAllowed(_attacker))
				        {
					        return (damageArea.transform.position, false);
				        }
			        }
		        }
	        }

	        return (ClosestTargetPoint(_attacker, _attacker.GetNextAttackAvailableInCombo().AttackRadius), false);
        }
        else
        {
	        NavAgent nav = GetComponent<NavAgent>();
	        if (nav != null) return (nav.GetPositionIn(_delay), false);
        }

        return (transform.position, false);
    }

    public void DoDamage(IDamageReceiver.DamageSource _source, ref float _potentialDamage, Vector3 _damageOrigin, MAAttackInstance _attack = null)
    {
	    switch(TargetObjectType)
	    {
		    case TargetType.BuildingType:
			    if(m_buildingSelf != null) m_buildingSelf.ApplyDamageEffect(_source, _potentialDamage, _damageOrigin, _attack);
			    break;
		    case TargetType.CharacterType:
			    if(m_movingTargetSelf != null) m_movingTargetSelf.ApplyDamageEffect(_source, _potentialDamage, _damageOrigin, _attack);
			    break;
		    case TargetType.WallType:
			    PathBreak pb = GetComponent<PathBreak>();
			    pb.DoDamage(ref _potentialDamage, _damageOrigin);
			    break;
		    case TargetType.InteractionPointType:
			    break;
			case TargetType.ExplodeInteraction:
				if (m_explodeInteractionSelf != null) m_explodeInteractionSelf.ApplyDamageEffect(_source, _potentialDamage, _damageOrigin, _attack);
				break;
		}
    }
    
    public MonoBehaviour Obj
    {
	    get
	    {
		    switch (TargetObjectType)
		    {
			    case TargetType.BuildingType:
				    return m_buildingSelf;
			    case TargetType.CharacterType:
				    return m_movingTargetSelf;
			    case TargetType.WallType:
				    return m_pathBreak;
			    case TargetType.InteractionPointType:
				    return m_interactionPointSelf;
			    case TargetType.ExplodeInteraction:
				    return m_explodeInteractionSelf;
		    }
		    return null;
	    }
    }

    private void Awake()
    {
        TargetType targetType = TargetType.None;
        MACharacterBase characterTarget = GetComponent<MACharacterBase>(); 
        if(characterTarget != null)
        {
	        m_targetType = TargetType.CharacterType;
            m_movingTargetSelf = characterTarget;
            return;
        }
        
        m_buildingSelf = GetComponent<MABuilding>();
        if(m_buildingSelf != null)
        {
            if(targetType != TargetType.None) Debug.LogError($"CreatureTarget - Awake - Multiple target types found {targetType} & TargetType.BuildingType");
            m_targetType = TargetType.BuildingType;
            return;
        }
        
        if(GetComponent<PathBreak>() is { } pb)
        {
	        m_targetType = TargetType.WallType;
	        m_pathBreak = pb;
	        return;
        }
        
        if(GetComponent<MACharacterInteract>() is { } inter)
        {
	        m_interactionPointSelf = inter;
	        m_targetType = TargetType.InteractionPointType;
	        return;
        }

		m_explodeInteractionSelf = GetComponent<ExplodeInteraction>();
		if (m_explodeInteractionSelf != null)
		{
			if (targetType != TargetType.None) Debug.LogError($"CreatureTarget - Awake - Multiple target types found {targetType} & TargetType.ExplodeInteraction");
			m_targetType = TargetType.ExplodeInteraction;
			return;
		}

		m_targetType = targetType;
    }

    public bool ReadyForHit()
    {
	    switch(TargetObjectType)
	    {
		    case TargetType.BuildingType:
		    case TargetType.WallType:
			    return GetHealth() > 0f;
		    case TargetType.CharacterType:
			    return (m_movingTargetSelf != null &&
			            m_movingTargetSelf.CharacterUpdateState.State == CharacterStates.KnockedDown) == false;
		    case TargetType.InteractionPointType:
			    return false;
			case TargetType.ExplodeInteraction:
				return false;
		}
	    return false;
    }
}

// #if UNITY_EDITOR
// [CustomEditor(typeof(TargetObject))]
// public class TargetObjectEditor : Editor
// {
// 	public override void OnInspectorGUI()
// 	{
// 		base.OnInspectorGUI();
// 		TargetObject targetObject = target as TargetObject;
// 	}
// }
//
// #endif

	
