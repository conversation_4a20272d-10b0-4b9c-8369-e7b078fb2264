using System;
using UnityEngine;

namespace MACharacterStates
{
	public class HeroSpawn : Spawn
	{
		public HeroSpawn(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnUpdate()
		{
			if (HasAgentArrived()) // || Time.time >= m_stopTime)
			{
				if (m_character.ObjectiveWaypoint != null)
				{
					ApplyState(CharacterStates.PatrolToWaypoint);
				}
				else
				{
					ApplyState(CharacterStates.RoamForTarget);
				}
			}
		}
	}
}