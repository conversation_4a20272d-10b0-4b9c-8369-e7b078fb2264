using System;
using UnityEngine;
// public class ZombieAttack : Attack
// {
// 	public ZombieAttack(MACreatureBase.CREATURE_STATE _state, MACharacterBase _character) : base(_state, _character) { }
//
// 	public override void OnEnter() { base.OnEnter(); }
//
// 	public override void OnUpdate() {
// 		base.OnUpdate();
// 		//m_creature.StateCreatureAttack();
// 	}
// 	public override void OnExit() { base.OnExit(); }
// }
namespace MACharacterStates
{
	[Serializable]
	public class AttackClose : CommonState
	{
		public AttackClose(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		private const float m_tickEvaluateTarget = 1f;

		[ReadOnlyInspector][SerializeField] private float m_timeEvaluateTarget = -1f;
		
		private TargetObject m_lastTargetObject = null;
		private MACharacterBase m_targetCharacter = null;
		private TargetObject m_characterTarget = null;
		
		public override void OnEnter()
		{
			base.OnEnter();

			m_character.BlendAnimatorLayerWeight("Combat", 1);
			// m_character.SetCanBePushed(false);

			m_timeEvaluateTarget = Time.time + m_tickEvaluateTarget;

			m_character.m_onCollisionWithTarget -= OnCollisionWithTarget;
			m_character.m_onCollisionWithTarget += OnCollisionWithTarget;
			m_character.m_onRangeAttackFired -= OnRangeAttackFired;
			m_character.m_onRangeAttackFired += OnRangeAttackFired;
			m_character.m_comboStateUpdate -= ComboStateUpdated;
			m_character.m_comboStateUpdate += ComboStateUpdated;

			m_character.PrepareToDoDamage();
			UpdateTarget();
		}

		void ComboStateUpdated()
		{
			if (!m_character.IsComboInCooldown())
			{
				m_character.m_nav.PopPause("AttackClose");
				if (!m_character.CurrentAttack.CanMove)
					m_character.m_nav.PushPause("AttackClose", false, true);

				int result = TryStartAttack(true); //Result of 2 means we should maybe reconsider our target
				if (result == 2 && Time.time > m_timeEvaluateTarget)
				{
					m_timeEvaluateTarget = Time.time + m_tickEvaluateTarget;
					if (EvaluateTarget(0.8f))
						return;
				}
			}
		}

		public override void OnUpdate()
		{
			base.OnUpdate();

			if (m_character.CurrentAttack == null)
			{
				// RW-13-MAR-25: We shouldn't update the target while playing an attack anim.
				// Doing so risks changing state, which may lead to sliding around while playing the anim.
				UpdateTarget();

				if (!m_character.IsTurning)
				{
					TryStartAttack();
				}
			}
			
			TargetObject target = m_character.TargetObject;
			if (target != null && target.TargetObjectType == TargetObject.TargetType.CharacterType)
			{
				MACharacterBase enemy = target.GetComponent<MACharacterBase>();			
				m_character.AddExperience(m_character.CreatureInfo.m_inCombatExp * enemy.Level, "fight");
			}
		}

		private bool UpdateTarget()
		{
			if (m_lastTargetObject == m_character.TargetObject)
				return false;
			m_lastTargetObject = m_character.TargetObject;
			if (m_lastTargetObject == null)
			{
				EvaluateTarget();
				return false;
			}
			m_targetCharacter = m_lastTargetObject.GetComponent<MACharacterBase>();
			m_characterTarget = TargetObject.Create(m_lastTargetObject);

			// RW-12-MAR-25: The character shouldn't rotate to face a new target mid-combo unless
			// the target is reacahable with an attack. Otherwise it looks weird when they spin on
			// the spot then just stand there while on cooldown.
			if (m_character.CanReachTargetWithAttack(m_character.CurrentComboState.GetNextAttack(), out var targetPos))
			{
				m_character.LookAt(m_lastTargetObject.transform.position);
			}
			return true;
		}
		
		protected override string GetAttackStateForTarget()
		{
			TargetObject targetObject = m_character.TargetObject;
			if (targetObject == null)
			{
				return m_character.ObjectiveWaypoint != null
					? CharacterStates.ReturnToPatrol
					: DefaultState;
			}
			
			Transform targetTransform = targetObject.transform;
			if (!m_character.IsTargetAliveAndValid(targetTransform) ||
			    !m_character.IsTargetAudible(targetTransform.position))
			{
				switch (targetObject.TargetObjectType)
				{
					case TargetObject.TargetType.InteractionPointType:
						return CharacterStates.Interaction;
				}
				
				return m_character.ObjectiveWaypoint != null
					? CharacterStates.ReturnToPatrol
					: DefaultState;
			}

			var attack = m_character.CurrentAttack;
			if (attack == null)
			{
				attack = m_character.GetNextAttackAvailableInCombo();
			}

			string attackOrChase = AttackOrChase(attack);
			if (attackOrChase.IsNullOrWhiteSpace() == false)
			{
				return attackOrChase;
			}
			
			if(!m_character.IsTargetVisible(targetObject.transform.position, out bool isTargetWithinVisionRadius))
			{
				if(m_character.ObjectiveWaypoint != null && !isTargetWithinVisionRadius)
				{
					return CharacterStates.ReturnToPatrol;
				}
				else
				{
					string smashSmash = CheckSmashWallsInPath();
					if(string.IsNullOrWhiteSpace(smashSmash) == false)
						return smashSmash;
					
					if (m_gameStateData.m_guardObjectiveWaypoint)
					{
						return FindSearchStateForGuardMode(CharacterStates.ChaseTarget, isTargetWithinVisionRadius);
					}
					return CharacterStates.ReturnToPatrol;
				}
			}
			
			switch (targetObject.TargetObjectType)
			{
				// case TargetObject.TargetType.BuildingType:
				// 	MABuilding home = m_character.MyHome as MABuilding;
				// 	if (home != null && targetObject.GetComponent<MABuilding>() == home)
				// 		return CharacterStates.GoingHome;
				// 	break;
				case TargetObject.TargetType.InteractionPointType:
					return CharacterStates.Interaction;
			}

			if(m_character.ObjectiveWaypoint != null)
			{
				if (m_gameStateData.m_guardObjectiveWaypoint)
				{
					return FindSearchStateForGuardMode(CharacterStates.ChaseTarget, isTargetWithinVisionRadius);
				}
				else
				{
					Vector3 posNearestOriginalPath = NavAgent.GetPathPosition(m_character.LastPatrolPath, m_character.transform.position);
					if((posNearestOriginalPath - targetObject.GetDestinationPosition(m_character).destinationPos).xzSqrMagnitude() > m_character.VisionRadiusSq)
						return CharacterStates.ReturnToPatrol;
				}
			}
			
			return CharacterStates.ChaseTarget;
		}
		
		private int TryStartAttack(bool _breakComboImmediately=false)
		{
			var state = GetAttackStateForTarget();
			if (state != CharacterStates.AttackClose)
			{
				// RW-09-JAN-25: Typically, combo breaks should be queued rather than done immediately.
				// This allows the next CheckNewState call to pick it up and process it correctly.
				// However, if TryStartAttack is being called by ComboStateUpdated, we're effectively 
				// already inside a CheckNewState call and we want to process the break immediately so
				// the calling function doesn't play the next attack anim.
				if (_breakComboImmediately)
				{
					m_character.CurrentComboState.BreakCombo();
				}
				else
				{
					m_character.CurrentComboState.QueueComboBreak();
				}

				ApplyState(state);
				return 1; //Not able to attack because of our state
			}

			// RW-09-JAN-25: I'm disabling this early exit for the time being. We may need to restore it later,
			// but at the moment, the combo system has the assumption that one attack begins immediately after another 
			// and we wouldn't be at this point in the code if there wasn't an enemy in hit range for the next attack,
			// based on where we're currently facing.
			// One alternative is to make unpossessed characters turn much faster, like they now do when possessed.
			if (m_character.IsTurning)
			{
				//return 1; //Not able to attack because of our state
			}
			if (!m_characterTarget.ReadyForHit())
				return 2; //Not able to attack because of enemy state
			
			m_character.PrepareToDoDamage();
			m_character.QueueNextAttack(false);
			return 0; //We're attacking
		}

		private void OnCollisionWithTarget()
		{
			//m_character.m_onCollisionWithTarget -= OnCollisionWithTarget;
			AttackMelee();
			// OnAttackAnimFinished(null, false);
		}

		private void OnRangeAttackFired()
		{
			// m_character.m_onRangeAttackFired -= OnRangeAttackFired;
			AttackRange();
			// OnAttackAnimFinished(null, false);
		}

		// private void OnAttackAnimFinished(MAAnimationSet.AnimationParams _animInfo, bool _interrupted)
		// {
		// 	//m_character.m_onCollisionWithTarget -= OnCollisionWithTarget;
		// 	//m_character.RemoveAnimListener(OnAttackAnimFinished);

		// 	// if (/*!m_character.InState("AttackClose") ||*/ m_character.GetComponentInChildren<AnimationOverride>() != null)
		// 	// 	return;

		// 	//if (m_character.CharacterSettings.m_attackCooldown > 0)
		// 		//ApplyState(CharacterStates.AttackCooldown);
		// 	//else   //do not do this, we MUST leave the state after an attack. animation can keep playing and finish during cooldown
		// 		//EvaluateTarget(1f);
		// }
		
		private void AttackMelee()
		{
			var target = m_character.GetBestTarget();
			m_character.SetTargetObj(target);
			if (m_character.TargetObject == null)
				return;
			
			m_character.DoAttackOnTarget();
		}

		private void AttackRange()
		{
			var target = m_character.GetBestTarget();
			m_character.SetTargetObj(target);
			if (m_character.TargetObject == null)
				return;
			
			m_character.DoRangeAttackOnTarget();
		}

		public override void OnExit()
		{
			base.OnExit();

			m_character.ResetDoDamage();
			if (m_targetCharacter != null)
				m_targetCharacter.ResetTakeDamage();
			
			m_character.m_nav.PopPause("AttackClose");
			// m_character.SetCanBePushed(true);
			
			m_character.m_onCollisionWithTarget -= OnCollisionWithTarget;
			m_character.m_onRangeAttackFired -= OnRangeAttackFired;
			m_character.m_comboStateUpdate -= ComboStateUpdated;
			// m_character.RemoveAnimListener(OnAttackAnimFinished);

			//m_character.CurrentComboState.QueueComboBreak();
		}
	}

	[Serializable]
	public class DogAttackClose : AttackClose
	{
		public DogAttackClose(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnUpdate()
		{
			base.OnUpdate();
			
			if(m_character.TargetObject != null) m_character.LookAt(m_character.TargetObject.transform.position);
		}
	}

}