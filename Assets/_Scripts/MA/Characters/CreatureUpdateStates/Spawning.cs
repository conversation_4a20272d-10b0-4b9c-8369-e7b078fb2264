using System;
using UnityEngine;

namespace MACharacterStates
{
    [Serializable]
    public class Spawn : CommonState
    {
        private int elapsedFrames = 0;
        private bool m_playingAnim = false;
        public bool PlayingAnim
        {
            get { return m_playingAnim; }
            set { m_playingAnim = value; }
        }

        public Spawn(string _state, MACharacterBase _character) : base(_state, _character)
        {
        }

        public override void OnEnter()
        {
            base.OnEnter();

            elapsedFrames = 0;
            
            if(m_character.m_insideMABuilding != null)
            {
                Vector3 awayFromDoor = m_character.m_insideMABuilding.DoorPosOuter - m_character.m_insideMABuilding.DoorPosInner;
                Vector3 awayFromDoorNorm = awayFromDoor.GetXZ().normalized;
                awayFromDoorNorm = awayFromDoorNorm.RotateAbout(Vector3.zero, UnityEngine.Random.Range(0f, 1f) * Mathf.PI * 0.25f);
                float dist = 10f;
                Vector3 firstDestPos = m_character.transform.position +
                                       awayFromDoorNorm * (dist * 0.5f + dist * 0.5f * UnityEngine.Random.Range(0f, 1f));
                m_gameStateData.m_speed = m_gameStateData.m_walkSpeed;
                m_character.SetMoveToPosition(firstDestPos);//changed from direct to indirect due to recent building/move changes
            }
            else
            {
                ToggleVisual(false);
                m_playingAnim = true;
                if (!m_character.PlaySpawnAnim("Spawn"))
                {
                    m_playingAnim = false;
                    ApplyState(CharacterStates.RoamForTarget);
                }
            }

#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
            MANightChallengeManager.Me.CharacterSpawned(m_character);
#endif
        }

        public override void OnUpdate()
        {
            base.OnUpdate();

            if (m_character.m_insideMABuilding == null)
            {
                if (elapsedFrames > 0)
                {
                    ToggleVisual(true);
                }
            }
            
            if(HasAgentArrived() && m_playingAnim == false)
            {
                ApplyState(CharacterStates.RoamForTarget);
            }
            
            ++elapsedFrames;
        }

        public override void OnExit()
        {
            m_gameStateData.m_speed = m_gameStateData.m_walkSpeed;
            ToggleVisual(true);
        }

        private void ToggleVisual(bool enable)
        {
            var visual = m_character.SkinnedMeshRenderer();
            if (visual != null)
                visual.enabled = enable;
        }
    }

    [Serializable]
    public class Despawn : CommonState
    {
        private float m_despawnTime = 3.25f;
        public float DespawnTime
        {
            get { return m_despawnTime; }
            set { m_despawnTime = value; }
        }

        public Despawn(string _state, MACharacterBase _character) : base(_state, _character)
        {
        }

        public override void OnEnter()
        {
            base.OnEnter();

            var nightChallenge = MANightChallengeManager.Me;
            if (m_character.IsHarvestingCreataure)
            {
                if (m_character.Carrying != null)
                {
                    nightChallenge.ResourceStolen(m_character.Carrying.Contents);
                    m_character.Carrying.DestroyMe();
                }
            }
            else if (m_character.IsEscortedCreataure)
            {
                nightChallenge.EscorteeSaved(m_character);
            }

             m_character.m_nav.Pause(true, true);
             m_character.m_nav.Pause(false, true);
             if (m_character.m_insideMABuilding != null || !m_character.PlayDespawnAnim("Despawn"))
             {
                 m_despawnTime = 0;
             }
             m_character.CharacterDespawned?.Invoke(m_character);
        }

        public override void OnUpdate()
        {
            base.OnUpdate();
            m_despawnTime -= Time.deltaTime;
            if(m_despawnTime <= 0)
            {
                m_character.DestroyMe(); //use despawn animation if available & despawn over time
            }
        }

        public override void OnExit()
        {
            m_character.m_nav.StopNavigation();
        }
    }
}
