using System;
using System.Collections;
using UnityEngine;

// public class ZombieAttack : Attack
// {
// 	public ZombieAttack(MACreatureBase.CREATURE_STATE _state, MACharacterBase _character) : base(_state, _character) { }
//
// 	public override void OnEnter() { base.OnEnter(); }
//
// 	public override void OnUpdate() {
// 		base.OnUpdate();
// 		//m_creature.StateCreatureAttack();
// 	}
// 	public override void OnExit() { base.OnExit(); }
// }
namespace MACharacterStates
{
	[Serializable]
	public class AttackMoveCloser : CommonState
	{
		public AttackMoveCloser(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		protected MACharacterBase m_targetCharacter = null;
		
		public override void OnEnter()
		{
			base.OnEnter();

			if (m_character.TargetObject != null)
			{
				m_targetCharacter = m_character.TargetObject.GetComponent<MACharacterBase>();
			}
			
			if(m_character.IsTargetAliveAndValid(m_character.TargetObject.transform) == false)
			{
				ApplyState(CharacterStates.LookForTarget);
				return;
			}
			m_character.BlendAnimatorLayerWeight("Combat", 1);
			m_character.m_nav.Pause(false, true);
		}
		
		public override void OnUpdate()
		{
			base.OnUpdate();

			if(m_character.IsTargetAliveAndValid(m_character.TargetObject.transform) == false)
			{
				ApplyState(CharacterStates.LookForTarget);
				return;
			}

			var toTarget = m_targetCharacter.transform.position - m_character.transform.position;
			m_character.transform.forward = Vector3.Lerp(m_character.transform.forward, toTarget.normalized, 0.1f);

			var state = GetAttackStateForTarget();
			if (state != CharacterStates.AttackMoveCloser)
			{
				ApplyState(state);
				return;
			}
		}
	}
}