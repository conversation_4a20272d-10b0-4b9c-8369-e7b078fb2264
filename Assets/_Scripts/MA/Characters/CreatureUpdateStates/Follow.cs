using System;
using UnityEngine;
using Vector3 = UnityEngine.Vector3;

namespace MACharacterStates
{
	[Serializable]
	public class Follow : CommonState
	{
		private const float c_tickUpdateLeaderPos = 0.5f;

		[ReadOnlyInspector] [SerializeField] protected float m_timeEvaluateTarget = -1;
		[ReadOnlyInspector] [SerializeField] private float m_timeUpdateLeaderPos = -1f;
		protected float m_tickEvaluateTarget = 1f;

		public Follow(string _state, MACharacterBase _character) : base(_state, _character) { }

		public override void OnEnter()
		{
			base.OnEnter();
			m_character.DisableObjective();
		}

		public override void OnUpdate()
		{
			base.OnUpdate();

			if (Time.time > m_timeEvaluateTarget)
			{
				if (EvaluateTarget())
					return;

				m_timeEvaluateTarget = Time.time + m_tickEvaluateTarget;
			}

			if (m_timeUpdateLeaderPos < Time.time)
			{
				MACharacterBase leader = m_character.Leader as MACharacterBase;
				var selfPos = m_character.transform.position;
				if (leader == null)
				{
					ApplyState(CharacterStates.RoamForTarget);
					return;
				}
				
				
				var leaderPos = leader.transform.position;
				var leaderToSelfDirect = selfPos - leaderPos;
				var leaderToSelfDirectDist = leaderToSelfDirect.xzMagnitude();
				float allowedDistance = (leader.m_bodyToBodyCollider.radius * 0.5f +
				                        m_character.m_bodyToBodyCollider.radius * 0.5f +
				                        m_character.CharacterSettings.m_followModeDistance) + NavAgent.c_distanceToFinalPositionThreshold;

				bool agentArrived = HasAgentArrived();
				bool leaderTooFar = leaderToSelfDirectDist > allowedDistance;
				if (agentArrived == false || leaderTooFar)
				{
					Vector3 followPos = Vector3.zero;
					Vector3 leaderToSelf = Vector3.zero;
					if (leader.m_insideMABuilding != null)//m_character.IsLeaderAvailable == false)
					{
						Vector3 offset = leader.m_insideMABuilding.DoorPosOuter.normalized;
						followPos = leader.m_insideMABuilding.DoorPosOuter + offset;
						leaderToSelf = selfPos.GetXZ() - followPos.GetXZ();
					}
					else
					{
						Vector3 futurePos = leader.m_nav.GetPositionIn(c_tickUpdateLeaderPos);
						Vector3 offsetLeaderPosToFuturePos = futurePos.GetXZ() - leaderPos.GetXZ();
						float leaderPosToFuturePosDist = offsetLeaderPosToFuturePos.magnitude;

						leaderToSelf = selfPos.GetXZ() - futurePos.GetXZ();
						
						Vector3 offset = Mathf.Approximately(leaderPosToFuturePosDist, 0f)
							? leader.transform.forward
							: offsetLeaderPosToFuturePos.normalized;
						
						Vector3 perp = offset.PerpendicularXZ();
						Vector3 side1 = perp.GetXZ();
						Vector3 side2 = side1 * -1f;
						offset = (selfPos - side1).xzSqrMagnitude() < (selfPos - side2).xzSqrMagnitude()
							? side1
							: side2;
/*
						GameManager.Me.ClearGizmos("Destination1");
						GameManager.Me.AddGizmoPoint("Destination1",
							(futurePos + side1 * allowedDistance).GroundPosition(1f), 1f, Color.yellow);
						GameManager.Me.ClearGizmos("Destination2");
						GameManager.Me.AddGizmoPoint("Destination2",
							(futurePos + side2 * allowedDistance).GroundPosition(1f), 1f, Color.green);
*/
						followPos = futurePos + offset * allowedDistance;
					}
					
					if (selfPos.DistanceSq(followPos) < NavAgent.c_distanceToFinalPositionThreshold)	
					{
						m_timeUpdateLeaderPos = Time.time + c_tickUpdateLeaderPos;
						if (m_character.IsLeaderAvailable == false)
						{
							m_character.SetToGuard(leaderPos, allowedDistance);
							ApplyState(CharacterStates.GuardLocation);
						}
						return;
					}
					
					m_character.SetMoveToPosition(followPos);
					
					float leaderToSelfDist = leaderToSelf.magnitude;
					float speed = m_gameStateData.m_attackSpeed;
					if (leaderToSelfDist < 2f)
					{
						speed = leader.GetDesiredSpeed();
					}
					else if (leaderToSelfDist < 5f)
					{
						speed = m_gameStateData.m_walkSpeed;
					}
					
					m_character.SetSpeed(speed);
					m_timeUpdateLeaderPos = Time.time + c_tickUpdateLeaderPos;
				}
				else
				{
					if (m_character.IsLeaderAvailable == false)
					{
						m_character.SetToGuard(leaderPos, allowedDistance);
						ApplyState(CharacterStates.GuardLocation);
						return;
					}
				}
			}
		}

		public override void OnExit()
		{
			base.OnExit();
			//m_character.m_nav.PopPause("Follow");
		}
	}
	
	[Serializable]
	public class DogFollow : Follow
	{		
		private MADog Dog => m_character as MADog;
		
		public DogFollow(string _state, MACharacterBase _character) : base(_state, _character) { }

		public override void OnEnter()
		{
			base.OnEnter();
		}
		

		public override void OnUpdate()
		{
			base.OnUpdate();
			if (m_character.IsNotBusy && m_character.TargetObject == null)
			{
				if (m_character.Leader != null)
				{
					if ((m_character.Leader as MACharacterBase).IsAnyDeadState)
					{
						ApplyState(CharacterStates.Mourn);
						return;
					}
				}
				Dog.PooCheck();
				Dog.FoodCheck();
			}
		}

		public override void OnExit()
		{
			base.OnExit();
		}
	}
}


