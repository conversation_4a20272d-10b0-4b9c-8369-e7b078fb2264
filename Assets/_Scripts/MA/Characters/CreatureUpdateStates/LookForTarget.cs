using System;
using System.Collections.Generic;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class LookForTarget : CommonState
	{
		private const float m_tickEvaluateTarget = 0.5f;
		private const float c_tickUpdateTargetPos = 2f;

		[ReadOnlyInspector][SerializeField] private float m_timeEvaluateTarget = -1f;
		[ReadOnlyInspector][SerializeField] private float m_timeUpdateTargetPos = -1f;

		private Vector2 m_currentTargetPos = Vector3.zero;

		public LookForTarget(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnEnter()
		{
			base.OnEnter();
			m_character.BlendAnimatorLayerWeight("Combat", 1);
			m_character.m_nav.Unpause();
			m_gameStateData.m_speed = m_gameStateData.m_attackSpeed;
			m_timeEvaluateTarget = Time.time + m_tickEvaluateTarget;
			if (m_character.TargetObject == null)
			{
				m_timeEvaluateTarget = 0f;
			}
		}

		public override void OnUpdate()
		{
			base.OnUpdate();

			if (Time.time > m_timeEvaluateTarget)
			{
				if (EvaluateTarget(1.2f))
					return;

				m_timeEvaluateTarget = Time.time + m_tickEvaluateTarget;
			}

			// var state = GetAttackStateForTarget();
			// if (state != CharacterStates.LookForTarget)
			// {
			// 	ApplyState(state);
			// 	return;
			// }
			
			TargetObject targetObject = m_character.TargetObject;
			if (m_timeUpdateTargetPos <= Time.time)
			{
				if (m_currentTargetPos == Vector2.zero)
					m_currentTargetPos = m_gameStateData.m_targetPos.GetXZVector2();

				bool insideValidDamageArea = false;
				if (targetObject != null)
				{
					var destPos = targetObject.GetDestinationPosition(m_character);
					m_gameStateData.m_targetPos = destPos.destinationPos;
					insideValidDamageArea = destPos.isInsideValidDamageArea;
				}

				//float deltaPosChangeSqXZ = (m_currentTargetPos - m_gameStateData.m_targetPos.GetXZVector2()).SqrMagnitude();
				//if (deltaPosChangeSqXZ >= 2f || m_character.m_nav.IsNavigating == false)
				{
					m_currentTargetPos = m_gameStateData.m_targetPos.GetXZVector2();
					m_character.SetMoveToPosition(m_gameStateData.m_targetPos, insideValidDamageArea);
				}
				
				m_timeUpdateTargetPos = Time.time + c_tickUpdateTargetPos;
			}
		}
	}
}