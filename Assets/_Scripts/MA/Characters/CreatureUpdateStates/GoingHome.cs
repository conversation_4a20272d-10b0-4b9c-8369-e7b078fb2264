using System;
using System.Collections.Generic;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class GoingHome : CommonState
	{
		private MASpawnPoint m_originSpawn = null;
		private Vector3? m_originSpawnPosition = null;
		protected float m_tickEvaluateTarget = 1f;
		private bool m_haveShownStuckWarning = false;

		private Vector3 m_debugDesiredHomePos = Vector3.zero;
		private MABuilding m_debugDesiredHomeBuilding = null;

		[ReadOnlyInspector] [SerializeField] protected float m_timeEvaluateTarget = -1;

		public GoingHome(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		private float m_renavTimer = 0f;
		
		public override void OnEnter()
		{
			base.OnEnter();

			m_character.BlendAnimatorLayerWeight("Combat", 0);
			m_gameStateData.m_speed = m_gameStateData.m_attackSpeed;

			//m_character.DisableGuard();

			if (m_gameStateData.m_optionalDespawnPosition != Vector3.zero)
			{
				m_debugDesiredHomePos = (Vector3)m_gameStateData.m_optionalDespawnPosition;
				m_character.m_onPathProcessed -= OnPathReturned;
				m_character.m_onPathProcessed += OnPathReturned;
				m_character.SetMoveToPosition((Vector3)m_gameStateData.m_optionalDespawnPosition);
				return;
			}

			FindWayHome();
		}
		
		public override void OnUpdate()
		{
			base.OnUpdate();
			
			if (m_renavTimer > 0f)
			{
				m_renavTimer -= Time.deltaTime;
				
				if(m_renavTimer <= 0f)
				{
					m_renavTimer = 0f;
					m_character.m_onPathProcessed -= OnPathReturned;
					m_character.m_onPathProcessed += OnPathReturned;
					FindWayHome();
				}
			}
			
			if (Time.time > m_timeEvaluateTarget)
			{
				if (EvaluateTarget())
					return;

				m_timeEvaluateTarget = Time.time + m_tickEvaluateTarget;
			}

			if (HasAgentArrived(true) ||  
			    (m_character.CreatureInfo.IsDayWalker == false && m_character.CreatureInfo.IsNightWalker && DayNight.Me.IsTimeToDespawnNightCreatures))
			{
				if (m_character is MACreatureBase) ApplyState(CharacterStates.Despawn);
				else if (m_character.CharacterGameState.m_optionalDespawnPosition != Vector3.zero &&
				         m_character.m_nav.OriginalTargetPosition.GetXZVector2().Approximately(m_character.CharacterGameState.m_optionalDespawnPosition.GetXZVector2()))
				{
					ApplyState(CharacterStates.Despawn);
				}
			}
			else if(m_character.m_nav.IsStuck)
			{
				if (m_haveShownStuckWarning == false)
				{
					m_haveShownStuckWarning = true;
					Debug.LogWarning($"{m_character.GetType()} - {m_character.name} - GoingHome - HasAgentArrived(true): {HasAgentArrived(true)} || m_character.m_nav.IsStuck: {m_character.m_nav.IsStuck}, stuckTime {m_character.m_nav.StuckTime}");
				}

				if (m_character is MACreatureBase)
				{
					Debug.LogWarning($"{m_character.GetType()} - {m_character.name} - GoingHome - DESPAWNING STUCK CHARACTER m_character.m_nav.IsStuck: {m_character.m_nav.IsStuck}, stuckTime {m_character.m_nav.StuckTime}");
					ApplyState(CharacterStates.Despawn);
				}
			}
		}

		public override void OnExit()
		{
			m_character.m_onPathProcessed -= OnPathReturned;
			base.OnExit();
		}

		Transform FindAndChooseDespawnArea(Transform _spawnPoint)
		{
			List<MADespawnArea> maDespawnAreas = new();
			_spawnPoint.GetComponentsInChildren<MADespawnArea>(maDespawnAreas);
			
			var spawnPositions = new List<Transform>();
			foreach (MADespawnArea maDespawnArea in maDespawnAreas)
			{
				Transform tr = maDespawnArea.transform;
				if(MASpawnArea.AssertNavPos(tr.position) == false)
				{
					Debug.LogWarning($"Invalid DeSpawn area position '{maDespawnArea.name}' has navPoint Type 'BuildingInner' or 'EnterBuilding'" +
														$"Ignoring. Is Inside Building, not valid for character: '{m_character.name}'");
					continue;
				}
				spawnPositions.Add(tr);
			}
			
			spawnPositions.Add(_spawnPoint.transform);

			if (spawnPositions.Count > 0)
			{
				int iDeSpawnIndex = UnityEngine.Random.Range(0, spawnPositions.Count);
				return spawnPositions[iDeSpawnIndex];
			}
			return null;
		}

		private void FindWayHome()
		{
			m_originSpawn = m_character.FindOriginSpawnPoint();
			if (m_originSpawn == null)
			{
				if (m_character.Home && m_character.Home.Building)
				{
					var home = m_character.Home.Building;
					Transform despawnTransform = FindAndChooseDespawnArea(home.transform);
					
					if (despawnTransform != null)
					{
						if (despawnTransform == home.transform)
						{
							m_debugDesiredHomePos = home.transform.position;
							m_debugDesiredHomeBuilding = home;
							m_character.m_onPathProcessed -= OnPathReturned;
							m_character.m_onPathProcessed += OnPathReturned;
							m_character.m_nav.StopNavigation();
							m_character.SetMoveToBuilding(home);
						}
						else
						{
							m_originSpawnPosition = despawnTransform.position;
							m_debugDesiredHomePos = (Vector3)m_originSpawnPosition;
							m_character.m_onPathProcessed -= OnPathReturned;
							m_character.m_onPathProcessed += OnPathReturned;
							m_character.m_nav.StopNavigation();
							m_character.SetMoveToPosition((Vector3)m_originSpawnPosition);
						}
					}
					else
					{
						m_debugDesiredHomePos = home.transform.position;
						m_debugDesiredHomeBuilding = home;
						m_character.m_onPathProcessed -= OnPathReturned;
						m_character.m_onPathProcessed += OnPathReturned;
						m_character.m_nav.StopNavigation();
						m_character.SetMoveToBuilding(home);
					}
				}
				else
				{
					var namedPoint = NamedPoint.GetNamedPoint(m_character.CharacterGameState.m_spawnPointName);
					if (namedPoint != null)
					{
						var despawnTransform = FindAndChooseDespawnArea(namedPoint.transform);
						m_originSpawnPosition = despawnTransform.position;
						m_debugDesiredHomePos = (Vector3)m_originSpawnPosition;
						m_character.m_onPathProcessed -= OnPathReturned;
						m_character.m_onPathProcessed += OnPathReturned;
						m_character.m_nav.StopNavigation();
						m_character.SetMoveToPosition((Vector3)m_originSpawnPosition);
					}
					else
					{
						Debug.LogWarning($"{GetType().Name} - No valid despawn position to go home to for character: '{m_character.name}'. Despawning");
						ApplyState(CharacterStates.Despawn);
						return;
					}
				}
			}
			else
			{
				var spawnPositions = m_originSpawn.SpawnPositions;
				if (spawnPositions != null && spawnPositions.Count > 0)
				{
					int iDeSpawnIndex = UnityEngine.Random.Range(0, spawnPositions.Count);
					m_originSpawnPosition = spawnPositions[iDeSpawnIndex].transform.position;
				}
				else
				{
					m_originSpawnPosition = m_originSpawn.transform.position;
				}

				m_debugDesiredHomePos = (Vector3)m_originSpawnPosition;
				m_character.m_onPathProcessed -= OnPathReturned;
				m_character.m_onPathProcessed += OnPathReturned;
				m_character.SetMoveToPosition((Vector3)m_originSpawnPosition);
			}
		}

		protected override string GetAttackStateForTarget()
		{
			string smashSmash = CheckSmashWallsInPath();
			if(string.IsNullOrWhiteSpace(smashSmash) == false)
				return smashSmash;

			TargetObject targetObject = m_character.TargetObject;
			if (targetObject == null)
			{
				return CharacterStates.GoingHome;
			}

			if (targetObject.TargetObjectType == TargetObject.TargetType.CharacterType)
			{
				TargetObject selfAsTarget = m_character.GetComponent<TargetObject>();
				MACharacterBase targetCharacter = targetObject.GetComponent<MACharacterBase>();
				if(targetCharacter != null && selfAsTarget != null && selfAsTarget.TargetedBy.Contains(targetCharacter as IDamager))
				{
					var attack = m_character.GetNextAttackAvailableInCombo();
					// <EMAIL> - If the character is already GoingHome
					// only attack a target that is in the way with a non-ranged attack
					if (!attack.IsProjectile)
					{
						string attackOrChase = AttackOrChase(attack);
						if(attackOrChase.IsNullOrWhiteSpace() == false)
						{
							return attackOrChase;
						}
					}
				}
			}

			return CharacterStates.GoingHome;
		}	
		
		protected void OnPathReturned(NGMovingObject.TargetReachability _targetReachability)
		{
			m_character.m_onPathProcessed -= OnPathReturned;
			switch(_targetReachability)
			{
				case NGMovingObject.TargetReachability.IsReachable:
				case NGMovingObject.TargetReachability.IsNotReachable:
					break;
				case NGMovingObject.TargetReachability.IsNotReachableAndAlreadyAtNearestPos:
				case NGMovingObject.TargetReachability.PathFindingAlreadyInProgress:
				case NGMovingObject.TargetReachability.PathFailure:
					m_renavTimer = 3f;
					m_character.m_nav.StopNavigation();
					Debug.LogWarning($"GoingHome - character {m_character.name} - {_targetReachability.ToString()} - desiredpos: {m_debugDesiredHomePos.ToString()} - desiredHome: {(m_debugDesiredHomeBuilding?.name ?? "n/a")}");
                    break;
			}
		}

	}
}