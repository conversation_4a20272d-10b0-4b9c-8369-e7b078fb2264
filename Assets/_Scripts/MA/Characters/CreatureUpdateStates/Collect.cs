using System;
using UnityEngine;

namespace MACharacterStates
{
	[Serializable]
	public class Collect : CommonState
	{
		public Collect(string _state, MACharacterBase _character) : base(_state, _character)
		{
		}

		public override void OnEnter()
		{
			base.OnEnter();

			m_character.SetMoveToBuilding(m_character.m_destinationMABuilding);
			m_character.BlendAnimatorLayerWeight("Combat", 0);
			m_gameStateData.m_speed = m_gameStateData.m_attackSpeed;
		}

		public override void OnUpdate()
		{
			base.OnUpdate();
			
		}

		public override void OnExit()
		{
			base.OnExit();
		}
	}
}