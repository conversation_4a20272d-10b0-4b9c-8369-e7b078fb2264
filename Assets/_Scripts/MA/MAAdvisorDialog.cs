using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class MAAdvisorDialog : MAGUIBaseSingleton<MAAdvisorDialog>
{
    public Image m_advisorImage;
    public TMP_Text m_advisorName;
    public TMP_Text m_advisorTitle;
    public TMP_Text m_advisorExplainer;    
    public TMP_Text m_advisorMessage;

    public void ClickedMe()
    {
        Destroy(gameObject);
    }
    void Activate(NGBusinessAdvisor _advisor, string _message)
    {
        base.Activate();
        m_advisorName.text = $"{_advisor.m_firstName} {_advisor.m_givenName}";
        m_advisorTitle.text = _advisor.m_title;
        m_advisorExplainer.text = _advisor.m_info;
        m_advisorImage.sprite = _advisor.PortaitSprite;
        if(_message.IsNullOrWhiteSpace() == false)
            m_advisorMessage.text = _message;
        else
        {
            m_advisorMessage.text = "";
        }
    }
    
    public static MAAdvisorDialog Create(NGBusinessAdvisor _advisor, string _message)
    {
        if(Me != null)
        {
            Me.DestroyMe();
        }
        
        var go = Instantiate(NGBusinessDecisionManager.Me.m_MAAdvisorDialogPrefab, NGManager.Me.m_centreScreenHolder);
        var ad = go.GetComponent<MAAdvisorDialog>();
        ad.Activate(_advisor, _message);
        return ad;
    }
}
