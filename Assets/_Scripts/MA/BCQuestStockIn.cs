using System.Collections.Generic;
using NUnit.Framework;
using UnityEngine;

public class BCQuestStockIn : BCStockIn
{
    public override bool AddPickup(ReactPickup _pickup)
    {
        if(base.AddPickup(_pickup))
        {
            var order = _pickup.GetOrder();
            if(order != null)
            {
                order.ItemDispatched();
                order.ItemDelivered();
            }
            return true;
        }
        return false;
    }
    
    public NGCarriableResource GetResource()
    {
        foreach (var item in m_stock.Items)
        {
            if(item.Stock > 0)
            {
                return item.Resource;
            }
        }
        return null;
    }
}
