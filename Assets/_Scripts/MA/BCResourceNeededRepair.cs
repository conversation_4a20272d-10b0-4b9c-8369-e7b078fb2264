using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BCResourceNeededRepair  : BCResourceNeededBase
{
    public float m_repairPerDelivery = .1f;
    
    private PathBreak m_pathBreakToRepair;
    private PathManager.Path m_pathToConstruct;

    public override bool OnWorkerDelivers(MAWorker _worker)
    {
        if (_worker.Carrying == null) return false;

        var carrying = _worker.Carrying.Contents;
        if (GetResourceRequirement(carrying) <= 0) return false;

        GlobalData.Me.StartCoroutine(Co_DoRepair(_worker));
        return true;
    }

    private Vector3 GetRepairPosition()
    {
        if (m_pathToConstruct != null) return transform.position;
        return m_pathBreakToRepair.transform.position;
    }
    private IEnumerator Co_DoRepair(MAWorker _worker)
    {
        Vector3 pathPosition = Vector3.zero;
        if (m_pathBreakToRepair == null)
        {
            if (m_pathToConstruct == null)
            {
                Debug.LogError("BCResourceNeededRepair::Co_DoRepair m_pathBreakToRepair and m_pathToConstruct are both null!");

                yield return null;
            }
            else
            {
                var resultTuple = m_pathToConstruct.GetClosestPointToPoint(_worker.transform.position, true, 2f*2f, true);
                pathPosition = new Vector3(resultTuple.Item3.x, resultTuple.Item3.y, resultTuple.Item3.z);
            }
        }
        else
        {
            pathPosition = m_pathBreakToRepair.transform.position;
        }

        _worker.CleanupIfCarrying();
        const string c_pauseReason = "Repairing";
        _worker.m_nav.PushPause(c_pauseReason);
        
        var facing = (pathPosition - _worker.transform.position).GetXZNorm();
        while (Vector3.Dot(facing, _worker.transform.forward) < .99f)
        {
            var newFwd = Vector3.Slerp(_worker.transform.forward, facing, Time.deltaTime * 5f);
            _worker.transform.LookAt(_worker.transform.position + newFwd, Vector3.up);
            yield return null;
        }
        
        bool finished = false;
        _worker.PlayLoopAnimation("WorkerChop", (b) => finished = true);
        yield return new WaitForSeconds(5f);
        _worker.StopCurrentAnimation();
        
        DoRepair();
        while (finished == false) yield return null;
        _worker.m_nav.PopPause(c_pauseReason);
        _worker.SetMoveToComponent(_worker.Job, PeepActions.ReturnToWork);
    }

    void DoRepair()
    {
        if (m_pathToConstruct != null) m_pathToConstruct.DoConstruct(m_repairPerDelivery);
        else m_pathBreakToRepair.DoRepair(m_repairPerDelivery);
    }


    public void SetPathBreak(PathBreak _pathBreak)
    {
        m_pathBreakToRepair = _pathBreak;
    }
    public void SetPath(PathManager.Path _path)
    {
        m_pathToConstruct = _path;
    }
    
    static DebugConsole.Command s_repairAllCmd = new ("repairall", _s => RepairAll(_s));
    static void RepairAll(string _s)
    {
        for(int i = NGManager.Me.m_maBuildings.Count - 1; i >= 0; i--)
        {
            var b = NGManager.Me.m_maBuildings[i];
            if (b.GetComponentInChildren<BCResourceNeededRepair>() != null)
                b.GetComponentInChildren<BCResourceNeededRepair>().DoRepair();
        }
    }

    override public float GetResourceRequirement(NGCarriableResource _resource, bool _isTest = false)
    {
      // RW-13-MAY-25: It's possible to have PathBreaks which have 100% repair, as they're created by TrySmashWall which is
      // used in decision-making and does not necessarily indicate that it will be attacked. Repairing these leads to odd behaviour.
      // Instead, don't allow wood to be dropped on 100% repair Path Breaks.
      if (m_pathBreakToRepair != null && !m_pathBreakToRepair.IsDamaged)
      {
          return 0f;
      }
      return base.GetResourceRequirement(_resource, _isTest);
    }

    override public bool AddPickup(ReactPickup _pickup)
    {
        if (GetResourceRequirement(_pickup.m_contents) <= 0f)
            return false;

        DoRepair();

        return true;
    }
}
