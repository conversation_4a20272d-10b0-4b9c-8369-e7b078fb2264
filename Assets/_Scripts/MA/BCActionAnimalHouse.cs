#if UNITY_EDITOR
using UnityEditor;
#endif

public class BCA<PERSON>AnimalHouse : MACreatureSpawnPointActionBase
{
    override public bool Arrive(MACharacterBase _character)
    {
        return _character as MAAnimal != null;
    }
}

#if UNITY_EDITOR
[CanEditMultipleObjects]
[CustomEditor(typeof(BCActionAnimalHouse))]
public class BCActionAnimalHouseEditor : MACreatureSpawnPointActionBaseEditor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        // BCActionAnimalHouse spawnPoint = (BCActionAnimalHouse)target;
        //
        // if(Application.isPlaying)
        // {
        //     foreach(string creatureType in spawnPoint.m_spawnCreatures)
        //     {
        //         if(GUILayout.Button($"Spawn {creatureType}"))
        //         {
        //             spawnPoint.EditorSpawnCreature(creatureType);
        //         }
        //     }
        // }
    }
}
#endif