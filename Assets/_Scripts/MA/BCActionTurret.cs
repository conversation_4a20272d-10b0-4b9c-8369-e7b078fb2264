using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;
using Time = UnityEngine.Time;

public class BCActionTurret : BCActionBase, IImpactDataSupplier, IDamager
{
//IDamager
    public Transform Transform => transform;
    public string Name => name; 
    public void ClearTarget() { }
    public TargetObject TargetObject => m_lastObjectFiredAt?.GetComponent<TargetObject>();
 //  
 
    [KnackField] public string m_targets;
    [KnackField] public string m_defaultAmmo = "MA_Turret_Ammo_Boulder";
    [KnackField] public float m_timeBetweenShots = 2;
    [KnackField] public float m_damageMultiplier = 10f;
    
    
    [KnackField] public float m_payloadSpeedXZ = 50;
    [KnackField] public float m_explosionRadius = 10;
    [KnackField] public float m_explosionForce = 200;
    [KnackField] public float m_turnSpeed = 100; // Deg / second
    [KnackField] public float m_minPitch = -30f;
    [KnackField] public float m_maxPitch = 30f;
    [KnackField] public float m_fireAnimationSpeedModifier = 1f;
    [KnackField] public float m_recoilAmount = 0.8f;
    [KnackField] public float m_ammoSizeMultiplier = 1f;
    [KnackField] public float m_defaultAmmoSizeMultiplier = 1f;
    [KnackField] public float m_reloadGUIHeight = 4f;
    
    [KnackField] public string m_barrelForwardType = "up";
    [KnackField] public float m_barrelEndDist = 3f;
    [KnackField] public float m_barrelEndUp = 0f;
    [KnackField] public float m_pitchOffset = -90f;// 40f;
    [KnackField] public float m_aimAdjustment = -90f;// 40f;
    [KnackField] public bool m_rotateOnX = false;
    [KnackField] public bool m_invertPitch = false;
    
    [KnackField] public string m_controlledVisualName = "ControlledVisual";
    [KnackField] public string m_ammoVisualName = "Dummy001";
    [KnackField] public string m_barrelVisualName = "Bone_Base";
    
    [Save] private Vector3 m_lockedDirection = Vector3.zero;
    
    public enum TurretState
    {
        LoadAmmo,
        PrimeTurret,
        ReadyToFire,
        Fire,
        Cooldown,
    }
    
    private Transform m_ammoVisual;
    private Transform m_controlledVisual;
    private Transform m_barrelVisual;
    private float m_barrelElevation = 0;
    private float m_cooldown = 0;
    private float m_damageEnhancementMultiplier = 1f;
    private float m_explosionRadiusEnhancementMultiplier = 1f;
    private float m_explosionForceEnhancementMultiplier = 1f;
    private float m_cooldownEnhancementMultiplier = 1f;
    private Animator m_animator;
    private float m_minRange = 3.75f;  
    private float m_maxRange = 57;
    
    private float m_orientationLock = 0;
    
    public bool m_updateRange = false;
    private static DebugConsole.Command s_debugTurretsCmd = new ("debugturrets", _s => Utility.SetOrToggle(ref s_debugTurrets, _s));
    private static bool s_debugTurrets = false; // fires cubes so doesn't require stock, fires at tourists rather than creatures
    private GameObject m_lastObjectFiredAt = null;
    private LineRenderer[] m_parabolaLine = new LineRenderer[2];
    
    public const KeyCode c_lockTurretKey = KeyCode.L;
    
    private (Vector3 barrelEndPos, Vector3 barrelFwd, Vector3 ammoPos) BarrelInfo()
    {
        Vector3 fwd = Vector3.forward;
        switch(m_barrelForwardType)
        {
            case "forward": fwd = m_barrelVisual.forward; break;
            case "back": fwd = -m_barrelVisual.forward; break;
            case "up": fwd = m_barrelVisual.up; break;
            case "down": fwd = -m_barrelVisual.up; break;
            case "left": fwd = -m_barrelVisual.right; break;
            case "right": fwd = m_barrelVisual.right; break;
        }
        return (m_barrelVisual.transform.position + fwd * m_barrelEndDist + Vector3.up * m_barrelEndUp, fwd, m_ammoVisual.position);
    }

    public float TimeBetweenShots => m_timeBetweenShots * m_cooldownEnhancementMultiplier;
    public bool IsLocked => m_lockedDirection.sqrMagnitude > 0;
    public bool IsFunctional => m_isFunctional;
    public override bool ShowWarning => m_isFunctional == false || base.ShowWarning;
    
    private bool m_couldFireLastFrame = false;
    private Material m_lineMaterial = null;
    private bool m_arePossessedShortcutsShowing = false;
    private bool m_isPossessed = false;
    private int m_turretMovementAudio = 0;
    
    private List<GameState_Design> m_ammoTypes = new();
    private int m_ammoRotationIndex;
    private bool m_isFunctional = false;
    
    private MACooldownGUI m_cooldownVisual = null;
    private MAAreaSelectionCircle m_rangeCirlce = null;
    private List<(MACharacterBase obj, float d2, float priority)> m_lastTargets = new();
    private MACharacterBase m_target;
    
    private bool m_loadingAmmo = false;
    private GameObject m_nextAmmo = null;
    private float m_nextAmmoDamage = 1f;
    
    private TurretState m_state = TurretState.LoadAmmo;
    
    private void UpdateRanges()
    {
        // Min Range
        SetBarrelPitch(m_minPitch);
        var barrelInfo = BarrelInfo();
        var range1 = CalculateRange(barrelInfo.barrelEndPos, barrelInfo.barrelFwd * m_payloadSpeedXZ);
        
        // Max Range
        SetBarrelPitch(m_maxPitch);
        barrelInfo = BarrelInfo();
        var range2 = CalculateRange(barrelInfo.barrelEndPos, barrelInfo.barrelFwd * m_payloadSpeedXZ);
        
        m_minRange = Mathf.Min(range1, range2);
        m_maxRange = Mathf.Max(range1, range2);
        
        SetBarrelPitch(0);
    }
    
    private string m_defaultAmmoDesign = "";
    private void UpdateAmmo()
    {
        m_ammoTypes.Clear();
        m_isFunctional = false;
        
        if(m_controlledVisual == null || m_barrelVisual == null || m_ammoVisual == null)
            return;
            
        UpdateRanges();
        
        m_defaultAmmoDesign = $"1|{m_defaultAmmo}@|0|";
        
        foreach(var ammoBlock in Building.BuildingComponents<BCAmmoStock>())
        {
            var ammoType = ammoBlock.GetAmmoBlock();
            if(ammoType.IsNullOrWhiteSpace() == false)
                m_ammoTypes.Add(new GameState_Design($"1|{ammoType}@|0|"));
            m_isFunctional = true;
        }
        if(m_ammoTypes.Count == 0)
        {
            m_ammoTypes.Add(new GameState_Design(m_defaultAmmoDesign));
        }
    }

    public override void OnBuildingDesignChanged()
    {
        UpdateAmmo();
        
        if(DTDragBlock.m_lastGrabbedBlock == Block && DesignTableManager.Me.m_isInDesignGlobally)
        {
            ShowRange();
        }
    }
    
    public override void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
        m_controlledVisual = m_block.m_toVisuals.FindChildRecursiveByName(m_controlledVisualName);
        m_barrelVisual = m_block.m_toVisuals.FindChildRecursiveByName(m_barrelVisualName);
        m_ammoVisual = m_block.m_toVisuals.FindChildRecursiveByName(m_ammoVisualName);
        
        m_animator = m_controlledVisual?.GetComponentInChildren<Animator>();
        
        ApplyEnhancements();
        
        UpdateAmmo();
        
        base.Activate(_indexOfComponentType, _quantityInBuilding);
    }

		protected override void Deactivate(MABuilding _previousOwner, int _quantityRemainingInBuilding, BlockDragAction _action)
		{
			// RW-06-AUG-25: Hide the range highlight when the turret block is destroyed.
			BuildingComponentVisualHelpers.ClearHighlights();
			base.Deactivate(_previousOwner, _quantityRemainingInBuilding, _action);
		}

    virtual protected void Awake()
    {
        base.Awake();
        
        if(m_block == null) return;
        
        if (m_lineMaterial == null)
            m_lineMaterial = new Material(Shader.Find("Sprites/Default"));
    }
    
    private void TryLoadCooldownVisual()
    {
        if(m_cooldownVisual != null)
            return;
            
        var cooldownPrefab = Resources.Load<MACooldownGUI>("_GUI/TurretCooldown"); 
        if(cooldownPrefab != null)
            m_cooldownVisual = Instantiate(cooldownPrefab, m_block.m_toVisuals);
    }
    
    override public void UpdateInternal(BuildingComponentsState _state)
    {
        m_rangeCirlce?.Update();
        UpdateTurret();
        base.UpdateInternal(_state);
    }

    public bool Possess()
    {
        if(m_isFunctional == false)
            return false;
            
        TryLoadCooldownVisual();
        
        m_isPossessed = true;
        KeyboardShortcutManager.Me.PushShortcuts(KeyboardShortcutManager.EShortcutType.PossessTurret);
        return true;
    }

    public bool Unpossess()
    {
        if(m_isPossessed)
        {
            if(m_cooldownVisual != null)
            {
                Destroy(m_cooldownVisual.gameObject);
                m_cooldownVisual = null;
            }
            m_isPossessed = false;
            DestroyParabola();
            KeyboardShortcutManager.Me.PopShortcuts();
            return true;
        }
        return false;
    }

    public override void LateUpdateInternal()
    {
        if(m_updateRange)
        {
            UpdateRanges();
            m_updateRange = false;
        }
        
        if (m_isPossessed)
        {
            var cam = Camera.main;
            var camXform = cam.transform;
            var camOffset = GameManager.Me.CurrentPossessionSettings.m_possessedTurretPos;
            var barrelInfo = BarrelInfo();
            camXform.position = m_controlledVisual.position - m_controlledVisual.forward * camOffset.z + Vector3.up * camOffset.y + m_controlledVisual.right * camOffset.x;
            camXform.forward = Vector3.Lerp(m_controlledVisual.forward, barrelInfo.barrelFwd, 0.5f).normalized + Vector3.up * camOffset.w;
            
        }
        base.LateUpdateInternal();
    }
    
    public void ApplyEnhancements()
    {
        m_damageEnhancementMultiplier = 1f;
        m_explosionRadiusEnhancementMultiplier = 1f;
        m_explosionForceEnhancementMultiplier = 1f;
        m_cooldownEnhancementMultiplier = 1f;
        
        foreach(var enhancement in Building.BuildingComponents<BCTurretEnhancement>())
        {
            m_damageEnhancementMultiplier *= enhancement.m_damageMultiplier;
            m_explosionRadiusEnhancementMultiplier *= enhancement.m_explosionRadiusMultiplier;
            m_explosionForceEnhancementMultiplier *= enhancement.m_explosionForceMultiplier;
            m_cooldownEnhancementMultiplier *= enhancement.m_cooldownMultiplier;
        }
    }
    
    private void UpdateAudio(bool _rotating)
    {
        if (_rotating)
        {
            if (m_turretMovementAudio <= 0)
            {
                m_turretMovementAudio = AudioClipManager.Me.PlaySound("PlaySound_TurretRotate", gameObject);
            }
        }
        else
        {
            if (m_turretMovementAudio > 0)
            {
                AudioClipManager.Me.PlaySound("StopSound_TurretRotate", gameObject);
                m_turretMovementAudio = 0;
            }
        }
    }
    
    public float PitchTowardsTarget(Transform _target, bool _immediate = false)
    {
        var toTarget = _target.position - m_barrelVisual.position;
        var localDir = m_barrelVisual.parent.InverseTransformDirection(toTarget);
        float targetPitch = Mathf.Atan2(localDir.y, new Vector2(localDir.x, localDir.z).magnitude) * Mathf.Rad2Deg + m_aimAdjustment;
        targetPitch = Mathf.Clamp(targetPitch, m_minPitch, m_maxPitch);
        
        var euler = m_barrelVisual.localEulerAngles;
        
        float currentPitch = (m_rotateOnX ? euler.x : euler.z) - m_pitchOffset;
        if(currentPitch > 180f) currentPitch -= 360f;
        
        if(_immediate)
        {
            if (targetPitch < 0f) targetPitch += 360f;
            SetBarrelPitch(targetPitch);
            return 0f;
        }
        
        float newPitch = Mathf.MoveTowards(currentPitch, targetPitch, m_turnSpeed * Time.deltaTime);
        if (newPitch < 0f) newPitch += 360f;
        SetBarrelPitch(targetPitch);
        return Mathf.Abs(Mathf.DeltaAngle(targetPitch, newPitch));
    }
    
    private void SetBarrelPitch(float _pitch)
    {
        var euler = m_barrelVisual.localEulerAngles;
        m_barrelVisual.localEulerAngles = m_rotateOnX ? new Vector3(_pitch + m_pitchOffset, euler.y, euler.z) : new Vector3(euler.x, euler.y, _pitch + m_pitchOffset);
    }
    
    private void PitchBarrel(float _pitchDelta)
    {
        var euler = m_barrelVisual.localEulerAngles;
        
        float currentPitch = (m_rotateOnX ? euler.x : euler.z) - m_pitchOffset;
        
        if(currentPitch > 180F) currentPitch -= 360f;
        
        var newPitch = Mathf.Clamp(currentPitch + _pitchDelta, m_minPitch, m_maxPitch);
        
        SetBarrelPitch(newPitch);
    }
    
    private bool UpdatePosession()
    {
        if(m_cooldownVisual != null)
            m_cooldownVisual.UpdateProgress(m_controlledVisual.position + Vector3.up * m_reloadGUIHeight, m_cooldown, TimeBetweenShots);
            
        var canFire = m_state == TurretState.ReadyToFire;
        var refreshCanFire = false;
        if (m_couldFireLastFrame != canFire)
        {
            refreshCanFire = true;
            m_couldFireLastFrame = canFire;
        }
        
        if (m_isPossessed)
        {
            if (refreshCanFire) KeyboardShortcutManager.Me.Refresh();
            
            var xDelta = Utility.MouseXAxis * GameManager.Me.CurrentPossessionSettings.m_possessedCameraXSensitivity;
            var yDelta = Input.GetAxis("Mouse Y") * GameManager.Me.CurrentPossessionSettings.m_possessedCameraYSensitivity * (m_invertPitch?-1:1);
            var camTurnScale = GameManager.Me.CurrentPossessionSettings.m_possessedTurretSpeed;
            m_controlledVisual.Rotate(Vector3.up, xDelta * camTurnScale.x);
            PitchBarrel(yDelta*camTurnScale.y);

            var (barrelPos, barrelFwd, ammoPos) = BarrelInfo();
            CreateParabola(barrelPos, barrelFwd * m_payloadSpeedXZ, m_cooldown <= 0 ? Color.green : Color.red, 1, 0);

            if (m_lockedDirection.sqrMagnitude > 0)
            {
                var backupVis = m_controlledVisual.localEulerAngles;
                var backupBarrel = m_barrelVisual.localEulerAngles;
                m_controlledVisual.localEulerAngles = new Vector3(0, m_lockedDirection.y, 0);
                m_barrelVisual.localEulerAngles = new Vector3(m_lockedDirection.x, 0, 0);
                var (lockedBarrelPos, lockedBarrelFwd, lockedAmmoPos) = BarrelInfo();
                m_controlledVisual.localEulerAngles = backupVis;
                m_barrelVisual.localEulerAngles = backupBarrel;
                CreateParabola(lockedBarrelPos, lockedBarrelFwd * m_payloadSpeedXZ, Color.blue, 4f, 1);
            }
            else
            {
                DestroyParabola(1);
            }

            /*if (Utility.GetKeyDown(c_lockTurretKey))
            {
                m_lockedDirection = m_lockedDirection.sqrMagnitude > 0 ? Vector3.zero : new Vector3(m_barrelVisual.transform.localEulerAngles.x, m_controlledVisual.transform.localEulerAngles.y, 0);
                KeyboardShortcutManager.Me.Refresh();
            }*/

            if (canFire && Input.GetMouseButtonDown(0))
            {
                m_state = TurretState.Fire;
            }
            return true;
        }
        return false;
    }
    
    private void UpdateTurret()
    {
        if (m_cooldown > 0)
            m_cooldown -= Time.deltaTime;

        if(m_isFunctional == false)
            return;
        
        m_controlledVisual.position = Vector3.Lerp(m_controlledVisual.position, m_controlledVisual.parent.position, .1f); // don't do this in local space to retain relative position
        
        switch(m_state)
        {
            case TurretState.LoadAmmo:
                if(m_nextAmmo != null)
                {
                    m_state = TurretState.PrimeTurret;
                }
                else if(m_loadingAmmo == false)
                {
                    LoadNextAmmo();
                }
            break;
            
            case TurretState.PrimeTurret:
                if(m_animator != null)
                {
                    var stateInfo = m_animator.GetCurrentAnimatorStateInfo(0);
                    if(stateInfo.IsName("04_Reloading") == false)
                    {
                        m_animator.speed = 1f;
                        m_animator.Play("04_Reloading");
                    }
                    else if(stateInfo.normalizedTime >= 1f)
                    {
                        m_state = TurretState.ReadyToFire;
                    }
                }
                else
                {
                    m_state = TurretState.ReadyToFire;
                }
                break;
                
                case TurretState.Fire:
                    bool fire = true;
                    if(m_animator != null)
                    {
                        fire = false;
                        var stateInfo = m_animator.GetCurrentAnimatorStateInfo(0);
                        if(stateInfo.IsName("02_Launch") == false)
                        {
                            m_animator.speed = m_payloadSpeedXZ * m_fireAnimationSpeedModifier;
                            m_animator.Play("02_Launch");
                        }
                        else if(stateInfo.normalizedTime >= 1f)
                        {
                            fire = true;
                        }
                    }
                    
                    if(fire)
                    {
                        FireObject(m_nextAmmo, m_target == null ? null : m_target.gameObject, m_damageMultiplier * m_damageEnhancementMultiplier * m_nextAmmoDamage);
                        m_state = TurretState.Cooldown;
                        m_nextAmmo = null;
                        m_nextAmmoDamage = 1f;
                        m_target = null;
                    }
                break;
                
                case TurretState.Cooldown:
                    if(m_cooldown <= 0)
                        m_state = TurretState.LoadAmmo;
                break;
        }
        
        
        if(UpdatePosession())
            return;
        
        DestroyParabola();

        UpdateAutoTurret();
    }
    
    private void UpdateAutoTurret()
    {
        if(m_cooldown > 0)
            return;
        
        if(m_target == null)
        {
            UpdateAudio(false);
            m_target = GetBestTarget();
            return;
        }
        
        // Check if target has gone out of range....
        if(m_state != TurretState.Fire && IsTargetValid(m_target, (m_target.transform.position - m_controlledVisual.position).xzSqrMagnitude(), m_minRange, m_maxRange) == false)
        {
            m_target = null;
            return;
        }
        
        UpdateAudio(true);
        
        float targetRotation = 0;
        float barrelElevationTarget = 0;
        
        if (m_lockedDirection.sqrMagnitude > 0)
        {
            targetRotation = m_lockedDirection.y;
            barrelElevationTarget = m_lockedDirection.x;
        }
        else
        {
            targetRotation = GetTargetRotation(m_target.gameObject);
        }
        
        //var barrelInfo = BarrelInfo();
        //CreateLine(barrelInfo.barrelEndPos, m_target.transform.position, m_lineMaterial);
        
        float speed = m_turnSpeed * Time.deltaTime;
        float rotation = Mathf.Clamp(targetRotation, -speed, speed);
        m_controlledVisual.Rotate(0,rotation,0);
        
        float leftToPitch = PitchTowardsTarget(m_target.transform);
        
        if(Mathf.Abs(targetRotation) < 5f && leftToPitch < 5f && m_state == TurretState.ReadyToFire)
        {
            m_state = TurretState.Fire;
            var target = TargetObject.Create(m_target);
            target.AddTargeter(this);
        }
    }
    
    public float GetTargetRotation(GameObject _target)
    {
        var d = _target.transform.position - m_controlledVisual.transform.position;
        d.y = 0;
        if(d == Vector3.zero)
            return m_controlledVisual.eulerAngles.y;
        var target = Mathf.Atan2(d.x, d.z) * Mathf.Rad2Deg;
        return Mathf.DeltaAngle(m_controlledVisual.eulerAngles.y, target);
    }
    
    private MACharacterBase GetBestTarget()
    {
        var barrelInfo = BarrelInfo();
        //var direction = m_controlledVisual.forward;
        
        var targets = GetNearestTargets(m_controlledVisual.transform.position, barrelInfo.barrelFwd.GetXZNorm(), m_minRange, m_maxRange);
        if(targets.Count == 0)
            return null;
            
        targets.Sort((x,y)=> x.Item2.CompareTo(y.Item2));
        for (int i = 0; i < targets.Count; ++i) // do raycast on sorted list to minimise number
        {
            // check visibility
            var creature = targets[i].Item1;
            var rayFwd = creature.m_transform.position - barrelInfo.barrelEndPos;
            var rayLength = rayFwd.magnitude;
            rayFwd /= rayLength;
            if (Physics.Raycast(barrelInfo.barrelEndPos + rayFwd, rayFwd, out var hit, rayLength - 1f))
                if (hit.collider.transform.IsChildOf(creature.transform) == false && hit.collider.transform.IsChildOf(Block.m_toVisuals) == false)
                    continue;
            return creature;
        }
        return null;
    }

    public static float TimeToImpact(Vector3 _initialPos, Vector3 _velocity, GameObject _ignore = null)
    {
        var pos = _initialPos;
        var hitPos = Vector3.zero;
        float t = 0, dt = .1f;
        var gravity = Physics.gravity;
        for (int i = 0; i < 1000; ++i) // max 1000 steps
        {
            var prev = pos;
            t += dt;
            pos += _velocity * dt;
            _velocity += gravity * dt;
            
            if (Punt.Cast(prev, pos, out hitPos, out var _hit, _ignore))
                return t;
        }
        return 1000 * dt;
    }

    private GameState_Design GetNextAmmoDesign()
    {
        if(m_ammoTypes.Count > 0)
        {
            return m_ammoTypes[m_ammoRotationIndex % m_ammoTypes.Count];
        }
        return null;
    }
    
    private void LoadNextAmmo()
    {
        var nextItemDesign = GetNextAmmoDesign();

        if(nextItemDesign == null)
            return;
         
        bool isDefaultAmmo = nextItemDesign.m_design.Equals(m_defaultAmmoDesign);
        m_ammoRotationIndex++;
        m_nextAmmoDamage = 1f;
        
        //GameObject.CreatePrimitive(PrimitiveType.Cube);
        m_loadingAmmo = true;
        GameState_Design.BuildProduct(nextItemDesign, m_ammoVisual, true, (_o,componentsChanged) => {
            Block[] blocks = null;
            if(_o != null)
            {
                _o.transform.localScale = isDefaultAmmo ? Vector3.one * m_defaultAmmoSizeMultiplier : Vector3.one * (GlobalData.Me.m_heldItemScale * NGManager.Me.m_productPickupScale * m_ammoSizeMultiplier);
                _o.transform.localEulerAngles = new Vector3(0, 0, 0);
                
                blocks = _o.GetComponentsInChildren<Block>();
                m_nextAmmo = _o;
                m_nextAmmoDamage = nextItemDesign.m_attackScore;
            }
            if(blocks != null)
                foreach(Block block in blocks)
                {
                    block.gameObject.transform.localEulerAngles = new Vector3(0, 0, 0);
                    block.enabled = false;
                    Destroy(block);
                }
                    
            m_loadingAmmo = false;
        });
    }
    
    private Rigidbody GetOrCreateRigidbody(GameObject _obj, Vector3 _startPos)
    {
        var rb = _obj.GetComponent<Rigidbody>();
        if (rb == null)
            rb = _obj.AddComponent<Rigidbody>();
        rb.isKinematic = false;
        rb.angularDamping = 1f;
        rb.linearDamping = 0;
        rb.collisionDetectionMode = CollisionDetectionMode.ContinuousDynamic;
        rb.position = _startPos;
        return rb;
    }
    
    void FireObject(GameObject _objectToFire, GameObject _target, float _damage)
    {
        float timeToDestination = 0;
        
        m_controlledVisual.position = m_controlledVisual.parent.position - m_controlledVisual.forward * m_recoilAmount;
        
        if (_target == null)
        {
            var bInfo = BarrelInfo();
            var vel = bInfo.barrelFwd * m_payloadSpeedXZ;
            timeToDestination = TimeToImpact(bInfo.barrelEndPos, vel, m_controlledVisual.gameObject);
            _objectToFire.transform.SetParent(null, true);
            var rb = GetOrCreateRigidbody(_objectToFire, bInfo.barrelEndPos);
            rb.linearVelocity = vel;
        }
        else
        {
            _objectToFire.transform.SetParent(null, true);
            var bInfo = BarrelInfo();
            var targetPos = _target.transform.position;
            //var visualsPos = m_endOfWeapon.position;//m_barrelVisual
            var visualToTarget = targetPos - bInfo.barrelEndPos;
            var targetBody = _target.GetComponentInChildren<Rigidbody>();
            if (targetBody != null)
            {
                var visualToTargetDistance = visualToTarget.xzMagnitude();
                var timeToTarget = visualToTargetDistance / m_payloadSpeedXZ;
                var targetVelocity = targetBody.linearVelocity;
                targetPos += targetVelocity * timeToTarget;
            }
            //m_controlledVisual.LookAt(targetPos.GetXZ() + Vector3.up * m_controlledVisual.transform.position.y, Vector3.up);
            
            var rb = GetOrCreateRigidbody(_objectToFire, bInfo.barrelEndPos);//m_barrelVisual
            
            var payloadSpeedXZ = m_payloadSpeedXZ;
            rb.SetVelocityToMoveWithSpeed(targetPos, payloadSpeedXZ, out timeToDestination); //, (r) => Destroy(r.gameObject));
            var velocityDirection = rb.linearVelocity.normalized;
            if (velocityDirection.y < -.1f)
            {
                // too close, slow the projectile to increase elevation
                payloadSpeedXZ *= .5f;
                rb.SetVelocityToMoveWithSpeed(targetPos, payloadSpeedXZ, out timeToDestination); //, (r) => Destroy(r.gameObject));
                velocityDirection = rb.linearVelocity.normalized;
            }
            
            _objectToFire.transform.position = rb.position;
            
            // orient the barrel to the velocity
            PitchTowardsTarget(_target.transform, true);
        }
        var bounds = ManagedBlock.GetTotalVisualBounds(_objectToFire);
        
        Quaternion lookRot = Quaternion.LookRotation(BarrelInfo().barrelFwd);
        Quaternion currentRot = _objectToFire.transform.rotation;
        Quaternion offset = Quaternion.Inverse(lookRot) * currentRot;
        _objectToFire.AddComponent<AlignWithVelocity>().m_offset = offset;
        
        /*AlignWithVelocity.EAlignType alignType = AlignWithVelocity.EAlignType.X;
        if (bounds.size.y > bounds.size.x && bounds.size.y > bounds.size.z) alignType = AlignWithVelocity.EAlignType.Y;
        else if (bounds.size.z > bounds.size.x && bounds.size.z > bounds.size.y) alignType = AlignWithVelocity.EAlignType.Z;
        _objectToFire.AddComponent<AlignWithVelocity>().m_alignType = alignType;*/
        
        
        // switch off collisions at first to avoid self-collision
        ControlCollisions(_objectToFire, timeToDestination, _damage);
        m_orientationLock = 1;
        m_cooldown = TimeBetweenShots;

        if (NGManager.Me.m_turretMuzzleFlashPrefab != null)
        {
            var info = BarrelInfo();
            var muzzleFlash = Instantiate(NGManager.Me.m_turretMuzzleFlashPrefab, info.barrelEndPos, Quaternion.LookRotation(info.barrelFwd, Vector3.up));
            muzzleFlash.transform.parent = m_controlledVisual;
            Destroy(muzzleFlash, 1);
        }

        if (m_block != null) m_block.PlayExecuteAudio();

        if (m_lastObjectFiredAt != _target)
        {
            if(m_lastObjectFiredAt != null)
            {
                TargetObject previousTarget = m_lastObjectFiredAt.GetComponent<TargetObject>();
                if(previousTarget != null) previousTarget.RemoveTargeter(this);
            }
            m_lastObjectFiredAt = _target;
        }
    }
    
    private bool IsTargetValid(MACharacterBase _target, float _d2ToTarget, float _minDistance, float _maxDistance)
    {
        if (_target == null || _target.IsAlive == false) return false;
        var state = _target.CharacterUpdateState;
        if(state != null && (state.State == CharacterStates.Spawn || state.State == CharacterStates.Despawn || state.State == CharacterStates.Dying)) return false;
        if (_d2ToTarget > _maxDistance * _maxDistance) return false;
        if (_d2ToTarget < _minDistance * _minDistance) return false;
        
        return true;
    }
    
    public List<(MACharacterBase obj, float d2, float priority)> GetNearestTargets(Vector3 _origin, Vector3 _direction, float _minDistance, float _maxDistance)
    {
        m_lastTargets.Clear();
        
        void CheckCharacter(MACharacterBase _chr)
        {
            var toTarget = (_chr.m_transform.position - _origin).GetXZ();
            var d2 = toTarget.sqrMagnitude;
            
            if(IsTargetValid(_chr, d2, _minDistance, _maxDistance) == false)
                return;
            
            var dot = Vector3.Dot(toTarget, _direction);
            float aimValue = (1-dot);
            float aimWeight = Mathf.Clamp01(1f / m_turnSpeed);
            float priority = d2 + (aimValue * aimValue) * aimWeight;
            
            m_lastTargets.Add((_chr, d2, priority));
        }
        
        if (s_debugTurrets)
        {
            foreach (var creature in MATouristManager.Me.m_tourists)
                CheckCharacter(creature);
        }
        else
        {
            foreach (var creature in NGManager.Me.m_MACreatureList)
            {
                if (creature.IsEnemy)
                    CheckCharacter(creature);
            }
        }
        return m_lastTargets;        
    }

    private void InhibitCollisions(GameObject _obj, bool _inhibit) => _obj.GetComponent<Rigidbody>().detectCollisions = !_inhibit;
    /*HashSet<Collider> m_inhibitedColliders = new HashSet<Collider>(); 
    private void InhibitCollisions(GameObject _obj, bool _inhibit)
    {
        var colliders = _obj.GetComponentsInChildren<Collider>();
        foreach (var collider in colliders)
        {
            if (_inhibit)
            {
                if (collider.enabled)
                {
                    m_inhibitedColliders.Add(collider);
                    collider.enabled = false;
                }
            }
            else if (m_inhibitedColliders.Contains(collider))
            {
                m_inhibitedColliders.Remove(collider);
                collider.enabled = true;
            }
        }
    }*/

    private void ControlCollisions(GameObject _who, float _toDestination, float _damage)
    {
        InhibitCollisions(_who, true);
        StartCoroutine(Co_ControlCollisions(_who, _toDestination, _damage));
    }

    public class CollisionDetector : MonoBehaviour
    {
        public Action<Vector3> m_onCollision;
        
        void OnCollisionEnter(Collision _collision)
        {
            Vector3 collisionPoint = Vector3.zero;
            if(_collision != null && _collision.contactCount > 0)
            {
                collisionPoint = _collision.contacts[0].point;
            }
            else
            {
                collisionPoint = transform.position;
            }
            
            m_onCollision?.Invoke(collisionPoint);
        }
    }
    
    private IEnumerator Co_ControlCollisions(GameObject _who, float _toDestination, float _damage)
    {
        var rb = _who.GetComponent<Rigidbody>();
        var initialXZSpeed = rb.linearVelocity.xzMagnitude();
        float initialCollisionInhibit = Mathf.Min(.2f, _toDestination * .8f);
        yield return new WaitForSeconds(initialCollisionInhibit);
        InhibitCollisions(_who, false);
        var cd = rb.gameObject.AddComponent<CollisionDetector>();
        cd.m_onCollision = (pos) => { CreateExplosion(pos, _damage); _who.SetActive(false); Destroy(_who, .01f); };
        
        float t = initialCollisionInhibit;
        while (t < 5f)
        {
            if(_who == null || _who.activeSelf == false)
                break;
            
            t += Time.deltaTime;
            yield return null;
        }
        /*while (t < _toDestination + .2f)
        {
            var lt = t;
            t += Time.deltaTime;
            if (t >= _toDestination && lt < _toDestination)
                CreateExplosion(_who, _damage);
            if (rb == null)
            {
                Debug.LogError($"Rigidbody destroyed for object {_who.name} {_who.GetInstanceID()}");
            }
            var xzSpeed = rb.linearVelocity.xzMagnitude();
            if (xzSpeed < initialXZSpeed * .5f)
            {
                if (t < _toDestination)
                    CreateExplosion(_who, _damage);
                break;
            }
            yield return null;
        }*/
        
        if(_who != null && _who.activeSelf)
        {
            CreateExplosion(_who.transform.position, _damage);
            Destroy(_who, .01f);
        }
    }

    public void PlayImpactAudioOnObject(NGMovingObject _obj)
    {
        // TODO - choose impact audio based on projectile design
    }

    public void ApplyDamage(IDamageReceiver.DamageSource _source, NGMovingObject _obj, bool _isContinuous, float _damage = 1.0f)
    {
        _obj.ApplyDamageEffect(_source, _damage, _obj.transform.position);
        _obj.SetVengeanceTarget(Building);
    }

    private static string s_debugTurretLabel = null;
    private static DebugConsole.Command s_debugTurretCmd = new ("debugturret", _s => s_debugTurretLabel = _s, "Debug turret by name", "<string>");
    private void CreateExplosion(Vector3 _position, float _damageMultiplier)
    {
        // TODO - c_explosionForce and Radius should be based on projectile design
        var source = IDamageReceiver.GetSourceFromBCAction(this);
        MAPowerEffectBase.CreateExplosionAtPoint(source, _position, m_explosionRadius * m_explosionRadiusEnhancementMultiplier, 
            m_explosionForce * m_explosionForceEnhancementMultiplier, this, Vector3.up, null, s_debugTurretLabel, _damageMultiplier);
        if (NGManager.Me.m_turretImpactPrefab != null)
        {
            var impact = Instantiate(NGManager.Me.m_turretImpactPrefab, _position, Quaternion.identity);
            Destroy(impact, 2);
        }
    }

    private  void CreateLine(Vector3 start, Vector3 end, Material material)
    {
        LineRenderer lineRenderer;
        lineRenderer = GetComponentInChildren<LineRenderer>();
        if (lineRenderer == null)
        {
            var line = new GameObject("ConeCastLine");
            line.transform.parent = transform; // Adjust parenting as needed
            lineRenderer = line.AddComponent<LineRenderer>();          
            lineRenderer.material = material;
            lineRenderer.useWorldSpace = true;
        }

        lineRenderer.SetPosition(0, start);
        lineRenderer.SetPosition(1, end);
        lineRenderer.startWidth = 0.05f; // Adjust line width as desired
    }

    private void DestroyParabola()
    {
        for (int i = 0; i < m_parabolaLine.Length; ++i)
            DestroyParabola(i);
    }

    private void DestroyParabola(int _index)
    {
        if (m_parabolaLine[_index] == null) return;
        Destroy(m_parabolaLine[_index].gameObject);
        m_parabolaLine[_index] = null;
    }

    private float CalculateRange(Vector3 _start, Vector3 _u)
    {
        var a = Physics.gravity;
        var deltaTime = Time.fixedDeltaTime * 4;
        var minHeight = GameManager.Me.HeightAtPoint(m_controlledVisual.transform.position);
        Vector3 p = _start;
        for (int i = 0; i < 200; i++)
        {
            float t = i * deltaTime;
            p = _start + _u * t + a * (0.5f * t * t);
            
            if (p.y < GlobalData.c_seaLevel || p.y < minHeight)
            {
                break;
            }
        }
        return (_start - p).xzMagnitude();
    }

    private void CreateParabola(Vector3 start, Vector3 u, Color _clr, float _width, int _lineIndex)
    {
        if (m_parabolaLine[_lineIndex] == null)
        {
            var line = Instantiate(GlobalData.Me.m_pickupLinePrefab, transform);
            m_parabolaLine[_lineIndex] = line.GetComponent<LineRenderer>();
            m_parabolaLine[_lineIndex].gameObject.SetLayerRecursively(0);
            var gradient = new Gradient();
            gradient.alphaKeys = new [] { new GradientAlphaKey(0, 0), new GradientAlphaKey(1, 0.075f), new GradientAlphaKey(1, 1) };
            m_parabolaLine[_lineIndex].colorGradient = gradient;
        }

        DragBase.UpdateBezier(m_parabolaLine[_lineIndex], _clr);

        var a = Physics.gravity;
        var deltaTime = Time.fixedDeltaTime * 4;
        var positions = new Vector3[200];
        int count = positions.Length;
        for (int i = 0; i < count; i++)
        {
            float t = i * deltaTime;
            var p = start + u * t + a * (0.5f * t * t);
            positions[i] = p;
            if (positions[i].y < GlobalData.c_seaLevel)
            {
                count = i;
                break;
            }
        }
        m_parabolaLine[_lineIndex].positionCount = count;
        m_parabolaLine[_lineIndex].SetPositions(positions);
        m_parabolaLine[_lineIndex].startWidth = m_parabolaLine[_lineIndex].endWidth = 0.25f * _width;
    }
    
    public void ShowRange()
    {
        Highlight();
        MAParser.MoveCamera(transform.position, 25f);
        BuildingComponentVisualHelpers.AddGroundArea(transform.position, m_minRange, m_maxRange);
        NGBuildingInfoGUI.DestroyCurrent();
    }
    
    public override bool CombineGUI => false;
    override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() =>
        (m_info?.id, () => new BCTurretPanel(m_info));
    
    public class BCTurretPanel : BCUIPanel
    {
        public override bool ShowWarning => m_turret?.ShowWarning ?? false;
        private BCActionTurret m_turret;
        private MABuilding m_building;
        private string m_blockID;

        public override string SpriteBlockID => m_blockID;
        public override string TableHeading => "Enhancements";
        public override string GetDescription() => m_turret?.m_info?.m_description;
        
        public override Action GetShowRangeAction()
        {
            if(m_turret == null) return null;
            
            return () => { m_turret?.ShowRange(); };
        }
        

        public BCTurretPanel(MAComponentInfo _info) : base(_info)
        {
        }

        public override void AddComponent(BCBase _component)
        {
            if (_component is BCActionTurret)
            {
                m_blockID = _component.Block.BlockID;
                m_turret = _component as BCActionTurret;
                m_building = _component.Building;
                base.AddComponent(_component);
            }
        }

        public override string GetPrimaryText()
        {
            string value = "";
            
            if(m_turret.m_isFunctional == false)
            {
                value += $"<color=orange>Ammo stock block required to function.</color>\n\n";
            }

            value += GetModifiedValue("Min Range", m_turret.m_minRange.ToString("F0") + "m", 1f, false);
            value += GetModifiedValue("Max Range", m_turret.m_maxRange.ToString("F0") + "m", 1f, false);
            value += GetModifiedValue("Reload Speed", m_turret.TimeBetweenShots + "sec",
                m_turret.m_cooldownEnhancementMultiplier);
            value += GetModifiedValue("Projectile Speed", m_turret.m_payloadSpeedXZ.ToString("F0") + "m/s");
            value += GetModifiedValue("Damage", "Ammo Dependant");

            return value;
        }

        override public IEnumerable<System.Action> CreateTableLines(Transform _holder)
        {
            if (m_building == null)
                yield break;

            Dictionary<string, List<BCTurretEnhancement>> enhancements = new();

            foreach (var enhancement in m_building.BuildingComponents<BCTurretEnhancement>())
            {
                if (enhancements.TryGetValue(enhancement.Block.BlockID, out var list) == false)
                {
                    list = new List<BCTurretEnhancement>();
                    enhancements[enhancement.Block.BlockInfo.m_displayName] = list;
                }

                list.Add(enhancement);
            }

            foreach (var e in enhancements)
            {
                yield return () => MADesignInfoSheetLine.Create(_holder, $"{e.Key} x {e.Value.Count}",
                    e.Value[0].m_info.m_description, true);
            }

            foreach (var c in m_building.BuildingComponents<BCAmmoStock>())
            {
                var blockName = c.GetAmmoBlock();
                string ammoName = "Default";
                if (blockName.IsNullOrWhiteSpace() == false)
                {
                    var info = NGBlockInfo.GetInfo(blockName);
                    if (info != null)
                    {
                        ammoName = info.m_displayName;
                    }
                }

                yield return () => MADesignInfoSheetLine.Create(_holder, $"{c.m_info.m_title}", ammoName, true);
            }
        }
    }
}