using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;
using VehicleStates;

public class MAVehicle : NGMovingObject, IStateControlledVehicle
{
    [SerializeField] 
    protected MAVehicleControl.VehicleMetadata m_vehicleMetaData;

    [SerializeField]
    private Transform m_frontAxle;

    [SerializeField]
    private Rigidbody m_frontAxleRigidBody;
    
    [SerializeField]
    private Transform m_backAxle;
    
    [SerializeField]
    private VehicleBaseState m_vehicleUpdateState = new VehicleStates.VehicleBaseState();
    
    [Range(1,50)]
    public float m_addGravityFactor = 30f;

    [SerializeField]
    private GameState_MAVehicle m_gameState;
    
    protected bool m_isTerrainVisible = false;
    
    private float m_axleLength;
    
    public MAVehicleControl.VehicleMetadata VehicleMetaData => m_vehicleMetaData;
    public GameState_MAVehicle VehicleData => m_gameState;
    public override GameState_MovingObject GameState => VehicleData;
    public override Rigidbody RigidBody => m_frontAxleRigidBody;
    
    protected override float[] NavigationCosts => GlobalData.s_vehicleCosts;
    public Rigidbody FrontAxleRigidBody => m_frontAxleRigidBody;
    
    protected bool IsKinematic
    {
        get => m_frontAxleRigidBody.isKinematic;
        set
        {
            if(m_frontAxleRigidBody.isKinematic != value)
                m_frontAxleRigidBody.isKinematic = value;
        }
    }

    public int GetQueueSlot()
    {
        if (m_gameState.m_queueSlot == 0)
            m_gameState.m_queueSlot = (Home?.Building?.ClaimQueueSlot() ?? -1) + 1;
        return m_gameState.m_queueSlot - 1;
    }

    public int GetQueueSlot(GateOpener _gate, int _direction)
    {
        if (m_gameState.m_gateQueueSlot == 0)
            m_gameState.m_gateQueueSlot = _gate.ClaimQueueSlot(_direction);
        else
            _gate.LoadQueueSlot(m_gameState.m_gateQueueSlot);
        return m_gameState.m_gateQueueSlot;
    }
    
    public Vector3 GetQueuePosition()
    {
        return Home.GetQueuePos(GetQueueSlot());
    }

    public void ReleaseQueueSlot()
    {
        if (m_gameState.m_queueSlot == 0 || m_gameState.m_queueSlot >= 16)
            return; // no queue slot or a gate queue slot, not our responsibility to release it
        Home?.Building?.ReleaseQueueSlot(m_gameState.m_queueSlot - 1);
        m_gameState.m_queueSlot = 0;
    }
    
    public void ReleaseQueueSlot(GateOpener _gate)
    {
        _gate.ReleaseQueueSlot(m_gameState.m_gateQueueSlot);
        m_gameState.m_gateQueueSlot = 0;
    }

    public virtual VehicleStates.VehicleBaseState VehicleState => m_vehicleUpdateState;	
    
    public virtual int FreeCargoSpace => 0;
    public virtual int MaxCargoSpace => 0;
    public bool IsFull => FreeCargoSpace <= 0;
    
    protected override void Awake()
    {
        base.Awake();

		if (m_nav == null)
			m_nav = m_frontAxle.gameObject.AddComponent<NavAgent>();

        m_axleLength = (float)System.Math.Round((m_frontAxle.position - m_backAxle.position).magnitude, 2);
        
        NGManager.Me.AddVehicle(this);
    }

    protected override void Start()
    {
        //m_nav.m_isRoadVehicle = true;
        m_nav.Initialise(this, GlobalData.s_vehicleCosts);
        
        m_frontAxle.position = m_frontAxle.position.SetYToHeight();
        m_backAxle.position = m_backAxle.position.SetYToHeight();
        
        m_isTerrainVisible = TerrainManager.Me.IsVisible(transform.position);
        
        IsKinematic = WantKinematic(m_isTerrainVisible);
    }
    
    override protected void Update()
    {
        base.Update();
        UpdatePosition();
    }

    protected override void OnDestroy()
    {
        base.OnDestroy();

        m_onPathProcessed = null;
        
        if(GameManager.IsShuttingDown == false)
        {
            GameManager.Me.m_state.m_vehicles.Remove(this.m_gameState);

            (Home as BCActionDispatch)?.OnVehicleDestroyed(this);
        }
        
        if (NGManager.Me != null)
        {
            NGManager.Me.RemoveVehicle(this);
        }
    }
	
    public override float GetDesiredSpeed()
    {
        return VehicleData.m_speed;
    }
    
    public virtual void Init()
    {
        Vector3 front = m_frontAxle.position;
        Vector3 back = m_backAxle.position;
        float highest = Mathf.Max(front.SetYToHeight().y, back.SetYToHeight().y);
        front.y = highest;
        back.y = highest;
        m_frontAxle.position = front;
        m_backAxle.position = back;
        //m_nav.m_isRoadVehicle = true;
        
        NGManager.Me.AddVehicle(this);
        
        VehicleData.m_speed = NGManager.Me.m_defaultObjectSpeed * 
                              NGManager.Me.m_vanSpeedModifier *
                              MAVehicleControl.Me.GeneralSpeedMod * 
                              m_vehicleMetaData.m_speedMod;
        
        m_nav.Pause(true, true);
        m_nav.Speed = VehicleData.m_speed;
        m_nav.Initialise(this, GlobalData.s_vehicleCosts);
        //m_axleLength = (float)System.Math.Round((m_frontAxle.position - m_backAxle.position).magnitude, 2);
        m_isTerrainVisible = TerrainManager.Me.IsVisible(transform.position);
        IsKinematic = WantKinematic(m_isTerrainVisible);
    }
    
    public virtual void OnArrivedAtFinalDestination()
    {
    }
    
    private void UpdatePosition()
    {
        if((TerrainManager.Me.AllBlocksReady && GameManager.Me.LoadComplete && GameManager.Me.DataReady) == false)
            return;

        var front = m_frontAxle.position;
        var back = front - (front - m_backAxle.position).GetXZNorm() * m_axleLength;
        back.y = (back.y + back.GroundPosition().y) * .5f;

        Transform tr = transform;
        tr.position = (front + back) * 0.5f;
        tr.LookAt(front);
        m_frontAxle.position = front;
        m_backAxle.position = back;
        m_backAxle.LookAt(front);

        bool isTerrainVisible = TerrainManager.Me.IsVisible(tr.position);
        bool terrainStateChanged = isTerrainVisible != m_isTerrainVisible;
        if(m_isTerrainVisible == false && isTerrainVisible)
        {
            bool hit = TrySetHeightByRayCast(transform, GetComponentInChildren<SphereCollider>().radius);
        }
        
        m_isTerrainVisible = isTerrainVisible;
		
        //IsKinematic = WantKinematic(isTerrainVisible);
		
        if (!IsKinematic && !terrainStateChanged)
        {
            m_frontAxleRigidBody.AddForce(Vector3.down * m_addGravityFactor, ForceMode.Acceleration);
        }
    }

    protected bool TrySetHeightByRayCast(Transform _pos, float _heightOffset)
    {
        bool hit = false;
        Vector3 pos = _pos.position;
        pos.y += 1000;
        Ray ray = new Ray(pos, Vector3.down);
        int layerMask = LayerMask.GetMask("Roads");
        if(Physics.Raycast(ray, out RaycastHit hitInfo, 10000f, layerMask, QueryTriggerInteraction.Ignore))
        {				
            if(LayerMask.LayerToName(hitInfo.collider.transform.gameObject.layer) == "Roads")
            {
                Vector3 position = _pos.position;
                position.y = hitInfo.point.y + _heightOffset;
                _pos.position = position;
                hit = true;
            }
        }
        return hit;
    }

    public VehicleStateFactory.VehicleState m_afterWait = VehicleStateFactory.VehicleState.kNone;
	public float m_waitFor = 0f;
 
    public override bool SetMoveToPosition(Vector3 _position, bool _direct = false, PeepActions _action = PeepActions.None, float _destinationRadius = 0f)
    {
        if ((TerrainManager.Me.AllBlocksReady && GameManager.Me.LoadComplete && GameManager.Me.DataReady) == false)
            return false;
        
        m_onPathProcessed -= OnPathProcessed;
        m_onPathProcessed += OnPathProcessed;
        
        if(NavigateToPosition(_position))
        {
            return true;
        }
        
        return false;
    }
    
    protected void OnPathProcessed(NGMovingObject.TargetReachability _reachability)
    {
        switch(_reachability)
        {
            case NGMovingObject.TargetReachability.IsReachable:
                break;
            case NGMovingObject.TargetReachability.IsNotReachable:
                break;
            case NGMovingObject.TargetReachability.IsNotReachableAndAlreadyAtNearestPos:
                break;
            case NGMovingObject.TargetReachability.PathFindingAlreadyInProgress:
                break;
            case NGMovingObject.TargetReachability.PathFailure:
                break;
        }
    }
    
    override public void SetState(STATE _state)
    {
        base.SetState(_state);

        if(m_vehicleUpdateState != null) name += $", vehicleState: {m_vehicleUpdateState.State}";
    }                                                                                                                                                                                           
    
    public virtual void SetVehicleState(VehicleBaseState _newState)
    {
        m_vehicleUpdateState = _newState;
        
		name = $"[" + m_ID + $"] {GetType().Name}: moveState: " + m_state.ToString();
        name += $", vehicleState: {m_vehicleUpdateState.State}";
    }
    
    protected virtual bool WantKinematic(bool _isTerrainVisible)
    {
        return !_isTerrainVisible;
    }

    protected virtual NGReactPickupAny CreateCargoVisuals(GameState_Product _orderItem) { return null; }
    
    public virtual void HandOverCargo() { }

    public override void SetGameStateSaveData(GameState_MovingObject _gameState)
    {
        base.SetGameStateSaveData(_gameState);
        m_gameState = _gameState as GameState_MAVehicle;
    }
    
    public void SetVehicleData(GameState_MAVehicle _gameState)
    {
        SetGameStateSaveData(_gameState);
        m_gameState = _gameState;
        if (GameManager.Me.m_state.m_vehicles.Contains(m_gameState) == false)
        {
            GameManager.Me.m_state.m_vehicles.Add(m_gameState);
        }
        
        SetVehicleData_Internal(_gameState);
    }

    public override float Health { get => VehicleData.m_health; set => VehicleData.m_health = value;
    }

    public override void PostLoad()
    {
        base.PostLoad();
        
        VehicleStateFactory.VehicleState vehicleState = (VehicleStateFactory.VehicleState)m_gameState.m_vehicleState;
        VehicleStateFactory.ApplyState(vehicleState, this);
        Home?.Building?.LoadQueueSlot(m_gameState.m_queueSlot - 1);
    }

    protected virtual void SetVehicleData_Internal(GameState_MAVehicle _vehicleData)
    {
        Health = 1f;//_vehicleData.m_health; //TODO: Add to Knack!!
        m_ID = _vehicleData.m_id;
        
        Transform tr = transform;
        tr.position = _vehicleData.m_pos;
        tr.eulerAngles = _vehicleData.m_rotation;

        if(GameManager.Me.DataReady == false)
        {
            VehicleStateFactory.VehicleState vehicleState = (VehicleStateFactory.VehicleState)_vehicleData.m_vehicleState;
            VehicleStateFactory.ApplyState(vehicleState, this);
        }
    }

    public void PreSave()
    {
        VehicleData.PreSave(this);
    }
    
    public static MAVehicle Create(string _type)
    {
        return MAVehicleControl.Me.SpawnVehicleByPrefabName(_type);
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(MAVehicle))]
public class MAVehicleEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        MAVehicle vehicle = (MAVehicle)target;
        
        GUILayout.Space(10);
        GUILayout.TextArea($"Vehicle State: {vehicle.VehicleState.State.ToString()}");
        GUILayout.Space(10);

        for(int i = 0; i < (int)VehicleStateFactory.VehicleState.kCOUNT; i++)
        {
            if(GUILayout.Button($"Set State: {(VehicleStateFactory.VehicleState)i}"))
            {
                VehicleStateFactory.ApplyState((VehicleStateFactory.VehicleState)i, vehicle);
            }
        }
    }
}
#endif