using System.Collections.Generic;
using UnityEngine;

public class BCGuildHealing : BCBase
{
    [KnackField] public float m_heroHealingPerSecond = 1f;
    [KnackField] public int m_heroesToHeal = 1;
    private GameObject m_heroBlockVisual;
    private int m_doneThisFrame = 0;
    
    public string[] m_acceptsCharacterTypes = new string[] { "Giant", "GiantFemale" };

    public override List<MACharacterBase> GetHeroesPresent()
    {
        return m_building.GetHeroesPresent();
    }
    
    public bool TryHeal(MAHeroBase _hero, float _multiplier)
    {
        if(m_doneThisFrame < m_heroesToHeal)
        {
            HealHero(_hero, _multiplier);
            m_doneThisFrame++;
            return true;
        }
        return false;
    }
    
    public static bool RequiresHealing(MACharacterBase _hero)
    {
        return _hero.NormalizedHealth < 1f;
    }

    private void SetupBlockHeroCharacter()
    {
        var character = GetComponentInChildren<CharacterVisuals>();
        if(character != null)
        {
            m_heroBlockVisual = character.gameObject;
            SetHeroVisible(false);
        }
    }
    
    public override void Activate(int _indexOfComponentType, int _quantityInBuilding)
    {
        SetupBlockHeroCharacter();
        base.Activate(_indexOfComponentType, _quantityInBuilding);
    }

    private void SetHeroVisible(bool _value)
    {
        if(m_heroBlockVisual == null || m_heroBlockVisual.activeSelf == _value)
            return;
        
        m_heroBlockVisual.SetActive(_value);
    }
    
    public override void UpdateInternal(BuildingComponentsState _state)
    {
        SetHeroVisible(m_doneThisFrame > 0);
    }

    public override void LateUpdateInternal()
    {
        base.LateUpdateInternal();
        m_doneThisFrame = 0;
    }

    private void HealHero(MACharacterBase _hero, float _multiplier)
    {
        _hero.RecoverHealthImmediate(m_heroHealingPerSecond * _multiplier);
    }
    
    override public (string id, System.Func<BCUIPanel> create) GetUIPanelInfo() => (m_info?.id, () => new BCHealingPanel(m_info));
    
    public class BCHealingPanel : BCUIPanel
    {
        protected BCGuildHealing m_healingCmp;
        private string m_blockID;
        public override string SpriteBlockID => m_blockID;
        public override string GetPrimaryText() =>  m_healingCmp?.m_info?.m_description;

        public BCHealingPanel(MAComponentInfo _info) : base(_info) { }

        public override void AddComponent(BCBase _component)
        {
            if(_component is BCGuildHealing)
            {
                m_blockID = _component.Block.BlockID;
                m_healingCmp = _component as BCGuildHealing;
                base.AddComponent(_component);
            }
        }
    }
}
