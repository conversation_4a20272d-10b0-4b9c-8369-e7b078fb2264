using System;
using Unity.Mathematics;
using Unity.Jobs;
using Unity.Collections;
using Unity.Burst;
using UnityEngine;

[BurstCompile]
public struct HeatmapGenerationJob : IJobParallelFor
{
    [ReadOnly] private NativeArray<float> pixelMaxDensity;
    [ReadOnly] private NativeArray<float> pixelMinDensity;
    [ReadOnly] private NativeArray<float> pixelMeanDensity;
    [ReadOnly] private NativeArray<int> pixelTriangleCount;
    [ReadOnly] private float globalMaxDensity;
    [ReadOnly] private float globalMinDensity;
    [ReadOnly] private int heatmapDisplayMode;

    private NativeArray<Color> colorArray;
    
    public HeatmapGenerationJob(NativeArray<float> _maxDensity, NativeArray<float> _minDensity, 
        NativeArray<float> _meanDensity, NativeArray<int> _triangleCount, float _globalMaxDensity, 
        float _globalMinDensity, int _heatmapDisplayMode, NativeArray<Color> _colorArray)
    {
        pixelMaxDensity = _maxDensity;
        pixelMinDensity = _minDensity;
        pixelMeanDensity = _meanDensity;
        pixelTriangleCount = _triangleCount;
        globalMaxDensity = _globalMaxDensity;
        globalMinDensity = _globalMinDensity;
        heatmapDisplayMode = _heatmapDisplayMode;
        colorArray = _colorArray;
    }

    private static Color GetHeatmapColor(float value)
    {
        value = math.saturate(value);

        var color = new Color(0f, 0f, 0f, 1f);
        switch (value)
        {
            case <= 0.25f:
                color.r = 0f;
                color.g = 4f * value;
                color.b = 1f;
                break;
            case <= 0.5f:
                color.r = 0f;
                color.g = 1f;
                color.b = 1f - 4f * (value - 0.25f);
                break;
            case <= 0.75f:
                color.r = 4f * (value - 0.5f);
                color.g = 1f;
                color.b = 0f;
                break;
            default:
                color.r = 1f;
                color.g = 1f - 4f * (value - 0.75f);
                color.b = 0f;
                break;
        }

        return color;
    }

    public void Execute(int i)
    {
        if (pixelTriangleCount[i] == 0) return;

        float density = heatmapDisplayMode switch
        {
            0 => pixelMaxDensity[i],
            1 => pixelTriangleCount[i] == 0 ? 0f : pixelMinDensity[i],
            2 => pixelMeanDensity[i],
            _ => pixelMaxDensity[i]
        };

        float normalized = (density - globalMinDensity) / (globalMaxDensity - globalMinDensity + float.Epsilon);
        colorArray[i] = GetHeatmapColor(normalized);
    }
}


