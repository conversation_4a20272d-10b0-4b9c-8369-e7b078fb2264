using UnityEngine;
using UnityEditor;
using System.Collections.Generic;
using System.Linq;

public class OptimiserReportTab : ScriptableObject
{
    public string m_title;

    protected void DrawDoubleLabel(string label1, string label2, GUIStyle style)
    {
        EditorGUILayout.BeginHorizontal();
        EditorGUILayout.LabelField(label1, style);
        GUILayout.FlexibleSpace();
        EditorGUILayout.LabelField(label2, style);
        EditorGUILayout.EndHorizontal();
    }
    
    public virtual void Draw(OptimiserReport report, OptimiserReportEditor editor) { }
}

[CustomEditor(typeof(OptimiserReport))]
public class OptimiserReportEditor : Editor
{
    private List<OptimiserReportTab> tabs;
    private int selectedTab;

    private void OnEnable()
    {
        tabs = new List<OptimiserReportTab>
        {
            CreateInstance<ORMainTab>(),
            CreateInstance<ORSizeTab>(),
            CreateInstance<ORTimeTab>(),
            CreateInstance<ORRendererTab>(),
            CreateInstance<ORAtlasTab>(),
            CreateInstance<ORErrorTab>(),
        };
    }

    public override void OnInspectorGUI()
    {
        if (tabs == null)
            return;
        serializedObject.Update();
        if (GUILayout.Button("Refresh Data"))
        {
            var report = (OptimiserReport)target;
            report.OnEnable();
        }

        EditorGUILayout.BeginHorizontal();

        GUILayout.BeginVertical(GUILayout.ExpandWidth(true));
        selectedTab = GUILayout.Toolbar(
            selectedTab,
            tabs.Select(tab => tab.m_title).ToArray(),
            GUILayout.ExpandWidth(true)
        );
        EditorGUILayout.Space(4);

        tabs[selectedTab].Draw((OptimiserReport)target, this);

        GUILayout.EndVertical();
        EditorGUILayout.EndHorizontal();
    }
}