using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

#if UNITY_EDITOR
using UnityEditor;

[CustomPropertyDrawer(typeof(BinaryDisplayAttribute))]
public class BinaryDisplayDrawer : PropertyDrawer
{
	private bool binary = true;

	private static GUIStyle labelStyle = new GUIStyle( GUI.skin.label );
	private static float buttonWidth = GUI.skin.label.CalcSize(new GUIContent(" decimal ")).x;

	public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
	{
		if (property.propertyType != SerializedPropertyType.Integer) {
            EditorGUI.LabelField(position, label.text, "Use BinaryDisplay with an integer types only.");
			return;
		}

		var padding = ((BinaryDisplayAttribute)attribute).binaryPadding;

		int prop = property.intValue;
		string converted = (binary) ? System.Convert.ToString(prop, 2).PadLeft(padding, '0') : prop.ToString();
		string n = EditorGUI.DelayedTextField( position, label, converted );

		Rect buttonPos = position;
		buttonPos.x += EditorGUIUtility.labelWidth - buttonWidth - 1; // 1 for a pixel worth of padding.
		buttonPos.width = buttonWidth;
		labelStyle.alignment = TextAnchor.MiddleCenter;

		if( EditorGUI.Toggle( buttonPos, false, GUI.skin.button ) ) {
			binary = !binary;
		}

		int indent = EditorGUI.indentLevel;
		EditorGUI.indentLevel = 0;
		EditorGUI.LabelField( buttonPos, (binary) ? "binary" : "decimal", labelStyle );
		EditorGUI.indentLevel = indent;

		if( n != converted ) {
			prop = (binary) ? TryConvertBinaryToInt(n) : Convert.ToInt32(n);
			property.intValue = prop;
		}
	}

	private int TryConvertBinaryToInt( string v )
	{
		try {
			return Convert.ToInt32( v, 2 );
		}
		catch {
			return Convert.ToInt32( v );
		}
	}
}
#endif

public class BinaryDisplayAttribute : PropertyAttribute
{
	public readonly byte binaryPadding;

	// If you're using this attribute on an int, make sure you pass in sizeof(int);
	public BinaryDisplayAttribute( byte bytes = sizeof(byte) ) {
		this.binaryPadding = (byte)(bytes * 8);
	}
}