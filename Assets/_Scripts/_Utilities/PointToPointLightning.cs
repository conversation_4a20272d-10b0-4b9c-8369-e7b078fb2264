using UnityEngine;

public class PointToPointLightning : MonoBehaviour
{
    public Transform m_from;
    public Transform m_to;
    public LineRenderer m_lineRenderer;
    public float m_lengthPerSegment = .8f;
    public float m_randomDistance = .4f;
    public float m_arcHeight = .4f;
    public float m_noiseScale = 1f;
    public bool m_animating = true;
    private bool m_haveCreated = false;
    void Update()
    {
        if (m_animating || m_haveCreated == false)
        {
            MAPowerEffectLightning.FillLineWithLightning(m_from.position, m_to.position, m_lineRenderer, GetInstanceID(), .666f, m_lengthPerSegment, m_randomDistance, m_arcHeight, m_noiseScale);
            m_haveCreated = true;
        }
    }
}
