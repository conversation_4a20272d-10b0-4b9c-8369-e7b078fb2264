using System.Collections;
using System.Collections.Generic;
using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class EditorDisplayUtilities
{
#if UNITY_EDITOR
	
	// =================================================================================================================

	public class FoldoutStateTracker
	{
		public bool Open;
		public List<FoldoutStateTracker> SubFoldouts;
	}
	
	// =================================================================================================================
	
	public static void DrawTitle(string _text, int _fontSize = 12)
	{
		GUIStyle style =  new GUIStyle(EditorStyles.label);
		style.fontSize = _fontSize;

		GUILayout.BeginHorizontal();
		GUILayout.FlexibleSpace();
		GUILayout.Label(_text, style);
		GUILayout.FlexibleSpace();
		GUILayout.EndHorizontal();
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	public static void DisplayStringList(List<string> _list, string _label, float _fieldWidth)
	{
		GUILayout.BeginVertical();
		GUILayout.Label(_label);
		GUILayout.BeginHorizontal(GUILayout.Width(_fieldWidth));

		if(GUILayout.Button("Add"))
		{
			_list.Add("");
		}

		GUI.enabled = _list.Count > 0;
		if(GUILayout.Button("Remove"))
		{
			_list.RemoveAt(_list.Count - 1);
		}
		GUI.enabled = true;

		GUILayout.EndHorizontal();

		for(int i = 0; i < _list.Count; i++)
		{
			_list[i] = EditorGUILayout.TextField(" ", _list[i], GUILayout.Width(_fieldWidth));
		}

		GUILayout.EndVertical();
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	public static Vector3 DisplayVector(Vector3 _vector, string _label, float _fieldWidth)
	{
		GUILayout.Label(_label);
		GUILayout.BeginHorizontal(GUILayout.Width(_fieldWidth));

		var textDimensions = GUI.skin.label.CalcSize(new GUIContent("x:"));
		float oldWidth = EditorGUIUtility.labelWidth;
		EditorGUIUtility.labelWidth = textDimensions.x;


		_vector.x = EditorGUILayout.FloatField("x:",_vector.x);
		_vector.y = EditorGUILayout.FloatField("y:",_vector.y);
		_vector.z = EditorGUILayout.FloatField("z:",_vector.z);
		EditorGUIUtility.labelWidth = oldWidth;

		GUILayout.EndHorizontal();

		return _vector;
	}
	
	// -----------------------------------------------------------------------------------------------------------------

	public static string TextField(string _label, string _text, int _width)
	{
		var textDimensions = GUI.skin.label.CalcSize(new GUIContent(_label));
		float oldWidth = EditorGUIUtility.labelWidth;
		EditorGUIUtility.labelWidth = textDimensions.x;
		string t = EditorGUILayout.TextField(_label, _text, GUILayout.Width(EditorGUIUtility.labelWidth + _width));
		EditorGUIUtility.labelWidth = oldWidth;
		return t;
	}
	
	public static string TextFieldWithToolTip(string _label, string _text, int _width)
	{
		var textDimensions = GUI.skin.label.CalcSize(new GUIContent(_label));
		float oldWidth = EditorGUIUtility.labelWidth;
		EditorGUIUtility.labelWidth = textDimensions.x;
		string t = EditorGUILayout.TextField(new GUIContent(_label, _text), _text, GUILayout.Width(EditorGUIUtility.labelWidth + _width));
		EditorGUIUtility.labelWidth = oldWidth;
		return t;
	}
	
	
	// -----------------------------------------------------------------------------------------------------------------
	
	public static string TextField(string _label, string _text)
	{
		var textDimensions = GUI.skin.label.CalcSize(new GUIContent(_label));
		float oldWidth = EditorGUIUtility.labelWidth;
		EditorGUIUtility.labelWidth = textDimensions.x;
		string t = EditorGUILayout.TextField(_label, _text);
		EditorGUIUtility.labelWidth = oldWidth;
		return t;
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	public static float FloatField(string _label, float _value, int _width)
	{
		var textDimensions = GUI.skin.label.CalcSize(new GUIContent(_label));
		float oldWidth = EditorGUIUtility.labelWidth;
		EditorGUIUtility.labelWidth = textDimensions.x;
		float t = EditorGUILayout.FloatField(_label, _value, GUILayout.Width(EditorGUIUtility.labelWidth + _width));
		EditorGUIUtility.labelWidth = oldWidth;
		return t;
	}
	
	// -----------------------------------------------------------------------------------------------------------------
	
	public static T ObjectField<T>(T _obj, float _width) where T: Object
	{
#pragma warning disable 0618 // obsolete
		T t = (T)EditorGUILayout.ObjectField(_obj, typeof(T), GUILayout.MaxWidth(_width));
#pragma warning restore 0618 // obsolete
		
		return t;
	}	
	
	// =================================================================================================================
#endif
	
	public class BitMaskAttribute : PropertyAttribute
	{
		public System.Type propType;
		public BitMaskAttribute(System.Type aType)
		{
			propType = aType;
		}
	}

	#if UNITY_EDITOR
	public static int DrawBitMaskField (Rect aPosition, int aMask, System.Type aType, GUIContent aLabel)
     {
         var itemNames = System.Enum.GetNames(aType);
         var itemValues = System.Enum.GetValues(aType) as int[];
         
         int val = aMask;
         int maskVal = 0;
         for(int i = 0; i < itemValues.Length; i++)
         {
             if (itemValues[i] != 0)
             {
                 if ((val & itemValues[i]) == itemValues[i])
                     maskVal |= 1 << i;
             }
             else if (val == 0)
                 maskVal |= 1 << i;
         }
         int newMaskVal = EditorGUI.MaskField(aPosition, aLabel, maskVal, itemNames);
         int changes = maskVal ^ newMaskVal;
         
         for(int i = 0; i < itemValues.Length; i++)
         {
             if ((changes & (1 << i)) != 0)            // has this list item changed?
             {
                 if ((newMaskVal & (1 << i)) != 0)     // has it been set?
                 {
                     if (itemValues[i] == 0)           // special case: if "0" is set, just set the val to 0
                     {
                         val = 0;
                         break;
                     }
                     else
                         val |= itemValues[i];
                 }
                 else                                  // it has been reset
                 {
                     val &= ~itemValues[i];
                 }
             }
         }
         return val;
     }
 
	[CustomPropertyDrawer(typeof(BitMaskAttribute))]
	public class EnumBitMaskPropertyDrawer : PropertyDrawer
	{
		public override void OnGUI(Rect position, SerializedProperty prop, GUIContent label)
		{
			var typeAttr = attribute as BitMaskAttribute;
			// Add the actual int value behind the field name
			label.text = label.text + "(" + prop.intValue + ")";
			prop.intValue = DrawBitMaskField(position, prop.intValue, typeAttr.propType, label);
		}
	}
	#endif
}
