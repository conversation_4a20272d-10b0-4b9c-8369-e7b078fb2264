using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class PrefabUtils 
{
     public static bool IsPrefabInstance(GameObject obj)
     {
         bool is_prefab_instance = false;
 #if UNITY_EDITOR
         is_prefab_instance = UnityEditor.PrefabUtility.IsPartOfAnyPrefab(obj)
             && !UnityEditor.PrefabUtility.IsPartOfPrefabAsset(obj)
             && UnityEditor.PrefabUtility.IsPartOfNonAssetPrefabInstance(obj);
 #endif
         return is_prefab_instance;
     }
	 
     public static bool IsInPrefabEdit(GameObject obj)
     {
         bool is_prefab_asset = false;
 #if UNITY_EDITOR
         var stage = UnityEditor.SceneManagement.PrefabStageUtility.GetCurrentPrefabStage();
         is_prefab_asset = (stage != null
                 && stage.scene == obj.scene)
             || (UnityEditor.PrefabUtility.IsPartOfAnyPrefab(obj)
                     && UnityEditor.PrefabUtility.IsPartOfPrefabAsset(obj)
                     && !UnityEditor.PrefabUtility.IsPartOfNonAssetPrefabInstance(obj));
 #endif
         return is_prefab_asset;
     }
}
