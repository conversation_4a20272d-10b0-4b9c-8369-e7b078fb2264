using System;
using System.Collections;
using System.Collections.Generic;
using System.Linq.Expressions;
using System.Reflection;
using System.Runtime.Serialization;

public static class DeepCopyHelper
{
    private static readonly Dictionary<Type, FieldInfo[]> _fieldCache = new();
    private static readonly Dictionary<Type, Func<object>> _constructorCache = new();

    public static T DeepCopy<T>(T obj)
    {
        if (obj == null) return default;
        return (T)CopyObject(obj, new Dictionary<object, object>());
    }

    public class TypeWrapper
    {
        public Type Type { get; }
        public bool IsValueType { get; }
        public bool IsArray { get; }
        public Type ElementType { get; }
        public Type GetElementType() => ElementType;
        public FieldInfo[] Fields { get; }

        public TypeWrapper(Type _type)
        {
            Type = _type;
            IsValueType = _type.IsValueType;
            IsArray = _type.IsArray;
            if (IsArray) ElementType = _type.GetElementType();
            Fields = GetCachedFields(_type);
        }

        private static Dictionary<Type, TypeWrapper> _cache = new();
        public static TypeWrapper Get(Type _type)
        {
            if (!_cache.TryGetValue(_type, out var wrapper))
            {
                wrapper = new TypeWrapper(_type);
                _cache[_type] = wrapper;
            }
            return wrapper;
        }
        
        public static bool operator==(TypeWrapper _tw, Type _t) => _tw.Type == _t; 
        public static bool operator!=(TypeWrapper _tw, Type _t) => _tw.Type != _t;
        public static implicit operator Type(TypeWrapper _tw) => _tw.Type;
    }

    private static object CopyObject(object obj, Dictionary<object, object> visited)
    {
        if (obj == null) return null;
        var type = TypeWrapper.Get(obj.GetType());

        // Handle primitive types and strings (immutable)
        if (type.IsValueType || type == typeof(string))
            return obj;

        // Prevent infinite loops (handle circular references)
        if (visited.TryGetValue(obj, out var existingCopy))
            return existingCopy;

        // Handle arrays
        if (type.IsArray)
        {
            var elementType = type.GetElementType();
            var array = (Array)obj;
            var copiedArray = Array.CreateInstance(elementType, array.Length);
            visited[obj] = copiedArray;

            for (int i = 0; i < array.Length; i++)
            {
                copiedArray.SetValue(CopyObject(array.GetValue(i), visited), i);
            }

            return copiedArray;
        }

        // Handle Lists
        if (obj is IList list)
        {
            var listType = obj.GetType();
            var newList = (IList)CreateInstance(listType);
            visited[obj] = newList;

            foreach (var item in list)
            {
                newList.Add(CopyObject(item, visited));
            }

            return newList;
        }

        // Handle Dictionaries
        if (obj is IDictionary dictionary)
        {
            var dictType = obj.GetType();
            var newDict = (IDictionary)CreateInstance(dictType);
            visited[obj] = newDict;

            foreach (DictionaryEntry entry in dictionary)
            {
                newDict.Add(CopyObject(entry.Key, visited), CopyObject(entry.Value, visited));
            }

            return newDict;
        }

        // Handle objects (prefer default constructor, fallback to uninitialized object)
        var copy = CreateInstance(type);
        visited[obj] = copy;

        var fields = GetCachedFields(type);
        foreach (var field in fields)
        {
            field.SetValue(copy, CopyObject(field.GetValue(obj), visited));
        }

        return copy;
    }

    private static object CreateInstance(Type type)
    {
        if (!_constructorCache.TryGetValue(type, out var constructor))
        {
            constructor = CreateConstructorViaReflection(type);
            _constructorCache[type] = constructor;
        }
        return constructor();
    }

    private static Func<object> CreateConstructorViaReflection(Type type)
    {
        var constructorInfo = type.GetConstructor(Type.EmptyTypes);
        if (constructorInfo != null)
        {
            // Use reflection to invoke the constructor, which is compatible with IL2CPP
            return () => constructorInfo.Invoke(null);
        }

        // Fallback to uninitialized object in case no default constructor exists
        return () => FormatterServices.GetUninitializedObject(type);
    }

    private static FieldInfo[] GetCachedFields(Type type)
    {
        if (!_fieldCache.TryGetValue(type, out var fields))
        {
            fields = type.GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);

            // Exclude auto-property backing fields (<PropertyName>k__BackingField)
            fields = Array.FindAll(fields, field => !field.Name.Contains("k__BackingField"));

            _fieldCache[type] = fields;
        }
        return fields;
    }
}
