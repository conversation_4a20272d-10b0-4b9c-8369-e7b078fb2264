#if CHOICES
using UnityEditor;
using UnityEngine;

public class InvUnitTests : MonoBehaviour
{
#if UNITY_EDITOR
    private const string LocaleUnitTestLabel = "22Cans/LocaleUnitTests";
    [MenuItem(LocaleUnitTestLabel + "/Test All")] private static void TestAll() { RunAllTests(); }
    [MenuItem(LocaleUnitTestLabel + "/Test String")] private static void TestString() { StringTest(); }
    [MenuItem(LocaleUnitTestLabel + "/Test NGStock")] private static void TestNGStock() { NGStockTest(); }
    [MenuItem(LocaleUnitTestLabel + "/Test DesignTableManager")] private static void TestDesignTableManager() { DesignTableManagerTest(); }
    [MenuItem(LocaleUnitTestLabel + "/Test NGDock")] private static void TestNGDock() { NGDockTest(); }



    public const string english = "en-GB", french = "fr-FR";

    static void RunAllTests()
    {
        StringTest();
        NGStockTest();
        DesignTableManagerTest();
        NGDockTest();
    }



    public static void SwitchLocale(string locale, bool print = false)
    {
        System.Globalization.CultureInfo ci = new System.Globalization.CultureInfo(locale);
        System.Threading.Thread.CurrentThread.CurrentCulture = ci;
        System.Threading.Thread.CurrentThread.CurrentUICulture = ci;
        if(print)
            PrintCurrentLocale();
    }

    static void PrintCurrentLocale()
    {
        Debug.Log("The current locale is: " + System.Threading.Thread.CurrentThread.CurrentCulture);
    }

    static void ConstantFrameRate(bool frame)
    {
        Time.captureDeltaTime = frame ? 1f / 60f : 0f;
    }

    #region Unit Tests

    static void StringTest()
    {

        SwitchLocale(french);
        float myFloat = 30.5f;
        string floatToString = myFloat.ToString();
        float stringBackToFloat = float.Parse(floatToString);
        Debug.Log("my float is :" + myFloat + " to string: " + floatToString + " The string back to a float is " + stringBackToFloat);

        SwitchLocale(english);

        float engFloat = myFloat;
        string engFloatToString = floatToString.ToString();
        float engStringBackToInt = float.Parse(engFloatToString);

        Debug.Log("my float is :" + engFloat + " to string: " + engFloatToString + " The string back to a float is " + engStringBackToInt);

        Debug.Log("Using Invariable Functions");
        floatToString = myFloat.ToStringInv();
        floatToString.TryFloatInv(out stringBackToFloat);
        Debug.Log("my float is :" + myFloat + " to string: " + floatToString + " The string back to a float is " + stringBackToFloat);


    }

    static void NGStockTest()
    {
        NGKnack.SetEditorMe();
        NGCarriableResource.LoadInfo();

        int passedTests = 0;
        int failedTests = 0;

        NGStock TestStock()
        {
            var res01 = NGCarriableResource.GetInfo("RawResourceMetal");
            var res02 = NGCarriableResource.GetInfo("RawMaterialWood");
            var stock = new NGStock();
            stock.AddRequirements(res01, 3);
            stock.AddRequirements(res02, 5);
            stock.AddStock(res01, 1);
            stock.AddStock(res02, 3);
            return stock;
        }

        void PrintTest(NGStock stock, string conversion)
        {
            string expectedResult = "RawResourceMetal,1,3.000|RawMaterialWood,3,5.000";

            string expectedPrint = "Expected result: " + expectedResult;
            string givenPrint = "Given result " + stock.ExportString();
            bool testPassed = string.Compare(stock.ExportString(), expectedResult) == 0;

            if (!testPassed)
            {
                Debug.Log(expectedPrint + "\n" + givenPrint);
                failedTests++;
                Debug.Log(conversion + " " + (testPassed ? "Test Passed" : "Test Failed"));
            }
            else
                passedTests++;
        }

        SwitchLocale(english);
        var NGStockEN = TestStock();
        PrintTest(NGStockEN, "English -> English: ");

        SwitchLocale(french);
        PrintTest(NGStockEN, "English -> French: ");

        var NGStockFR = TestStock();
        PrintTest(NGStockFR, "French - > French: ");

        SwitchLocale(english);
        PrintTest(NGStockFR, "French - > English: ");

        Debug.Log($"Tests passed: {passedTests} / {passedTests + failedTests}");
    }

    static void DesignTableManagerTest()
    {
        ConstantFrameRate(true);

        int passedTests = 0;
        int failedTests = 0;
        const string testData = "s339,1495,10049,-14004,-125,-557,820,96,0";

        void PrintTest(string conversion)
        {
            DesignTableManager.Me.CurrentHighlightId = testData;
            DesignTableManager.Me.AdjustStickerDataExternal(1, 0.5f, 0.3f);

            bool testPassed = string.Compare(DesignTableManager.Me.CurrentHighlightId, testData) == 0;

            if (!testPassed)
            {
                failedTests++;
                Debug.Log("Expected result: "+ testData + "\n" + "Given result: " + DesignTableManager.Me.CurrentHighlightId);
                Debug.Log(conversion);
            }
            else
                passedTests++;
        }

        SwitchLocale(english);
        PrintTest("English -> English");

        SwitchLocale(french);
        PrintTest("English -> French");

        PrintTest("French -> French");

        SwitchLocale(english);
        PrintTest("French -> English");

        Debug.Log($"Tests passed: {passedTests} / {passedTests + failedTests}");

        ConstantFrameRate(false);

    }

    /// <summary>
    /// To test this one make a gameobject and put an NGDock class onto it
    /// </summary>
    static void NGDockTest()
    {/*
        var dock = FindObjectOfType<NGDock>();

        int passedTests = 0;
        int failedTests = 0;
        const string testData = ":GetType:AwaitBoatId=-1@DockState=WaitingForBoat@BoatTimeLeft=12.5@StockCount=0@StockRequiredCount=50@DesiredCommodity=Product";

        NGKnack.SetEditorMe();
        NGCarriableResource.LoadInfo();
        ConstantFrameRate(true);


        if (dock == null) Debug.LogWarning("Dock is missing, make sure to create a test dock object, and drag a reference in");

        void PrintTest(string conversion)
        {
            dock.BoatTimer = Time.time + 12.5f;
            string save = dock.GetReactProto().m_typeSpecificData;
            dock.Load(save);
            bool testPassed = string.Compare(save, testData) == 0;

            if (!testPassed)
            {
                failedTests++;
                Debug.Log(conversion);
                Debug.Log("Expected result: " + testData);
                Debug.Log("Given result: " + save);
            }
            else
                passedTests++;
        }

        SwitchLocale(english);
        PrintTest("English -> English");

        SwitchLocale(french);
        PrintTest("English -> French");

        PrintTest("French -> French");

        SwitchLocale(english);
        PrintTest("French -> English");

        Debug.Log($"Tests passed: {passedTests} / {passedTests + failedTests}");

        ConstantFrameRate(false);
*/
    }

   

   
    #endregion
#endif
}
#endif
