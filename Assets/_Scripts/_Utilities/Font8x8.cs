using UnityEngine;

public static class Font8x8
{
    static byte[] s_font  = {
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,   // U+0020 (space)
        0x18, 0x3C, 0x3C, 0x18, 0x18, 0x00, 0x18, 0x00,   // U+0021 (!)
        0x36, 0x36, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,   // U+0022 (")
        0x36, 0x36, 0x7F, 0x36, 0x7F, 0x36, 0x36, 0x00,   // U+0023 (#)
        0x0C, 0x3E, 0x03, 0x1E, 0x30, 0x1F, 0x0C, 0x00,   // U+0024 ($)
        0x00, 0x63, 0x33, 0x18, 0x0C, 0x66, 0x63, 0x00,   // U+0025 (%)
        0x1C, 0x36, 0x1C, 0x6E, 0x3B, 0x33, 0x6E, 0x00,   // U+0026 (&)
        0x06, 0x06, 0x03, 0x00, 0x00, 0x00, 0x00, 0x00,   // U+0027 (')
        0x18, 0x0C, 0x06, 0x06, 0x06, 0x0C, 0x18, 0x00,   // U+0028 (()
        0x06, 0x0C, 0x18, 0x18, 0x18, 0x0C, 0x06, 0x00,   // U+0029 ())
        0x00, 0x66, 0x3C, 0xFF, 0x3C, 0x66, 0x00, 0x00,   // U+002A (*)
        0x00, 0x0C, 0x0C, 0x3F, 0x0C, 0x0C, 0x00, 0x00,   // U+002B (+)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x0C, 0x06,   // U+002C (,)
        0x00, 0x00, 0x00, 0x3F, 0x00, 0x00, 0x00, 0x00,   // U+002D (-)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x0C, 0x0C, 0x00,   // U+002E (.)
        0x60, 0x30, 0x18, 0x0C, 0x06, 0x03, 0x01, 0x00,   // U+002F (/)
        0x3E, 0x63, 0x73, 0x7B, 0x6F, 0x67, 0x3E, 0x00,   // U+0030 (0)
        0x0C, 0x0E, 0x0C, 0x0C, 0x0C, 0x0C, 0x3F, 0x00,   // U+0031 (1)
        0x1E, 0x33, 0x30, 0x1C, 0x06, 0x33, 0x3F, 0x00,   // U+0032 (2)
        0x1E, 0x33, 0x30, 0x1C, 0x30, 0x33, 0x1E, 0x00,   // U+0033 (3)
        0x38, 0x3C, 0x36, 0x33, 0x7F, 0x30, 0x78, 0x00,   // U+0034 (4)
        0x3F, 0x03, 0x1F, 0x30, 0x30, 0x33, 0x1E, 0x00,   // U+0035 (5)
        0x1C, 0x06, 0x03, 0x1F, 0x33, 0x33, 0x1E, 0x00,   // U+0036 (6)
        0x3F, 0x33, 0x30, 0x18, 0x0C, 0x0C, 0x0C, 0x00,   // U+0037 (7)
        0x1E, 0x33, 0x33, 0x1E, 0x33, 0x33, 0x1E, 0x00,   // U+0038 (8)
        0x1E, 0x33, 0x33, 0x3E, 0x30, 0x18, 0x0E, 0x00,   // U+0039 (9)
        0x00, 0x0C, 0x0C, 0x00, 0x00, 0x0C, 0x0C, 0x00,   // U+003A (:)
        0x00, 0x0C, 0x0C, 0x00, 0x00, 0x0C, 0x0C, 0x06,   // U+003B (;)
        0x18, 0x0C, 0x06, 0x03, 0x06, 0x0C, 0x18, 0x00,   // U+003C (<)
        0x00, 0x00, 0x3F, 0x00, 0x00, 0x3F, 0x00, 0x00,   // U+003D (=)
        0x06, 0x0C, 0x18, 0x30, 0x18, 0x0C, 0x06, 0x00,   // U+003E (>)
        0x1E, 0x33, 0x30, 0x18, 0x0C, 0x00, 0x0C, 0x00,   // U+003F (?)
        0x3E, 0x63, 0x7B, 0x7B, 0x7B, 0x03, 0x1E, 0x00,   // U+0040 (@)
        0x0C, 0x1E, 0x33, 0x33, 0x3F, 0x33, 0x33, 0x00,   // U+0041 (A)
        0x3F, 0x66, 0x66, 0x3E, 0x66, 0x66, 0x3F, 0x00,   // U+0042 (B)
        0x3C, 0x66, 0x03, 0x03, 0x03, 0x66, 0x3C, 0x00,   // U+0043 (C)
        0x1F, 0x36, 0x66, 0x66, 0x66, 0x36, 0x1F, 0x00,   // U+0044 (D)
        0x7F, 0x46, 0x16, 0x1E, 0x16, 0x46, 0x7F, 0x00,   // U+0045 (E)
        0x7F, 0x46, 0x16, 0x1E, 0x16, 0x06, 0x0F, 0x00,   // U+0046 (F)
        0x3C, 0x66, 0x03, 0x03, 0x73, 0x66, 0x7C, 0x00,   // U+0047 (G)
        0x33, 0x33, 0x33, 0x3F, 0x33, 0x33, 0x33, 0x00,   // U+0048 (H)
        0x1E, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x1E, 0x00,   // U+0049 (I)
        0x78, 0x30, 0x30, 0x30, 0x33, 0x33, 0x1E, 0x00,   // U+004A (J)
        0x67, 0x66, 0x36, 0x1E, 0x36, 0x66, 0x67, 0x00,   // U+004B (K)
        0x0F, 0x06, 0x06, 0x06, 0x46, 0x66, 0x7F, 0x00,   // U+004C (L)
        0x63, 0x77, 0x7F, 0x7F, 0x6B, 0x63, 0x63, 0x00,   // U+004D (M)
        0x63, 0x67, 0x6F, 0x7B, 0x73, 0x63, 0x63, 0x00,   // U+004E (N)
        0x1C, 0x36, 0x63, 0x63, 0x63, 0x36, 0x1C, 0x00,   // U+004F (O)
        0x3F, 0x66, 0x66, 0x3E, 0x06, 0x06, 0x0F, 0x00,   // U+0050 (P)
        0x1E, 0x33, 0x33, 0x33, 0x3B, 0x1E, 0x38, 0x00,   // U+0051 (Q)
        0x3F, 0x66, 0x66, 0x3E, 0x36, 0x66, 0x67, 0x00,   // U+0052 (R)
        0x1E, 0x33, 0x07, 0x0E, 0x38, 0x33, 0x1E, 0x00,   // U+0053 (S)
        0x3F, 0x2D, 0x0C, 0x0C, 0x0C, 0x0C, 0x1E, 0x00,   // U+0054 (T)
        0x33, 0x33, 0x33, 0x33, 0x33, 0x33, 0x3F, 0x00,   // U+0055 (U)
        0x33, 0x33, 0x33, 0x33, 0x33, 0x1E, 0x0C, 0x00,   // U+0056 (V)
        0x63, 0x63, 0x63, 0x6B, 0x7F, 0x77, 0x63, 0x00,   // U+0057 (W)
        0x63, 0x63, 0x36, 0x1C, 0x1C, 0x36, 0x63, 0x00,   // U+0058 (X)
        0x33, 0x33, 0x33, 0x1E, 0x0C, 0x0C, 0x1E, 0x00,   // U+0059 (Y)
        0x7F, 0x63, 0x31, 0x18, 0x4C, 0x66, 0x7F, 0x00,   // U+005A (Z)
        0x1E, 0x06, 0x06, 0x06, 0x06, 0x06, 0x1E, 0x00,   // U+005B ([)
        0x03, 0x06, 0x0C, 0x18, 0x30, 0x60, 0x40, 0x00,   // U+005C (\)
        0x1E, 0x18, 0x18, 0x18, 0x18, 0x18, 0x1E, 0x00,   // U+005D (])
        0x08, 0x1C, 0x36, 0x63, 0x00, 0x00, 0x00, 0x00,   // U+005E (^)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0xFF,   // U+005F (_)
        0x0C, 0x0C, 0x18, 0x00, 0x00, 0x00, 0x00, 0x00,   // U+0060 (`)
        0x00, 0x00, 0x1E, 0x30, 0x3E, 0x33, 0x6E, 0x00,   // U+0061 (a)
        0x07, 0x06, 0x06, 0x3E, 0x66, 0x66, 0x3B, 0x00,   // U+0062 (b)
        0x00, 0x00, 0x1E, 0x33, 0x03, 0x33, 0x1E, 0x00,   // U+0063 (c)
        0x38, 0x30, 0x30, 0x3e, 0x33, 0x33, 0x6E, 0x00,   // U+0064 (d)
        0x00, 0x00, 0x1E, 0x33, 0x3f, 0x03, 0x1E, 0x00,   // U+0065 (e)
        0x1C, 0x36, 0x06, 0x0f, 0x06, 0x06, 0x0F, 0x00,   // U+0066 (f)
        0x00, 0x00, 0x6E, 0x33, 0x33, 0x3E, 0x30, 0x1F,   // U+0067 (g)
        0x07, 0x06, 0x36, 0x6E, 0x66, 0x66, 0x67, 0x00,   // U+0068 (h)
        0x0C, 0x00, 0x0E, 0x0C, 0x0C, 0x0C, 0x1E, 0x00,   // U+0069 (i)
        0x30, 0x00, 0x30, 0x30, 0x30, 0x33, 0x33, 0x1E,   // U+006A (j)
        0x07, 0x06, 0x66, 0x36, 0x1E, 0x36, 0x67, 0x00,   // U+006B (k)
        0x0E, 0x0C, 0x0C, 0x0C, 0x0C, 0x0C, 0x1E, 0x00,   // U+006C (l)
        0x00, 0x00, 0x33, 0x7F, 0x7F, 0x6B, 0x63, 0x00,   // U+006D (m)
        0x00, 0x00, 0x1F, 0x33, 0x33, 0x33, 0x33, 0x00,   // U+006E (n)
        0x00, 0x00, 0x1E, 0x33, 0x33, 0x33, 0x1E, 0x00,   // U+006F (o)
        0x00, 0x00, 0x3B, 0x66, 0x66, 0x3E, 0x06, 0x0F,   // U+0070 (p)
        0x00, 0x00, 0x6E, 0x33, 0x33, 0x3E, 0x30, 0x78,   // U+0071 (q)
        0x00, 0x00, 0x3B, 0x6E, 0x66, 0x06, 0x0F, 0x00,   // U+0072 (r)
        0x00, 0x00, 0x3E, 0x03, 0x1E, 0x30, 0x1F, 0x00,   // U+0073 (s)
        0x08, 0x0C, 0x3E, 0x0C, 0x0C, 0x2C, 0x18, 0x00,   // U+0074 (t)
        0x00, 0x00, 0x33, 0x33, 0x33, 0x33, 0x6E, 0x00,   // U+0075 (u)
        0x00, 0x00, 0x33, 0x33, 0x33, 0x1E, 0x0C, 0x00,   // U+0076 (v)
        0x00, 0x00, 0x63, 0x6B, 0x7F, 0x7F, 0x36, 0x00,   // U+0077 (w)
        0x00, 0x00, 0x63, 0x36, 0x1C, 0x36, 0x63, 0x00,   // U+0078 (x)
        0x00, 0x00, 0x33, 0x33, 0x33, 0x3E, 0x30, 0x1F,   // U+0079 (y)
        0x00, 0x00, 0x3F, 0x19, 0x0C, 0x26, 0x3F, 0x00,   // U+007A (z)
        0x38, 0x0C, 0x0C, 0x07, 0x0C, 0x0C, 0x38, 0x00,   // U+007B ({)
        0x18, 0x18, 0x18, 0x00, 0x18, 0x18, 0x18, 0x00,   // U+007C (|)
        0x07, 0x0C, 0x0C, 0x38, 0x0C, 0x0C, 0x07, 0x00,   // U+007D (})
        0x6E, 0x3B, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00,   // U+007E (~)
        0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00, 0x00    // U+007F
    };
    
    static int[] s_characterStarts;
    static int[] s_characterWidths;

    private static void CheckStats()
    {
        if (s_characterWidths != null) return;
        
        int numChrs = s_font.Length / 8;
        s_characterStarts = new int[numChrs];
        s_characterWidths = new int[numChrs];
        for (int i = 0; i < numChrs; ++i)
        {
            int totalBits = 0;
            for (int j = 0; j < 8; ++j)
                totalBits |= s_font[i * 8 + j];
            int start, end;
            for (start = 0; start < 8; ++start)
                if ((totalBits & (1 << start)) != 0)
                    break;
            for (end = 7; end >= 0; --end)
                if ((totalBits & (1 << end)) != 0)
                    break;
            int width = end - start + 1;
            if (start > end) start = width = 0;
            s_characterStarts[i] = start;
            s_characterWidths[i] = width;
        }
    }

    const int c_nonPrintWidth = 6;
    const int c_spaceWidth = 4;

    public static int DrawCharacter(Color[] _target, int _width, int _height, int _xOrigin, int _yOrigin, char _c, Color _color, float _scale)
    {
        if (_c < 0x20 || _c > 0x7F) return _xOrigin + c_nonPrintWidth;
        if (_c == 0x20) return _xOrigin + c_spaceWidth;
        int glyphIndex = _c - 0x20;
        int glyphDataIndex = glyphIndex * 8;
        CheckStats();
        var start = s_characterStarts[glyphIndex] * _scale;
        var width = s_characterWidths[glyphIndex] * _scale;
        _xOrigin -= (int)start;
        width += start;
        var h = _height;
        int finalPixels = (int)(_scale * 8);
        float invScale = 8.0f / finalPixels;
        for (int y = 0; y < finalPixels; ++y)
        {
            int pixelY = h - 1 - (_yOrigin + y);
            if (pixelY < 0 || pixelY >= _height) continue;
            
            float fy = y * invScale;
            int iy = (int)fy;
            fy -= iy;
            fy -= .5f;
            if (fy < 0)
            {
                fy += 1;
                --iy;
            }
            var iyp1 = iy + 1;
            fy = fy * fy * (3 - fy - fy);
            for (int x = 0; x < finalPixels; ++x)
            {
                int pixelX = _xOrigin + x;
                if (pixelX < 0 || pixelX >= _width) continue;

                float fx = x * invScale;
                int ix = (int) fx;
                fx -= ix;
                fx -= .5f;
                if (fx < 0)
                {
                    fx += 1;
                    --ix;
                }
                fx = fx * fx * (3 - fx - fx);
                var ixp1 = ix + 1;
                var p00 = iy < 0 || iy >= 8 || ix < 0 || ix >= 8 ? 0 : (s_font[glyphDataIndex + iy] >> ix) & 1;
                var p01 = iy < 0 || iy >= 8 || ixp1 < 0 || ixp1 >= 8 ? 0 : (s_font[glyphDataIndex + iy] >> ixp1) & 1;
                var p10 = iyp1 < 0 || iyp1 >= 8 || ix < 0 || ix >= 8 ? 0 : (s_font[glyphDataIndex + iyp1] >> ix) & 1;
                var p11 = iyp1 < 0 || iyp1 >= 8 || ixp1 < 0 || ixp1 >= 8 ? 0 : (s_font[glyphDataIndex + iyp1] >> ixp1) & 1;
                var p0 = Mathf.Lerp(p00, p01, fx);
                var p1 = Mathf.Lerp(p10, p11, fx);
                var p = Mathf.Lerp(p0, p1, fy);
                const float c_threshold = .3f;
                float alpha = Mathf.Min(1, p / c_threshold);
                int index = pixelY * _width + pixelX;
                _target[index] = Color.Lerp(_target[index],_color, alpha);
            }
        }
        return _xOrigin + (int)(width + _scale);
    }

    public static int DrawCharacter(Texture2D _target, int _xOrigin, int _yOrigin, char _c, Color _color, float _scale)
    {
        if (_c < 0x20 || _c > 0x7F) return _xOrigin + (int)(c_nonPrintWidth * _scale);
        if (_c == 0x20) return _xOrigin + (int)(c_spaceWidth * _scale);
        int glyphIndex = _c - 0x20;
        int glyphDataIndex = glyphIndex * 8;
        CheckStats();
        var start = s_characterStarts[glyphIndex] * _scale;
        var width = s_characterWidths[glyphIndex] * _scale;
        _xOrigin -= (int)start;
        width += start;
        var h = _target.height;
        int finalPixels = (int)(_scale * 8);
        float invScale = 8.0f / finalPixels;
        for (int y = 0; y < finalPixels; ++y)
        {
            int pixelY = h - 1 - (_yOrigin + y);
            if (pixelY < 0 || pixelY >= _target.height) continue;
            
            float fy = y * invScale;
            int iy = (int)fy;
            fy -= iy;
            fy -= .5f;
            if (fy < 0)
            {
                fy += 1;
                --iy;
            }
            var iyp1 = iy + 1;
            fy = fy * fy * (3 - fy - fy);
            for (int x = 0; x < finalPixels; ++x)
            {
                int pixelX = _xOrigin + x;
                if (pixelX < 0 || pixelX >= _target.width) continue;

                float fx = x * invScale;
                int ix = (int) fx;
                fx -= ix;
                fx -= .5f;
                if (fx < 0)
                {
                    fx += 1;
                    --ix;
                }
                fx = fx * fx * (3 - fx - fx);
                var ixp1 = ix + 1;
                var p00 = iy < 0 || iy >= 8 || ix < 0 || ix >= 8 ? 0 : (s_font[glyphDataIndex + iy] >> ix) & 1;
                var p01 = iy < 0 || iy >= 8 || ixp1 < 0 || ixp1 >= 8 ? 0 : (s_font[glyphDataIndex + iy] >> ixp1) & 1;
                var p10 = iyp1 < 0 || iyp1 >= 8 || ix < 0 || ix >= 8 ? 0 : (s_font[glyphDataIndex + iyp1] >> ix) & 1;
                var p11 = iyp1 < 0 || iyp1 >= 8 || ixp1 < 0 || ixp1 >= 8 ? 0 : (s_font[glyphDataIndex + iyp1] >> ixp1) & 1;
                var p0 = Mathf.Lerp(p00, p01, fx);
                var p1 = Mathf.Lerp(p10, p11, fx);
                var p = Mathf.Lerp(p0, p1, fy);
                const float c_threshold = .3f;
                float alpha = Mathf.Min(1, p / c_threshold);
                _target.SetPixel(pixelX, pixelY, Color.Lerp(_target.GetPixel(pixelX, pixelY),_color, alpha));
            }
        }
        return _xOrigin + (int)(width + _scale);
    }

    public static int DrawString(Color[] _target, int _width, int _height, int _xOrigin, int _yOrigin, string _s, Color _color, float _scale)
    {
        for (int i = 0; i < _s.Length; ++i)
            _xOrigin = DrawCharacter(_target, _width, _height, _xOrigin, _yOrigin, _s[i], _color, _scale);
        return _xOrigin;
    }

    public static int DrawString(Texture2D _target, int _xOrigin, int _yOrigin, string _s, Color _color, float _scale)
    {
        for (int i = 0; i < _s.Length; ++i)
            _xOrigin = DrawCharacter(_target, _xOrigin, _yOrigin, _s[i], _color, _scale);
        return _xOrigin;
    }
}
