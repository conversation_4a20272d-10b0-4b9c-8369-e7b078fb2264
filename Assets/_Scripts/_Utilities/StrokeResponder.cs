using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class StrokeResponder : MonoBehaviour
{
    public float m_responseTime;
    public float m_repeatTime;
    public string m_responseAudio;
    
    private float m_timeAffected = 0;
    private float m_lastTimeAffected = 0;

    public void Stroke()
    {
        m_lastTimeAffected = Time.time;
        m_timeAffected += Time.deltaTime;
        if (m_timeAffected > m_responseTime)
        {
            if (string.IsNullOrEmpty(m_responseAudio) == false)
                AudioClipManager.Me.PlaySound(m_responseAudio, gameObject);
            if (m_repeatTime > 0)
                m_timeAffected = m_responseTime - m_repeatTime;
        }
    }

    void Update()
    {
        const float c_coolOffTime = .5f;
        if (m_lastTimeAffected - Time.time > c_coolOffTime)
            m_timeAffected = 0;
    }
}
