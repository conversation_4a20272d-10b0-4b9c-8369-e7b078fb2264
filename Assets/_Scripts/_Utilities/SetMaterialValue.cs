using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class SetMaterialValue : MonoBehaviour {
	public string m_variable;
	public float m_value;
	public float m_resetValue;
	void Start() {
		SetValue(true);
	}
	void OnDestroy() {
		SetValue(false);
	}
	void SetValue(bool _set) {
		foreach (var r in GetComponentsInChildren<Renderer>()) {
			MaterialPropertyBlock block = new MaterialPropertyBlock();
			if (r.<PERSON>()) r.GetPropertyBlock(block);
			if (_set)
				block.SetFloat(m_variable, m_value);
			else
				block.SetFloat(m_variable, m_resetValue);
		}
	}
	public SetMaterialValue SetVariable(string _s) { m_variable = _s; return this; }
	public SetMaterialValue SetValue(float _f) { m_value = _f; return this; }
	public SetMaterialValue SetResetValue(float _f) { m_resetValue = _f; return this; }
	public static SetMaterialValue Set(GameObject _o) {
		return _o.AddComponent<SetMaterialValue>();
	}
	public static void Finish(GameObject _o) {
		var smv = _o.GetComponent<SetMaterialValue>();
		if (smv != null) Destroy(smv);
	}
}
