using UnityEngine;
using UnityEngine.EventSystems;

public class InputConsumer : MonoBeh<PERSON>our, IStandardClickHandler, /*ISingleClickHandler, IDoubleClickHandler, */IBeginDragHandler, IDragHandler, IEndDragHandler, IBeginClickHoldHandler, IEndClickHoldHandler {
	
	// Dummy functions to block input
	
	public void OnStandardClick(PointerEventData eventData){}

	public void OnSingleClick(PointerEventData eventData){}
	public void OnDoubleClick(PointerEventData eventData){}

	public void OnBeginDrag(PointerEventData eventData){}
	public void OnDrag(PointerEventData eventData){}
	public void OnEndDrag(PointerEventData eventData){}
	
	public void OnBeginClickHold(PointerEventData eventData){}
	public void OnEndClickHold(PointerEventData eventData){}
}
