using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class IDElt {
	string m_id;
	GameObject m_object;
	public IDElt(string _id) {
		m_id = _id;
	}
	public GameObject Me {
		get {
			if (m_object == null) m_object = IDReg.Find(m_id);
			return m_object;
		}
	}
}
public class IDReg : MonoBehaviour {
	//
	static Dictionary<string, GameObject> s_lookup = new Dictionary<string, GameObject>();
	public static void Register(string _id, GameObject _this) {
		s_lookup[_id.ToLower()] = _this;
	}
	public static void Unregister(string _id) {
		s_lookup.Remove(_id.ToLower());
	}
	public static GameObject Find(string _id) {
		if (s_lookup.TryGetValue(_id.ToLower(), out GameObject found)) return found;
		return null;
	}
	//
	public static IDElt MoneyHUD = new IDElt("MoneyHUD");
	public static IDElt GoldHUD = new IDElt("GoldHUD");
	//
	public string m_id;
	void Awake() { Register(m_id, gameObject); }
	void OnDestroy() { Unregister(m_id); }
}
