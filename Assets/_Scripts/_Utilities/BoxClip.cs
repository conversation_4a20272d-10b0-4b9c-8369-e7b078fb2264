using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BoxClip : MonoBehaviour
{
    public Transform m_root;

    private static Dictionary<Transform, List<BoxClip>> s_lookup = new();

    private static void Register(BoxClip _clip)
    {
        if (!s_lookup.TryGetValue(_clip.m_root, out var list))
        {
            list = new List<BoxClip>();
            s_lookup[_clip.m_root] = list;
        }
        if (list.Count == 0) _clip.SetMaterialStates(true);
        list.Add(_clip);
    }

    private static void Unregister(BoxClip _clip)
    {
        if (s_lookup.TryGetValue(_clip.m_root, out var list))
        {
            list.Remove(_clip);
            if (list.Count == 0) _clip.SetMaterialStates(false);
        }
    }

    private static bool HasRegisteredClips(Transform _root)
    {
        return s_lookup.TryGetValue(_root, out var list) && list.Count > 0;
    }

    void Start()
    {
        Register(this);
        RefreshRenderers(true);
    }

    void OnDisable()
    {
        Unregister(this);
        RefreshRenderers(false);
    }

    void Update()
    {
        RefreshRenderers(true);
    }

    public void SetTarget(bool _on)
    {
        m_fadeTarget = _on ? 1 : 0;
    }

    private GameObject m_onDestroyObject, m_onStartObject;
    public void SetOnDestroy(GameObject _obj)
    {
        m_onDestroyObject = _obj;
    }
    public void SetOnStart(GameObject _obj)
    {
        m_onStartObject = _obj;
        m_onStartObject.SetActive(false);
    }

    private float m_fade = 0;
    private float m_fadeTarget = 1;
    private Vector3 m_pos, m_ext1, m_ext2, m_ext3;
    private bool m_enabled = false;

    void RefreshRenderers(bool _enable)
    {
        const float c_threshSqrd = .001f * .001f;
        var xform = transform;
        const float c_fadeStartTime = .75f, c_fadeEndTime = c_fadeStartTime + .25f;
        var dt = Time.deltaTime * c_fadeEndTime;
        var dFade = Mathf.Clamp(m_fadeTarget - m_fade, -dt, dt);
        m_fade += dFade;
        if (m_onDestroyObject != null && m_fade < c_fadeStartTime && m_fadeTarget < .1f)
        {
            Destroy(m_onDestroyObject);
        }
        else if (m_onStartObject != null && m_fade >= c_fadeStartTime && m_fadeTarget > .1f)
        {
            m_onStartObject.SetActive(true);
        }
        var fade = Mathf.Max(0, (m_fade - c_fadeStartTime) / (c_fadeEndTime - c_fadeStartTime));
        fade = fade * fade * (3 - fade - fade);
        _enable &= fade > .01f;
        
        var pos = xform.position;
        var ext1 = xform.right * (xform.lossyScale.x * fade);
        var ext2 = xform.forward * (xform.lossyScale.z * fade);
        var ext3 = xform.up * (xform.lossyScale.y * fade);
        if (!_enable) pos = ext1 = ext2 = ext3 = Vector3.zero;
        if (m_enabled == _enable && (m_pos - pos).sqrMagnitude < c_threshSqrd &&
            (m_ext1 - ext1).sqrMagnitude < c_threshSqrd &&
            (m_ext2 - ext2).sqrMagnitude < c_threshSqrd &&
            (m_ext3 - ext3).sqrMagnitude < c_threshSqrd)
            return;
        if (m_fadeTarget < 0.5f || m_fade >= c_fadeStartTime) // don't set until we're starting to fade in, always set when fading out
        {
            m_enabled = _enable;
            m_pos = xform.position;
            m_ext1 = xform.right * (xform.lossyScale.x * fade);
            m_ext2 = xform.forward * (xform.lossyScale.z * fade);
            m_ext3 = xform.up * (xform.lossyScale.y * fade);
            
            foreach (var rnd in m_root.GetComponentsInChildren<Renderer>(true))
            {
                foreach (var mat in rnd.materials)
                {
                    if (mat.HasProperty("_BoxCenter"))
                    {
                        mat.SetInt("_Cull", m_enabled ? 0 : 2);
                        mat.SetVector("_BoxCenter", pos);
                        mat.SetVector("_BoxExtent1", ext1);
                        mat.SetVector("_BoxExtent2", ext2);
                        mat.SetVector("_BoxExtent3", ext3);
                    }
                    else if (mat.name.StartsWith("MA_InternalTint") || mat.name.StartsWith("MainSelectedGreen"))
                    {
                        rnd.enabled = !m_enabled;
                    }
                }
            }
        }
    }
    
    void OnDrawGizmos()
    {
        Gizmos.color = Color.red;
        var corners = new Vector3[8];
        var xform = transform;
        var pos = xform.position;
        var ext1 = xform.right * (xform.lossyScale.x * m_fade);
        var ext2 = xform.forward * (xform.lossyScale.z * m_fade);
        var ext3 = xform.up * (xform.lossyScale.y * m_fade);
        corners[0] = pos + ext1 + ext2 + ext3;
        corners[1] = pos + ext1 + ext2 - ext3;
        corners[2] = pos + ext1 - ext2 + ext3;
        corners[3] = pos + ext1 - ext2 - ext3;
        corners[4] = pos - ext1 + ext2 + ext3;
        corners[5] = pos - ext1 + ext2 - ext3;
        corners[6] = pos - ext1 - ext2 + ext3;
        corners[7] = pos - ext1 - ext2 - ext3;
        Gizmos.DrawLine(corners[0], corners[1]);
        Gizmos.DrawLine(corners[1], corners[3]);
        Gizmos.DrawLine(corners[3], corners[2]);
        Gizmos.DrawLine(corners[2], corners[0]);
        Gizmos.DrawLine(corners[4], corners[5]);
        Gizmos.DrawLine(corners[5], corners[7]);
        Gizmos.DrawLine(corners[7], corners[6]);
        Gizmos.DrawLine(corners[6], corners[4]);
        Gizmos.DrawLine(corners[0], corners[4]);
        Gizmos.DrawLine(corners[1], corners[5]);
        Gizmos.DrawLine(corners[2], corners[6]);
        Gizmos.DrawLine(corners[3], corners[7]);
    }

    private void SetMaterialStates(bool _enableClip)
    {
        foreach (var rnd in m_root.GetComponentsInChildren<Renderer>())
        {
            foreach (var mat in rnd.materials)
            {
                if (_enableClip)
                {
                    mat.EnableKeyword("_BOXCLIP");
                    mat.EnableKeyword("_ALPHATEST_ON");
                    mat.SetFloat("_AlphaCutoffEnable", 1.0f);
                    mat.SetFloat("_AlphaClip", 1.0f);
                }
                else
                    mat.DisableKeyword("_BOXCLIP");
            }
        }
    }
}

