using System.Collections;
using System.Collections.Generic;
using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
public class DirectionalRenderingAssetPostprocessor : AssetPostprocessor
{
    void OnPostprocessPrefab(GameObject g)
    {
        var drs = g.GetComponentsInChildren<DirectionalRendering>();
        foreach (var dr in drs)
        {
            dr.PreSave();
        }
    }
}
#endif

[ExecuteAlways]
public class DirectionalRendering : MonoBehaviour
{
    public enum EAxis
    {
        ForwardZ,
        RightX,
        UpY,
        Custom,
    }
    public DirectionBasis[] m_bases; 
    public Transform m_children = null;
    public bool m_forceRefreshRenderers = false;
    
    private Renderer[] m_renderers;

    [System.Serializable]
    public class DirectionBasis
    {
        public Transform m_object;
        public EAxis m_axis;
        public Vector3 m_customAxis;
        public float m_threshold = 0;
        public bool m_reverse = false;
        public Vector3 Axis => m_axis == EAxis.Custom ? Quaternion.Euler(m_customAxis) * m_object.forward : m_axis == EAxis.ForwardZ ? m_object.forward : m_axis == EAxis.RightX ? m_object.right : m_object.up;

        public bool IsVisible(Transform cameraTransform)
        {
            if (m_object == null) return true;
            var axis = Axis;
            var view = (m_object.position - cameraTransform.position).normalized;
            var dot = Vector3.Dot(axis, view) * (m_reverse ? 1 : -1);
            return dot > m_threshold;
        }
    }

    void CheckChildren()
    {
        if (m_renderers != null && !m_forceRefreshRenderers) return;
        m_forceRefreshRenderers = false;
        var previousRenderers = m_renderers;
        var children = m_children == null ? transform : m_children;
        m_renderers = children.GetComponentsInChildren<Renderer>();
#if UNITY_EDITOR
        if (previousRenderers != null)
        {
            var restoredRenderers = new List<Renderer>();
            foreach (var r in previousRenderers)
                if (r != null && !m_renderers.Contains(r) && !restoredRenderers.Contains(r))
                    restoredRenderers.Add(r);
            if (restoredRenderers.Count > 0)
            {
                restoredRenderers.AddRange(m_renderers);
                m_renderers = restoredRenderers.ToArray();
            }
        }
#endif
    }

    void UpdateVisibility(Transform cameraTransform)
    {
        CheckChildren();
        bool isVisible = true;
        if (m_bases != null)
            foreach (var basis in m_bases)
                if (basis != null)
                    isVisible &= basis.IsVisible(cameraTransform);
        foreach (var r in m_renderers)
            if (r.enabled != isVisible)
                r.enabled = isVisible;
    }

    void Update()
    {
        if (Application.isPlaying)
            UpdateVisibility(Camera.main.transform);
    }
    

#if UNITY_EDITOR
    void OnRenderObject()
    {
        if (!Application.isPlaying)
        {
            m_forceRefreshRenderers = true;
            UpdateVisibility(UnityEditor.SceneView.GetAllSceneCameras()[0].transform);
        }
    }

    public void PreSave()
    {
        if (m_renderers != null)
            foreach (var r in m_renderers)
                r.enabled = true;
    }

    void OnDrawGizmosSelected()
    {
        if (m_bases == null) return;
        foreach (var basis in m_bases)
        {
            if (basis.m_object == null) continue;
            var axis = basis.Axis;
            if (basis.m_reverse) axis = -axis;
            var p = basis.m_object.position;
            int index = 1 << (int)basis.m_axis;
            Gizmos.color = new Color(index & 1, (index >> 2) & 1, (index >> 1) & 1);
            Gizmos.DrawSphere(p, 0.1f);
            Gizmos.DrawLine(p, p + axis);
        }
    }
#endif
}
