using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.AI;
using System; 

public class ObjectBase : NGLegacyBase
{
	[Header("Object")]
//	public string m_name; public virtual string Name { get{ return m_name;} set { m_name = value; }}
    [HideInInspector] public NavMeshAgent m_agent; public NavMeshAgent Agent => m_agent; public bool IsAgentActive => m_agent && m_agent.enabled == true;
	[Range(0f,1f)] public float m_power = 0f;
    public float m_radius = 0f;
	public Animator m_animator = null;
	private Vector3 m_lastPosition;
	private float m_currentSpeed;
	public AnimationHandler m_animHandler;
	//Animation Sequence
	
	public bool m_playingAnimation;
	public List<string> m_animationSequence = new List<string>();
	public int m_animationSequenceIndex;
	public float m_animationSequenceWaitTime;float m_animationSequenceWaitTimer;

	public AnimationHandler AnimHandler => m_animHandler;
	public virtual Animator CharacterAnimator => m_animator; 
    public virtual float 	Radius { get { return m_radius;} set { SetRadius(value); }}
    public virtual float 	Power { get { return GetPower(); } set { SetPower(value); }}
    public virtual float    GetPower()  { return m_power;}
    public virtual float GetKeepPower(ObjectBase _from)
    {
        return GetPower(_from.transform.position);
    }
    public virtual float GetAttractPower(ObjectBase _from)
    {
        return GetPower(_from.transform.position);
    }
    public virtual float    GetPower(Vector3 _from) 
    { 
        var distance = Vector3.Distance(transform.position, _from);
        var d = distance/Radius;
        if(distance > Radius) return 0f;
        var ratio = (1f-distance/Radius);
        var modified = (ratio*ratio*(3 - ratio - ratio));
        return modified * Power;
    }
    public virtual void     SetPower(float _value) { m_power = _value; }
    public virtual float    GetRadius()  { return m_radius;}
    public virtual void     SetRadius(float _value) { m_radius = _value; }
    //public virtual Vector3  GetArrivePosition(PeepController _peep = null) { return transform.position; }
	//public virtual Vector3 	GetFollowPosition(ObjectBase _followObject = null) { return GetArrivePosition(); }
    public virtual void     SetGoingTo(ObjectBase _who) { }
    public virtual void     SetLeaving(ObjectBase _who) { }
    public virtual void     FollowerStoppedFollowing(ObjectBase _who) {}
    public virtual float    GetDestinationPower() { return 0f;}
    public virtual bool     StartFollowing(ObjectBase _followObject, int _state) { return false; }
	//public virtual bool 	PeepHeadsFor(PeepController _peep) { return false; }
	//public virtual bool 	PeepArrives(PeepController _peep) { return false; }
	public virtual void 	SetState(string _state) {}
	public virtual void 	CollisionEnter(Collision _collision) {}
	public virtual void 	CollisionExit(Collision _collision) {}
	public virtual void 	CollisionStay(Collision _collision) {}
	public virtual void 	DestroyMe() { Destroy(gameObject); }
	public virtual void 	SetSpeed(float _speed) { Agent.speed = _speed; }
	//public virtual void 	HitByVehicle(VehicleController _vehicle) {}
    protected virtual void Update() 
	{
		UpdateAnim();
	}

	protected virtual void UpdateAnim()
	{
		var currMove = transform.position - m_lastPosition;
		m_currentSpeed = currMove.magnitude / Time.deltaTime;
		m_lastPosition = transform.position; 
		if(CharacterAnimator)
			CharacterAnimator.SetFloat("Speed", m_currentSpeed);
	}

	protected virtual void Awake()
	{
        if(m_agent == null) m_agent = GetComponentInChildren<NavMeshAgent>(true);

	}

	protected virtual void  Start()
	{
		if(m_animHandler == null) m_animHandler = GetComponentInChildren<AnimationHandler>();
		if(m_animator == null) m_animator = GetComponentInChildren<Animator>();
		m_lastPosition = transform.position;
	}

	public AnimationHandler.Handle PlaySingleAnimation(string _animClip, System.Action<bool> _callback = null, bool _re_enableNav = true, float animSpeed = 1.0f )
	{
		if(IsAgentActive && Agent.isOnNavMesh) Agent.isStopped = true;
		m_playingAnimation = true;
		if(_callback == null) _callback = InteractionAnimationEndCallback;
        return null;/*TODO AnimHandler.PlaySingleAnimation (_animClip, (bool _interrupted) =>
		{
			if(_re_enableNav && IsAgentActive && Agent.isOnNavMesh) Agent.isStopped = false;
			if(_callback != null) _callback(_interrupted);
		}, animSpeed );*/
	}
	
	public void StopAnimation(string _name) 
	{
        //    m_animator.Play(DefaultState);
    }
	void InteractionAnimationEndCallback(bool _inturrupted) 
	{
		m_playingAnimation = false;
	}

	public virtual void SetAnimationSequence(List<string> _sequence, bool _randomStart = false, bool _setState = true)
	{
		m_animationSequence = _sequence;
		m_animationSequenceIndex = (_randomStart) ? UnityEngine.Random.Range(0, m_animationSequence.Count) : 0;
	}
	
	enum ANIM_SEQUENCE
	{
		None,
		Loop,
		RandomLoop,
		Wait,
		SetState,
		Stay,
	}
	public enum SEQUENCE_STATE
	{
		PlayingAnim,
		ReachedEnd,
		InTimer,
	}

	protected virtual SEQUENCE_STATE StateSequence()
	{
		if(m_playingAnimation) return SEQUENCE_STATE.PlayingAnim;
		if(m_animationSequenceIndex >= m_animationSequence.Count) return SEQUENCE_STATE.ReachedEnd;
		if(m_animationSequenceWaitTimer.IsZero() == false && Time.time < m_animationSequenceWaitTimer) return SEQUENCE_STATE.InTimer;
		if(m_animationSequenceWaitTime.IsZero() == false)
		{
			m_animationSequenceWaitTimer = Time.time + m_animationSequenceWaitTime;
			m_animationSequenceWaitTime = 0f;
			return SEQUENCE_STATE.InTimer;
		}
		while(true)
		{
			var parts = m_animationSequence[m_animationSequenceIndex++].Split(':');
			ANIM_SEQUENCE command;
//Check For Sequence Command
			if(Enum.TryParse(parts[0], true, out command))
			{
				float skipTo = 0;
				string pram = "";
				if(parts.Length > 1)
				{
					pram = parts[1];
					if(float.TryParse(parts[1], out skipTo) == false)
					{
					}
				} 
				switch(command)
				{
					case ANIM_SEQUENCE.Loop:
						m_animationSequenceIndex = (int)skipTo;
						continue;
					case ANIM_SEQUENCE.RandomLoop:
						m_animationSequenceIndex = UnityEngine.Random.Range((int)skipTo, m_animationSequenceIndex-1);
						continue;
					case ANIM_SEQUENCE.Wait:
						m_animationSequenceWaitTimer = Time.time + skipTo;
						return SEQUENCE_STATE.InTimer;
					case ANIM_SEQUENCE.SetState:
						SetState(pram);
						break;
					case ANIM_SEQUENCE.Stay:
						m_animationSequenceIndex--;
						return SEQUENCE_STATE.InTimer;
				}
			}
			float timerStart = 0f;
//Check for : timer
			if(parts.Length > 1)
			{
				var comma = parts[1].Split(',');
				if(float.TryParse(comma[0], out timerStart))
				{
					if(comma.Length > 1)
					{
						float end = 0f;
						if(float.TryParse(comma[1], out end))
						{
							timerStart = UnityEngine.Random.Range(timerStart, end);
						}
					}  
				}
			}
//Check for State
			m_animationSequenceWaitTime = timerStart;
			if(SequenceState(parts[0])) break;
			if(AnimHandler.IsValidClip(parts[0]) == false) break;
			PlaySingleAnimation(parts[0]);
			break;
		}
		return SEQUENCE_STATE.PlayingAnim;
	}

	protected virtual bool SequenceState(string _state) { return false; }

	public void SetSkinColor(float _gradValue)
	{
		Utility.SetTintWindowColour(gameObject, 1, GlobalData.Me.m_skinColors.Evaluate(_gradValue), true);
	}

	public void SetHairColor(float _gradValue)
	{
		Utility.SetTintWindowColour(gameObject, 2, GlobalData.Me.m_hairColors.Evaluate(_gradValue), true);
	}
	
	public void SetBodyColor(float _hue, float _sat, float _val)
	{
		Color clr = Color.HSVToRGB(_hue, _sat, _val);
		Utility.SetTintWindowColour(gameObject, 0, clr, true);
	}	

	public string RandomCashSound()
	{
		string cashString = "HUDMONEYRECEIVED_0";

		cashString += ((int)UnityEngine.Random.Range(1,5)).ToString();

		return cashString;
	}

}
