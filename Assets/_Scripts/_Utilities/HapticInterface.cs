using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class HapticInterface
{
    public static bool IsEnabled = true;

    private static int s_lastHapticFrame = 0;
    private static int s_minFramesBetweenHaptics = 1;
    private static int s_totalHapticsTriggered = 0;
    private static int s_totalHapticsPlayed = 0;

    private static bool CheckExcess()
    {
        ++s_totalHapticsTriggered;
        if (Time.frameCount < s_lastHapticFrame + s_minFramesBetweenHaptics)
        {
            Debug.Log($"Attempting to fire too many simultaneous haptics");
            return false;
        }
        ++s_totalHapticsPlayed;
        //Debug.Log($"Haptics {s_totalHapticsTriggered} {s_totalHapticsPlayed}");
        s_lastHapticFrame = Time.frameCount;
        return true;
    }

    private static float ChrToFloat(char _c)
    {
        return (_c - '0') * (1.0f / 9.0f);
    }

    private static char FloatToChr(float _f)
    {
        return (char)('0' + (int)(_f * 9));
    }

    public static string HapticToString(float _intensity, float _duration, float _sharpness, int _repeats = 1, float _gap = 0, float _intensityChange = 1)
    {
        string s = "";
        s += FloatToChr(_intensity);
        s += FloatToChr(_sharpness);
        s += (char)('0' + _repeats - 1);
        if (_repeats > 1)
        {
            s += FloatToChr(_gap);
            s += FloatToChr(_intensityChange * 0.5f);
        }
        return s;
    }
    public enum EHapticPreset {
        Tap,
        Drag,
        RestComplete,
        TurnTable,
        ConstructionComplete,
        MakeTap,
        Made,
        Full,
        ChooseProductLine,
        ConfirmDesign,
        Decorate,
        Attach,
        ButtonTap,
        DrawerTap,
        CardTap,
        BezierChange,
        FactoryRumble,
        DTDragPickup,
        Drop,
        SignContract,
        MoveSlider,
        ScrollDrag,
    }
    private static string[] s_hapticPresets = new[]
    {
        "490", // Tap
        "490", // Drag
        "690", // RestComplete
        "290", // TurnTable
        "890", // ConstructionComplete
        "290", // MakeTap
        "690", // Made
        "69209", // Full
        "69604", // ChooseProductLine
        "490", // ConfirmDesign
        "290", // Decorate
        "290", // Attach
        "490", // ButtonTap
        "490", // DrawerTap
        "490", // CardTap
        "290", // BezierChange
        "29904", // FactoryRumble
        "290", // DTDragPickup
        "290", // Drop
        "490", // SignContract
        "490", // MoveSlider
        "290", // ScrollDrag
    };
    
    private static DebugConsole.Command s_sethaptic = new DebugConsole.Command("sethaptic", _s =>
    {
       var bits = _s.Split(":");
       if (bits.Length >= 2)
       {
           var type = System.Enum.Parse<EHapticPreset>(bits[0], true);
           ReplacePreset(type, bits[1]);
       }
    });

    public static void ReplacePreset(EHapticPreset _preset, string _with)
    {
        s_hapticPresets[(int)_preset] = _with;
    }

    public static void PlayHaptic(EHapticPreset _preset, float _intensityMultiplier = 1)
    {
        PlayHaptic(s_hapticPresets[(int)_preset], _intensityMultiplier);
    }
    public static void PlayHaptic(string _id, float _intensityMultiplier = 1)
    {
        float intensity = ChrToFloat(_id[0]) * _intensityMultiplier;
        float sharpness = ChrToFloat(_id[1]);
        int repeats = _id[2] - '0' + 1;
        float gap = 0, intensityChange = 0;
        if (repeats > 1)
        {
            gap = ChrToFloat(_id[3]) * 0.5f + 0.05f;
            intensityChange = ChrToFloat(_id[4]) * 2;
        }
        PlayCustomHaptic(intensity, 0.01f, sharpness, repeats, gap, intensityChange);
    }
    public static void PlayHaptic(float _intensity = 1)
    {
        if (IsEnabled == false) return;
        PlayCustomHaptic(_intensity, 0.01f, 1);
    }

    public static void PlayCustomHaptic(float _intensity, float _duration, float _sharpness, int _repeats = 1, float _gap = 0, float _intensityChange = 1)
    {
        if (IsEnabled == false) return;
        if (_repeats > 1)
        {
            GlobalData.Me.StartCoroutine(Co_PlayRepeating(_intensity, _duration, _sharpness, _repeats, _gap, _intensityChange));
            return;
        }
#if UNITY_IOS || UNITY_ANDROID
        if (CheckExcess())
            CandyCoded.HapticFeedback.HapticFeedback.Feedback((CandyCoded.HapticFeedback.HapticFeedback.EType)(_sharpness * 0.999f * (int)CandyCoded.HapticFeedback.HapticFeedback.EType.Count), _intensity);
#endif
    }

    private static IEnumerator Co_PlayRepeating(float _intensity, float _duration, float _sharpness, int _repeats, float _gap, float _intensityChange)
    {
        for (int i = 0; i < _repeats; ++i)
        {
            PlayCustomHaptic(_intensity, _duration, _sharpness);
            yield return new WaitForSeconds(_gap);
            _intensity = Mathf.Clamp(_intensity * _intensityChange, 0, 1);
        }
    }

    public static void PlayUIHaptic(bool _positive)
    {
        if (IsEnabled == false) return;
        if (_positive)
            PlayCustomHaptic(0.5f, .01f, 1, 2, .1f, 2);
        else
            PlayCustomHaptic(0.75f, .01f, 1, 3, .1f, .6666f);
    }
}
