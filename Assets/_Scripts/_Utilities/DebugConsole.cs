using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public class DebugConsole {
	//==== 

	public class Command {
		public Command(string _cmd, Action<string> _action, string _description = null, string _parameters = null) {
#if UNITY_EDITOR || DEVELOPMENT_BUILD
			s_consoleActions[_cmd.ToLower()] = (_action, _description, _parameters);
#endif
		}
	}

	//==== 
	public static Dictionary<string, (Action<string>, string, string)> s_consoleActions = new ();
	//==== 

	public static DebugConsole Me = null;

	GameObject m_console = null;
	GameObject m_consoleBG = null;
	List<GameObject> m_consoleButtons = new List<GameObject>();
	UnityEngine.UI.InputField m_consoleText = null;
	UnityEngine.UI.Text m_consoleLabel = null;
	Button m_pinButton = null;
	TMPro.TextMeshProUGUI m_pinButtonText = null;

	//====

	Dictionary<KeyCode, (string, string)> m_keyboardShortcuts = new Dictionary<KeyCode, (string, string)>();
	void SetupKeyboardShortcut(string _label, string _command, int _index)
	{
		if (_index < 9)
		{
			var key = (KeyCode)((int)KeyCode.Alpha1 + _index);
			m_keyboardShortcuts[key] = (_label, _command);
		}
	}

	public void CheckKeyboardShortcuts()
	{
		bool ctrl = Input.GetKey(KeyCode.LeftControl) || Input.GetKey(KeyCode.RightControl);
		bool alt = Input.GetKey(KeyCode.LeftAlt) || Input.GetKey(KeyCode.RightAlt);
		bool shift = Input.GetKey(KeyCode.LeftShift) || Input.GetKey(KeyCode.RightShift);
		if (!ctrl || !alt || shift) return;
		foreach (var kvp in m_keyboardShortcuts)
		{
			if (Input.GetKeyDown(kvp.Key))
			{
				Debug.LogError($"Shortcut {kvp.Key} triggered {kvp.Value.Item1} ({kvp.Value.Item2})");
				ExecuteConsole(kvp.Value.Item2, true);
			}
		}
	}

	const int c_consoleButtonH = 48;
	void AddConsoleButton(string _label, string _command, float _y) {
		var buttonGO = new GameObject("Button_" + _label); buttonGO.transform.SetParent(m_console.transform);
		var button = buttonGO.AddComponent<UnityEngine.UI.Button>();
		button.onClick.AddListener(() => {
			ExecuteConsole(_command);
			m_consoleText.ActivateInputField();
		});
		var img = buttonGO.AddComponent<UnityEngine.UI.Image>();
		img.color = new Color(0.1f, 0.2f, 0.5f, .5f);
		button.targetGraphic = img;
		//buttonGO.AddComponent<CanvasRenderer>();
		var rt = buttonGO.GetComponent<RectTransform>();
		rt.anchorMin = Vector2.one; rt.anchorMax = Vector2.one; rt.pivot = Vector2.one; rt.sizeDelta = new Vector2(240, c_consoleButtonH);
		rt.anchoredPosition = new Vector2(0, -_y);
		var buttonLabel = new GameObject("Label"); buttonLabel.transform.SetParent(buttonGO.transform);
		var label = buttonLabel.AddComponent<UnityEngine.UI.Text>();
		label.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
		label.fontSize = 32;
		label.color = Color.white;
		label.text = _label;
		label.alignment = TextAnchor.MiddleCenter;
		label.raycastTarget = false;
		var lrt = buttonLabel.GetComponent<RectTransform>();
		lrt.anchoredPosition = Vector2.zero;
		lrt.sizeDelta = rt.sizeDelta;
		m_consoleButtons.Add(buttonGO);
		buttonGO.SetActive(false);
	}

	public List<string> m_pinnedCommands = new();
	string[] m_buttonCSVs = new string[] { "debugConsoleButtons", "localDebugConsoleButtons" };
	void ReadButtonData() {
		m_pinnedCommands.Clear();
		int y = 64 + 48;
		foreach (var button in m_consoleButtons) GameObject.Destroy(button);
		m_consoleButtons.Clear();
		List<List<string>> entries = null;
		int index = 0;
		foreach (var s in m_buttonCSVs)
		{
			CSVReader csvData;
			if (s.StartsWith("local"))
			{
				var filePath = $"{Application.persistentDataPath}/{s}.csv";
				if (System.IO.File.Exists(filePath))
				{
					var data = System.IO.File.ReadAllText(filePath);
					entries = new();
					CSVReader.LoadFromString(data, entries);
				}
			}
			else
			{
				csvData = new CSVReader(s, CSVReader.direction.column, true);
				if (csvData != null)
				{
					entries = csvData.GetDataAsEntrys();
				}
			}
			if (entries != null)
			{
				foreach (var entry in entries)
				{
					if (entry.Count >= 2 && entry[0].StartsWith("#") == false)
					{
						SetupKeyboardShortcut(entry[0], entry[1], index);
						AddConsoleButton(entry[0], entry[1], y);
						m_pinnedCommands.Add(entry[1]);
						y += c_consoleButtonH + 8;
						++index;
					}
				}
			}
		}
	}

	//====

	public List<string> m_consoleHistory = new List<string>();
	int m_consoleHistoryIndex = 0;
	bool m_hasReadConsoleHistory = false;
	List<string> m_autoCompleteSorted;
	//====
	string AutoCompleteConsole(string _text, string _fullText, bool _backwards) {
		_text = _text.ToLower();
		_fullText = _fullText.ToLower();
		if (m_autoCompleteSorted == null) {
			m_autoCompleteSorted = new List<string>();
			foreach (var kvp in s_consoleActions) m_autoCompleteSorted.Add(kvp.Key.ToLower());
			m_autoCompleteSorted.Sort();
		}
		for (int i = 0; i < m_autoCompleteSorted.Count; i ++) {
			if (m_autoCompleteSorted[i] == _fullText) {
				int d = _backwards ? m_autoCompleteSorted.Count-1 : 1;
				for (int j = 0; j < m_autoCompleteSorted.Count; j ++) {
					i = (i + d) % m_autoCompleteSorted.Count;
					if (m_autoCompleteSorted[i].Contains(_text)) {
						return m_autoCompleteSorted[i];
					}
				}
				return _fullText;
			}
		}
		for (int i = 0; i < m_autoCompleteSorted.Count; i ++) {
			if (m_autoCompleteSorted[i].Contains(_text)) {
				return m_autoCompleteSorted[i];
			}
		}

		return _text;
	}
	public event Action<string> ExecuteCallback;
	public bool ExecuteConsole(string _text, bool _noHistoryAdjust = false) {
		bool rc = false;
#if UNITY_EDITOR || DEVELOPMENT_BUILD
		if (_noHistoryAdjust == false)
		{
			m_consoleHistory.Add(_text);
			for (int i = m_consoleHistory.Count-2; i >= 0; --i) if (m_consoleHistory[i] == _text) m_consoleHistory.RemoveAt(i);
			SaveConsoleHistory();
			m_consoleHistoryIndex = m_consoleHistory.Count;
		}
		var cmds = _text.Split("&&", StringSplitOptions.RemoveEmptyEntries);
		for (int i = 0; i < cmds.Length; ++i)
		{
			var bits = cmds[i].Split('=');

			// for backward compatability, if the parameters start with a $ we should strip $ and ToLower them
			// then we should hunt down and kill anything that requires lower case params 
			if (bits.Length > 1 && bits[1].Length > 0 && bits[1][0] == '$') bits[1] = bits[1].Substring(1).ToLower();

			if (s_consoleActions.TryGetValue(bits[0].Trim().ToLower(), out var exec))
			{
				GameManager.Me.AddDebug($"Console: {_text}");
				ExecuteCallback?.Invoke(_text);
				exec.Item1((bits.Length == 1) ? "" : bits[1].Trim());
				rc = true;
			}
		}
#endif
		return rc;
	}
	public string ConsoleHistory(bool _back) {
		if (_back) {
			if (m_consoleHistoryIndex > 0) m_consoleHistoryIndex--;
			return m_consoleHistory[m_consoleHistoryIndex];
		}
		if (m_consoleHistoryIndex < m_consoleHistory.Count) m_consoleHistoryIndex++;
		if (m_consoleHistoryIndex >= m_consoleHistory.Count) return "";
		return m_consoleHistory[m_consoleHistoryIndex];
	}
	void LoadConsoleHistory() {
		string history = MPlayerPrefs.GetString("ConsoleHistory", "");
		var entries = history.Split('@');
		m_consoleHistory.AddRange(entries);
		m_consoleHistoryIndex = m_consoleHistory.Count;
	}
	void SaveConsoleHistory() {
		string history = "";
		int firstEntry = 0, maxEntries = 50;
		if (m_consoleHistory.Count > maxEntries)
			firstEntry = m_consoleHistory.Count - maxEntries;
		for (int i = firstEntry; i < m_consoleHistory.Count; i ++) {
			if (string.IsNullOrEmpty(history)) {
				history = m_consoleHistory[i];
			} else {
				history += "@" + m_consoleHistory[i];
			}
		}
		MPlayerPrefs.SetString("ConsoleHistory", history);
	}

	public void TogglePinned(string _cmd)
	{
		m_consoleText.text = _cmd;
		TogglePinned();
	}

	void TogglePinned()
	{
		if (string.IsNullOrWhiteSpace(m_consoleText.text) == false)
		{
			List<string> lines;
			string localButtonsFile = $"{Application.persistentDataPath}/localDebugConsoleButtons.csv";
			if (System.IO.File.Exists(localButtonsFile))
				lines = new List<string>(System.IO.File.ReadAllLines(localButtonsFile));
			else
				lines = new List<string>();
			bool found = false;
			var quoted = $"\"{m_consoleText.text}\"";
			for (int i = 0; i < lines.Count; ++i)
			{
				var bits = lines[i].Split(',');
				if (bits.Length < 2) continue;
				if (bits[1] == quoted)
				{
					lines.RemoveAt(i);
					found = true;
					break;
				}
			}
			if (!found)
				lines.Add($"\"{m_consoleText.text}\",\"{m_consoleText.text}\"");
			System.IO.File.WriteAllLines(localButtonsFile, lines.ToArray());
		}
		ReadButtonData();
		s_deactivatedChecks = 0;
		Activate();
	}

	void SetupConsole() {
		if (!m_hasReadConsoleHistory) {
			m_hasReadConsoleHistory = true;
			LoadConsoleHistory();
		}

		if (m_console == null) {			
			m_console = new GameObject("Console");
			UnityEngine.Object.DontDestroyOnLoad(m_console);
			var canvas = m_console.AddComponent<Canvas>();
			canvas.renderMode = RenderMode.ScreenSpaceOverlay;
			canvas.sortingOrder = 1000;
			m_console.AddComponent<UnityEngine.UI.GraphicRaycaster>();

			var scaler = m_console.AddComponent<CanvasScaler>();
			scaler.uiScaleMode = CanvasScaler.ScaleMode.ScaleWithScreenSize;
			scaler.screenMatchMode = CanvasScaler.ScreenMatchMode.MatchWidthOrHeight;
			scaler.matchWidthOrHeight = 1;
			scaler.referenceResolution = new Vector2(1920, 1080);

			bool tapBringToFront = false;
			
			var inputBGGO = new GameObject("ConsoleEntryBG"); inputBGGO.transform.SetParent(m_console.transform);
			var inputBG = inputBGGO.AddComponent<UnityEngine.UI.Image>();
			inputBG.raycastTarget = tapBringToFront;
			var rtBG = inputBGGO.GetComponent<RectTransform>();
			rtBG.anchorMin = Vector2.one; rtBG.anchorMax = Vector2.one; rtBG.pivot = Vector2.one; rtBG.sizeDelta = new Vector2(600, 48);
			rtBG.anchoredPosition = new Vector2(-8-64, -8);
			inputBG.color = new Color(0.4f,0.4f,0.6f, 0.75f);
			m_consoleBG = inputBGGO;

			var labelGO = new GameObject("ConsoleLabel"); labelGO.transform.SetParent(m_console.transform);
			var labelText = labelGO.AddComponent<UnityEngine.UI.Text>();
			labelText.raycastTarget = false;
			labelText.supportRichText = true;
			labelText.alignment = TextAnchor.UpperRight;
			labelText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
			labelText.fontSize = 24;
			labelText.verticalOverflow = VerticalWrapMode.Overflow;
			var rtl = labelText.GetComponent<RectTransform>();
			rtl.anchorMin = Vector2.one; rtl.anchorMax = Vector2.one; rtl.pivot = Vector2.one; rtl.sizeDelta = new Vector2(1200 - 8 * 2, 48);
			rtl.anchoredPosition = new Vector2(-8 - 8 - 64, -8);
			m_consoleLabel = labelText;
			var shadow = labelGO.AddComponent<UnityEngine.UI.Shadow>();
			shadow.effectColor = Color.black;
			shadow.effectDistance = new Vector2(3, -3);

			var inputPin = new GameObject("ConsolePin"); inputPin.transform.SetParent(m_console.transform);
			m_pinButton = inputPin.AddComponent<UnityEngine.UI.Button>();
			m_pinButton.onClick.AddListener(() => TogglePinned());
			var pinImg = inputPin.AddComponent<Image>();
			var rtPin = inputPin.GetComponent<RectTransform>();
			rtPin.anchorMin = Vector2.one; rtPin.anchorMax = Vector2.one; rtPin.pivot = Vector2.one;
			if (GlobalData.Me.m_pinSprite != null)
			{
				rtPin.anchoredPosition = new Vector2(-8 - 8 - 8, -8 - 24);
				rtPin.sizeDelta = new Vector2(48, 48);
				pinImg.sprite = GlobalData.Me.m_pinSprite;
			}
			else
			{
				rtPin.anchoredPosition = new Vector2(-8 - 8 - 64, -8 - 48);
				pinImg.color = new Color(0.1f, 0.2f, 0.5f, 0.5f);
				rtPin.sizeDelta = new Vector2(80, 40);
				var inputPinText = new GameObject("ConsolePinText");
				inputPinText.transform.SetParent(inputPin.transform);
				m_pinButtonText = inputPinText.AddComponent<TMPro.TextMeshProUGUI>();
				m_pinButtonText.text = "Pin";
				m_pinButtonText.horizontalAlignment = TMPro.HorizontalAlignmentOptions.Center;
				m_pinButtonText.verticalAlignment = TMPro.VerticalAlignmentOptions.Middle;
				var rtPinText = inputPinText.GetComponent<RectTransform>();
				rtPinText.anchorMin = Vector2.zero; rtPinText.anchorMax = Vector2.one; rtPinText.pivot = new Vector2(0.5f, 0.5f);
				rtPinText.sizeDelta = Vector2.zero; rtPinText.anchoredPosition = Vector2.zero;
			}
			m_pinButton.gameObject.SetActive(false);
			
			var inputGO = new GameObject("ConsoleEntry"); inputGO.transform.SetParent(m_console.transform);
			var input = inputGO.AddComponent<UnityEngine.UI.InputField>();
			m_consoleText = input;
			var inputTextField = inputGO.AddComponent<UnityEngine.UI.Text>();
			inputTextField.raycastTarget = tapBringToFront;
			inputTextField.supportRichText = false;
			inputTextField.alignment = TextAnchor.MiddleRight;
			inputTextField.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
			inputTextField.fontSize = 36;
			inputTextField.resizeTextForBestFit = true;
			inputTextField.resizeTextMinSize = 20;
			var rt = inputGO.GetComponent<RectTransform>();
			rt.anchorMin = Vector2.one; rt.anchorMax = Vector2.one; rt.pivot = Vector2.one; rt.sizeDelta = new Vector2(600-8*2, 48);
			rt.anchoredPosition = new Vector2(-8-8-64, -8);
			input.lineType = UnityEngine.UI.InputField.LineType.MultiLineNewline;
			input.textComponent = inputTextField;
			input.caretWidth = 5;
			input.characterValidation = UnityEngine.UI.InputField.CharacterValidation.None;
			// GL - 100118 - recent (2017) Unity verions no longer report Enter pressed on iOS in the onEndEdit listener
			// Using the "preferable" check-for-return-pressed-in-validate method which works as long as we're in MultiLineNewline mode
			/*input.onEndEdit.AddListener(delegate {
				bool isPositive = !input.wasCanceled;
				if (!Application.isMobilePlatform) isPositive &= Input.GetKey(KeyCode.KeypadEnter) || Input.GetKey(KeyCode.Return);
				if (isPositive) {
					if (!string.IsNullOrEmpty(input.text)) {						
						if (!ExecuteConsole(input.text))
							input.ActivateInputField();
						input.text = "";
					}
				}
			});*/
			input.onEndEdit.AddListener(_s =>
			{
				UnityEngine.EventSystems.EventSystem.current.SetSelectedGameObject(null);
				s_deactivatedChecks = Time.frameCount + 10;
			});
			input.onValidateInput = (text, charIndex, addedChar) => {
				if (m_showingLabel) return addedChar;
				if (addedChar == 10 || addedChar == 13) {
					if (!string.IsNullOrEmpty(input.text) && !ExecuteConsole(input.text))
						input.ActivateInputField();
					else
						Utility.DoNextFrame(() =>
						{
							input.DeactivateInputField();
							UnityEngine.EventSystems.EventSystem.current.SetSelectedGameObject(null);
						});
					input.text = "";
					return (char)0;
				}
				if (addedChar == (char)9 || addedChar == ';' || addedChar == '?') {
					if (text.Length < m_lastLength) m_lastTyped = text;
					bool shiftHeld = Input.GetKey(KeyCode.LeftAlt) || Input.GetKey(KeyCode.RightAlt);
					m_autoTyping = true;
					input.text = AutoCompleteConsole(m_lastTyped, text, shiftHeld);
					m_autoTyping = false;
					m_lastLength = input.text.Length;
					return (char)0;
				} else if (addedChar == '[' || addedChar == ']') {//} || addedChar == ';' || addedChar == ':') {
					input.text = ConsoleHistory(addedChar == '[' || addedChar == ',');
					return (char)0;
				} else if (addedChar == '!') {
					if (!string.IsNullOrEmpty(input.text)) {						
						if (!ExecuteConsole(input.text))
							input.ActivateInputField();
						input.text = "";
					}
					return (char)0;
				} else if (addedChar == '=') {
					if (input.text.Contains("&&") == false)
					{
						int equalsPos = input.text.IndexOf('=');
						if (equalsPos != -1) input.text = input.text.Substring(0, equalsPos);
						input.caretPosition = input.text.Length;
					}
				}
				if (!m_autoTyping) {
					if ((addedChar >= 'a' && addedChar <= 'z') || (addedChar >= 'A' && addedChar <= 'Z'))
						m_lastTyped = text.Substring(0, charIndex) + addedChar;
					else
						m_lastTyped = text;
				}
				m_lastLength = input.text.Length;

				return addedChar;
			};

			ReadButtonData();
		}
	}
	string m_lastTyped = "";
	int m_lastLength = 0;
	bool m_autoTyping = false;

	public DebugConsole() {
		SetupConsole();
		Me = this;
	}
	public void Activate( bool toggle = false ) {
		//m_toggle = !m_toggle && toggle;
		//if( !m_toggle && toggle ) return;
		
		m_showingLabel = false;
		m_consoleText.ActivateInputField();
		m_consoleBG.SetActive(true);
		foreach (var b in m_consoleButtons) b.SetActive(true);
		m_pinButton.gameObject.SetActive(true);
	}
	IEnumerator Co_EnableButtons(bool _active) {
		yield return new WaitForSecondsRealtime(0.5f);
		foreach (var b in m_consoleButtons) b.SetActive(_active);
		m_pinButton.gameObject.SetActive(_active);
	}
	public bool IsActive() {
		if (m_consoleText == null) return false;
		return m_toggle || m_consoleText.isFocused;
	}
	bool m_showingLabel = false;
	bool wasInactive = false;
	bool m_toggle = false;
	public void ShowLabelIfInactive(string _s) {
		if (m_consoleBG.transform.GetSiblingIndex() != 0)
			m_consoleBG.transform.SetSiblingIndex(0);
		var active = IsActive();
		if (m_consoleBG.activeSelf != active) {
			m_consoleBG.SetActive(active);
			GameManager.Me.StartCoroutine(Co_EnableButtons(active));
		}
		if (!active) {
			m_consoleLabel.enabled = !string.IsNullOrEmpty(_s);
			m_consoleLabel.text = _s;
			wasInactive = true;
		} else if (wasInactive) {
			m_consoleLabel.enabled = false;
			m_consoleText.text = "";
			m_consoleText.textComponent.text = "";
			wasInactive = false;
		}
	}

	private static int s_deactivatedChecks = 0;
	public static bool HasFocus {
		get {
			if (Me == null) return false;
			if (s_deactivatedChecks > Time.frameCount)
			{
				return true;
			}
			return Me.IsActive();
		}
	}
}
