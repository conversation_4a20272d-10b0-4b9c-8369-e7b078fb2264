using UnityEngine;

public class ReferenceHolder : MonoBehaviour
{
    public string m_label;
    public GameObject[] m_references;

    private static GameObject[] s_empty = new GameObject[0];
    public static GameObject[] Get(GameObject _root, string _s)
    {
        var holders = _root.GetComponentsInChildren<ReferenceHolder>();
        foreach (var holder in holders)
            if (holder.m_label == _s)
                return holder.m_references ?? s_empty;
        return s_empty;
    }
}
