using System;
using System.Collections;
using System.Collections.Generic;
using Hairibar.Ragdoll;
using Hairibar.Ragdoll.Animation;
using UnityEditor;
using UnityEngine;

public class RagdollController : MonoBehaviour
{
    public enum RagdollState
    {
        None = 0,
        Animated,
        Responsive,
        Dragged,
        Ragdolled,
        BlendingToAnimated,
        Dead,
#if UNITY_EDITOR
        RagdollTesting
#endif
    }

    [Serializable]
    public class RagdollProfileState
    {
        [SerializeField]
        public RagdollAnimationProfile animationProfile = null;

        [SerializeField]
        public RagdollCollisionProfile collisionProfile = null;

        [SerializeField]
        public RagdollPowerProfile powerProfile = null;
    }

    public class ForceModel
    {
        public Vector3 force = Vector3.zero;
        public ForceMode forceMode = ForceMode.Impulse;
    }

    public class ForceToSingleBoneModel
    {
        public RagdollBone bone = null;
        public ForceModel forceModel = null;
    }

    [SerializeField]
    private RagdollAnimator ragdollAnimator = null;
    
    [SerializeField]
    private AnimationHandler animationHandler = null;

    [SerializeField]
    private Transform boneHipsTarget = null;
    public Transform BoneHipsTarget
    {
        get { return boneHipsTarget; }
    }

    public string getupBackAnimation = "PowerWorkerGetUp";
    public string getupFrontAnimation = "PowerWorkerGetUp";
    
    [SerializeField]
    private List<RagdollState> profileStateKeys = null;
    [SerializeField]
    private List<RagdollProfileState> profileStateValues = null;
    private Dictionary<RagdollState, RagdollProfileState> profileStates = null;
    private Dictionary<RagdollState, RagdollProfileState> ProfileStates
    {
        get
        {
            if (profileStates == null)
            {
                profileStates = new Dictionary<RagdollState, RagdollProfileState>();
                for (int i = 0; i < profileStateKeys.Count; ++i)
                {
                    profileStates.Add(profileStateKeys[i], profileStateValues[i]);
                }
            }

            return profileStates;
        }
    }

    [SerializeField]
    private GameObject ragdollPrefab = null;

    [SerializeField]
    private RagdollDefinitionBindings ragdollDefinitionBindings = null;

    [SerializeField]
    private RagdollSettings ragdollSettings = null;

    [SerializeField]
    private RagdollCollisionIgnorer ragdollCollisionIgnorer = null;
    
    [SerializeField]
    private Transform boneHipsRagdoll = null;
    public Transform BoneHipsRagdoll
    {
        get { return boneHipsRagdoll; }
    }
    
    [SerializeField]
    private Transform boneHeadRagdoll = null;

    public IEnumerable<RagdollBone> RagdollBones
    {
        get { return ragdollDefinitionBindings.Bones; }
    }

    public RagdollBone RootBone
    {
        get { return ragdollDefinitionBindings.Root; }
    }

    private Transform mainRoot = null;
    private Transform MainRoot
    {
        get
        {
            if (mainRoot == null)
            {
                if (Character != null)
                {
                    mainRoot = Character.m_transform;
                }

                if (mainRoot == null)
                {
                    mainRoot = transform.parent;
                }
            }

            return mainRoot;
        }
    }

    private MACharacterBase character = null;
    public MACharacterBase Character
    {
        get
        {
            if (character == null)
            {
                character = GetComponentInParent<MACharacterBase>();
            }
            return character;
        }
    }

    public bool IsAnimated
    {
        get { return currentState == RagdollState.Animated; }
    }
    public bool IsResponsive
    {
        get { return currentState == RagdollState.Responsive; }
    }
    public bool IsRagdolled
    {
        get { return currentState == RagdollState.Ragdolled; }
    }
    public bool IsBlendingToAnimated
    {
        get { return currentState == RagdollState.BlendingToAnimated; }
    }

    public bool WillBeRagdolled
    {
        get { return nextState == RagdollState.Ragdolled; }
    }
    public bool WillChangeState
    {
        get { return nextState != currentState; }
    }

    private Action<bool> ragdollEndedCallback = null;
    private Action m_onSwitchToBlend = null;
    
    public void AddRagDollEndedListener(Action<bool> _onRagDollEnd)
    {
        ragdollEndedCallback += _onRagDollEnd;
    }
    
    public void RemoveRagDollEndedListener(Action<bool> _onRagDollEnd)
    {
        ragdollEndedCallback -= _onRagDollEnd;
    }
    
    public void AddRagDollNotMovingListener(Action _onRagDollNotMoving)
    {
        m_onSwitchToBlend += _onRagDollNotMoving;
    }
    
    public void RemoveRagDollStationaryListener(Action _onRagDollNotMoving)
    {
        m_onSwitchToBlend -= _onRagDollNotMoving;
    }

    private RagdollState currentState = RagdollState.None;
    private RagdollState nextState = RagdollState.Animated;
    private RagdollState previousState = RagdollState.None;

    private ForceModel forceToEveryBone = null;

    private float stateDuration = -1.0f;
    private float stateElapsedTime = 0.0f;

    [SerializeField]
    private bool forceInitJointFixerProperties = false;

    private float airborneVelocityThreshhold = 0.5f;

    private const float c_defaultNotMovingDuration = 0.4f;
    private float notMovingDuration = c_defaultNotMovingDuration;
    private float notMovingElapsedTime = 0.0f;

    private float recoveryPoseDuration = 1.0f;
    private float recoveryPoseElapsedTime = 0.0f;

	// public bool debugAngularConstraints = false;

    private static GameObject ragdollsRoot = null;
    private static GameObject RagdollsRoot
    {
        get
        {
            if (ragdollsRoot == null)
            {
                ragdollsRoot = new GameObject
                {
                    name = "RagdollsRoot"
                };
                var characterHolder = GameObject.Find("CharacterHolder");
                if (characterHolder != null)
                {
                    ragdollsRoot.transform.SetParent(characterHolder.transform);
                }
                ragdollsRoot.transform.position = Vector3.zero;
                ragdollsRoot.transform.rotation = Quaternion.identity;
                ragdollsRoot.transform.localScale = Vector3.one;
            }

            return ragdollsRoot;
        }
    }
    
    public static void EnableRagdollRoot(bool _enable)
    {
        RagdollsRoot.SetActive(_enable);
    }

    private GameObject ragdollGameObject = null;

    private HashSet<string> lowerBoneNames = new HashSet<string>
    {
        "mixamorig:LeftUpLeg",
        "mixamorig:LeftLeg",
        "mixamorig:LeftFoot",
        "mixamorig:RightUpLeg",
        "mixamorig:RightLeg",
        "mixamorig:RightFoot"
    };

    private HeadTracker headTracker = null;

    private GameObject ragdollHipsParent = null;
    private GameObject RagdollHipsParent
    {
        get
        {
            if (ragdollHipsParent == null)
            {
                ragdollHipsParent = new GameObject { name = "Ragdoll" };
                ragdollHipsParent.transform.position = Vector3.zero;
                ragdollHipsParent.transform.rotation = Quaternion.identity;
                ragdollHipsParent.transform.localScale = Vector3.one;
            }

            return ragdollHipsParent;
        }
    }

		Transform[] projectileTargetableBodyparts;
		float [] projectileBodypartsCumulativeProbabilities;
		public Transform GetRandomTargetableBodypart()
		{
			float r = UnityEngine.Random.value;
			for (int i=0; i<projectileTargetableBodyparts.Length; i++)
			{
				if (r <= projectileBodypartsCumulativeProbabilities[i])
				{
					return projectileTargetableBodyparts[i];
				}
			}

			return null;
		}

    private Transform boneHeadTarget = null;
    public Transform BoneHeadTarget
    {
        get
        {
            if (boneHeadTarget == null)
            {
                boneHeadTarget = boneHipsTarget.FindChildRecursiveByName("mixamorig:Head");
            }

            return boneHeadTarget;
        }
    }

    private Transform boneLeftShoulderTarget = null;
    public Transform BoneLeftShoulderTarget
    {
        get
        {
            if (boneLeftShoulderTarget == null)
            {
                boneLeftShoulderTarget = boneHipsTarget.FindChildRecursiveByName("mixamorig:LeftShoulder");
            }

            return boneLeftShoulderTarget;
        }
    }

    private Transform boneRightShoulderTarget = null;
    public Transform BoneRightShoulderTarget
    {
        get
        {
            if (boneRightShoulderTarget == null)
            {
                boneRightShoulderTarget = boneHipsTarget.FindChildRecursiveByName("mixamorig:RightShoulder");
            }

            return boneRightShoulderTarget;
        }
    }

		private Transform boneLeftKneeTarget = null;
    public Transform BoneLeftKneeTarget
    {
        get
        {
            if (boneLeftKneeTarget == null)
            {
                boneLeftKneeTarget = boneHipsTarget.FindChildRecursiveByName("mixamorig:LeftLeg");
            }

            return boneLeftKneeTarget;
        }
    }

		private Transform boneRightKneeTarget = null;
    public Transform BoneRightKneeTarget
    {
        get
        {
            if (boneRightKneeTarget == null)
            {
                boneRightKneeTarget = boneHipsTarget.FindChildRecursiveByName("mixamorig:RightLeg");
            }

            return boneRightKneeTarget;
        }
    }
    
    private bool shouldBreakJoints = false;
    private Dictionary<string, int> detachableBones = new Dictionary<string, int>
    {
        {"mixamorig:LeftUpLeg", 50},
        {"mixamorig:RightUpLeg", 50},
        {"mixamorig:Neck", 20},
        {"mixamorig:LeftArm", 50},
        {"mixamorig:RightArm", 50},

        {"mixamorig:LeftLeg", 30},
        {"mixamorig:RightLeg", 30},
        {"mixamorig:LeftForeArm", 30},
        {"mixamorig:RightForeArm", 30},
    };

    public void OnValidate()
    {
        if (Application.isPlaying) return;

        if (forceInitJointFixerProperties)
        {
            forceInitJointFixerProperties = false;

            ConfigurableJointFixer[] jointFixers = GetComponentsInChildren<ConfigurableJointFixer>(true);
            foreach (ConfigurableJointFixer jointFixer in jointFixers)
            {
                jointFixer.SetupJointFixerProperties();
            }
        }
    }

    public void Start()
    {
        if (Character != null)
        {
            var head = boneHipsTarget.FindChildRecursiveByName("mixamorig:Head");
            headTracker = HeadTracker.Create(Character, head);

						projectileTargetableBodyparts = new Transform[]{ BoneHeadTarget, BoneHipsTarget, BoneLeftShoulderTarget, BoneRightShoulderTarget, BoneLeftKneeTarget, BoneRightKneeTarget };
						var relativeProbabilities = new float[]{ 1, 2, 2, 2, 1, 1 };
						projectileBodypartsCumulativeProbabilities = new float[relativeProbabilities.Length];
						float relativeProbabilitySum = 0;
						for (int i=0; i<relativeProbabilities.Length; i++)
						{
							relativeProbabilitySum += relativeProbabilities[i];
						}

						float cumulativeProb = 0;
						for (int i=0; i<relativeProbabilities.Length; i++)
						{
							cumulativeProb += relativeProbabilities[i]/relativeProbabilitySum;
							projectileBodypartsCumulativeProbabilities[i] = cumulativeProb;
						}
        }
    }

    public void CreateRagdoll()
    {
        if (ragdollGameObject != null)
            return;
        
        RagdollHipsParent.transform.SetParent(RagdollsRoot.transform, true);
        RagdollHipsParent.transform.localScale = transform.parent.parent.lossyScale;

        ragdollGameObject = Instantiate<GameObject>(ragdollPrefab, boneHipsTarget.position, boneHipsTarget.rotation, RagdollHipsParent.transform);
        ragdollGameObject.transform.localScale = ragdollPrefab.transform.localScale;

        ragdollDefinitionBindings = ragdollGameObject.GetComponent<RagdollDefinitionBindings>();
        ragdollSettings = ragdollGameObject.GetComponent<RagdollSettings>();
        ragdollCollisionIgnorer = ragdollGameObject.GetComponent<RagdollCollisionIgnorer>();
        ragdollAnimator.RagdollBindings = ragdollDefinitionBindings;
        ragdollAnimator.RagdollSettings = ragdollSettings;
        ragdollAnimator.StartMe();

        boneHipsRagdoll = ragdollGameObject.transform.GetChild(0);
        var mgor = boneHipsRagdoll.gameObject.AddComponent<MainGameObjectReference>();
        mgor.MainGameObject = gameObject;
        if (Character != null)
        {
            mgor.MainGameObject = Character.gameObject;
        }

        boneHeadRagdoll = boneHipsRagdoll.FindChildRecursiveByName("mixamorig:Head");
    }

    private void DestroyRagdoll()
    {
        if (ragdollGameObject != null)
        {
            Destroy(ragdollHipsParent);
            ragdollHipsParent = null;
            ragdollGameObject = null;
            ragdollDefinitionBindings = null;
            ragdollSettings = null;
            ragdollCollisionIgnorer = null;
            boneHipsRagdoll = null;
            boneHeadRagdoll = null;
            ragdollAnimator.RagdollBindings = null;
            ragdollAnimator.RagdollSettings = null;
        }
    }

    public void FixedUpdate()
    {
        if (currentState == RagdollState.Dead)
            return;
        
        ragdollAnimator.FixedUpdateMe();

        UpdateState();
    }

	public void LateUpdate()
	{
        if (currentState == RagdollState.Dead)
            return;
        
        if (currentState == RagdollState.Responsive)
            MoveRootToHipsPosition(0f);
        else if (currentState == RagdollState.Ragdolled)
            MoveRootToHipsPosition(1f);
        else if (currentState == RagdollState.BlendingToAnimated)
            MoveRootToHipsPosition(0f);

        ragdollAnimator.LateUpdateMe();

        if (headTracker != null)
            headTracker.LateUpdateMe();
	}

    public void OnDestroy()
    {
        DestroyRagdoll();
    }

	private void UpdateState()
    {
        bool hasChanged = false;
        if (currentState != nextState)
        {
            previousState = currentState;
            currentState = nextState;
            hasChanged = true;
        }

        switch (currentState)
        {
            case RagdollState.None:
                break;
            case RagdollState.Animated:
                UpdateAnimatedState(hasChanged);
                break;
            case RagdollState.Responsive:
                UpdateResponsiveState(hasChanged);
                break;
            case RagdollState.Dragged:
                UpdateDraggedState(hasChanged);
                break;
            case RagdollState.Ragdolled:
                UpdateRagdolledState(hasChanged);
                break;
            case RagdollState.BlendingToAnimated:
                UpdateBlendingToAnimatedState(hasChanged);
               break;
#if UNITY_EDITOR
            case RagdollState.RagdollTesting:
                UpdateRagdollTestingState(hasChanged);
                break;
#endif
        }
    }

    private void UpdateAnimatedState(bool hasChanged)
    {
        if (hasChanged)
        {
            if (m_onSwitchToBlend != null)
                m_onSwitchToBlend.Invoke();
            m_onSwitchToBlend = null;
            if (ragdollEndedCallback != null)
                ragdollEndedCallback.Invoke(true);
            ragdollEndedCallback = null;

            DestroyRagdoll();

            SetupRagdollProfiles(RagdollState.Animated);

            ragdollAnimator.SetRagdollBehaviour(true, false);

            recoveryPoseElapsedTime = 0.0f;

            if (Character != null)
                Character.m_bodyToBodyCollider.enabled = true;
        }

        if (recoveryPoseElapsedTime >= recoveryPoseDuration)
        {
            recoveryPoseElapsedTime = 0.0f;
        }
        else
        {
            recoveryPoseElapsedTime += Time.fixedDeltaTime;
        }
    }

    private void UpdateResponsiveState(bool hasChanged)
    {
        if (hasChanged)
        {
            CreateRagdoll();

            SetupRagdollProfiles(RagdollState.Responsive);

            ragdollAnimator.SetRagdollBehaviour(false, false);

            stateElapsedTime = 0.0f;

            if (Character != null)
                Character.m_bodyToBodyCollider.enabled = true;
        }

        ApplyForceToEveryBone();

        if (stateDuration > 0.0f)
        {
            if (stateElapsedTime >= stateDuration)
            {
                stateElapsedTime = 0.0f;
                stateDuration = -1.0f;
                StartAnimatedState();
            }
            else
            {
                stateElapsedTime += Time.fixedDeltaTime;
            }
        }
    }

    private void ApplyForceToEveryBone()
    {
        if (forceToEveryBone == null)
        {
            return;
        }

	    foreach (RagdollBone ragdollBone in RagdollBones)
        {
            float massFactor = ragdollSettings.WeightDistribution.GetBoneMassFactor(ragdollBone.Name);
            var rb = ragdollBone.Rigidbody;
            bool isLowerBone = lowerBoneNames.Contains(rb.gameObject.name);
            float boneFactor = isLowerBone ? 0.6f : 1.4f;
            ApplyForce(rb, forceToEveryBone.force * massFactor * boneFactor, forceToEveryBone.forceMode);
	    }
        forceToEveryBone = null;
    }

    private void ApplyForce(Rigidbody rigidbody, Vector3 force, ForceMode forceMode)
    {
        rigidbody.linearVelocity = Vector3.zero;
        rigidbody.AddForce(force, forceMode);
    }

    public void AdjustRagdollToTargetPose()
    {
        ResetJoints();

        ragdollAnimator.ReadAnimatedPose();
        ragdollAnimator.SnapToTargetPose();
        ragdollAnimator.DoAnimationMatching();
    }

    public static void GizmoTransform(string id, Transform root, Color colour, float radius = 0.05f)
    {
        GameManager.Me.AddGizmoLine(id, root.position, root.parent.position, colour, radius);
        foreach (Transform child in root)
            GizmoTransform(id, child, colour, radius);
    }

    private void UpdateDraggedState(bool hasChanged)
    {
        if (hasChanged)
        {
            CreateRagdoll();

            SetupRagdollProfiles(RagdollState.Dragged);

            ragdollAnimator.SetRagdollBehaviour(false, false);

            if (Character != null)
                Character.m_bodyToBodyCollider.enabled = false;
        }
    }

    public bool PauseNextStateChange
    {
        get;
        set;
    } = false;

    private void UpdateRagdolledState(bool hasChanged)
    {
        if (hasChanged)
        {
            CreateRagdoll();

            SetupRagdollProfiles(RagdollState.Ragdolled);

            ragdollAnimator.SetRagdollBehaviour(false, true);

            notMovingDuration = c_defaultNotMovingDuration;
            notMovingElapsedTime = 0.0f;

            if (Character != null)
                Character.m_bodyToBodyCollider.enabled = false;
        }

        BreakRagdollJoints();

        ApplyForceToEveryBone();

        if (ragdollEndedCallback == null)
        {
            return;
        }
        
        var fallingRate = RootBone.Rigidbody.linearVelocity.y;
        if (Mathf.Abs(fallingRate) < airborneVelocityThreshhold)
        {
            if (notMovingDuration >= 0f)
            {
                if (notMovingElapsedTime >= notMovingDuration)
                {
                    if (PauseNextStateChange == false)
                    {
                        SetNextStateWhenRagdollStationary();
                        notMovingDuration = -1f;
                    }
                    else
                    {
                        notMovingElapsedTime = 0.0f;
                    }
                }
                else
                {
                    notMovingElapsedTime += Time.fixedDeltaTime;
                }
            }
        }
        else
        {
            notMovingElapsedTime = 0.0f;
        }
    }

    private void BreakRagdollJoints()
    {
        if (!shouldBreakJoints)
            return;
        
        shouldBreakJoints = false;

        UnityEngine.Random.InitState(Time.frameCount);
        var root = RootBone.Transform;
        foreach (var bone in RagdollBones)
        {
            var boneName = bone.Name;
            if (detachableBones.ContainsKey(boneName))
            {
                int chance = detachableBones[boneName];
                if (UnityEngine.Random.Range(0, 100) < chance)
                {
                    var joint = bone.Joint;
                    joint.connectedBody = null;
                    joint.xMotion = ConfigurableJointMotion.Free;
                    joint.yMotion = ConfigurableJointMotion.Free;
                    joint.zMotion = ConfigurableJointMotion.Free;
                    joint.angularXMotion = ConfigurableJointMotion.Free;
                    joint.angularYMotion = ConfigurableJointMotion.Free;
                    joint.angularZMotion = ConfigurableJointMotion.Free;
                    bone.Transform.SetParent(root.parent, true);
                }
            }
            
            float massFactor = ragdollSettings.WeightDistribution.GetBoneMassFactor(boneName);
            var dir = bone.Transform.position - root.position;
            ApplyForce(bone.Rigidbody, dir * 10f * 50f * massFactor, ForceMode.Impulse);
        }
    }

    private void MoveRootToHipsPosition(float alpha)
    {
        if (boneHipsRagdoll == null)
            return;
        
        var newPos = Vector3.Lerp(boneHipsTarget.position, boneHipsRagdoll.position, alpha);
        MainRoot.position += (newPos - boneHipsTarget.position).GetXZ();
        var mainRootPos = MainRoot.position;
        float mainRootPosY = mainRootPos.y;
        var groundPos = mainRootPos.GroundPosition();
        if (mainRootPosY < groundPos.y)
            MainRoot.position = groundPos;
        boneHipsRagdoll.position += (newPos - boneHipsRagdoll.position).GetXZ();
	}

    private Coroutine turning = null;
    private void SetNextStateWhenRagdollStationary()
    {
        m_onSwitchToBlend?.Invoke();
        
        var mo = MainRoot?.GetComponent<NGMovingObject>();
        if ((mo != null) && (mo.Health <= 0))
        {
            SetupDeadState();
        }
        else
        {
            //if (turning != null)
            // {
            //     turning = StartCoroutine(TurnDownToGetUp(boneHeadRagdoll.position - boneHipsRagdoll.position,
            //         () =>
            //         {
            //             ChangeState(RagdollState.BlendingToAnimated);
            //             turning = null;
            //         }));
            // }
            ChangeState(RagdollState.BlendingToAnimated);
        }
    }

    private void SetupDeadState()
    {
        if (Character.InState(CharacterStates.WorkerState))
            return;
        
        animationHandler.StopAnimation();

        if (Character != null)
            Character.m_anim.enabled = false;
        
        DestroyRagdoll();

        currentState = RagdollState.Dead;
    }

    public void ForceRagdollToAnimated()
    {
        PauseNextStateChange = false;
        AddRagDollEndedListener((b) => {});
        SetNextStateWhenRagdollStationary();
    }

    public void StartTeleportation(Vector3 pos)
    {
        if (boneHipsRagdoll == null)
            return;
        
        RagdollProfileState ragdollProfileState = ProfileStates[RagdollState.Animated];
        UpdateRagdollPowerProfile(ragdollProfileState.powerProfile);
        boneHipsRagdoll.transform.position = pos;
    }
    
    public void EndTeleportation()
    {
        if (boneHipsRagdoll == null)
            return;
        
        RagdollProfileState ragdollProfileState = ProfileStates[RagdollState.Ragdolled];
        UpdateRagdollPowerProfile(ragdollProfileState.powerProfile);
    }
    
    private void UpdateBlendingToAnimatedState(bool hasChanged)
    {
        if (hasChanged)
        {
            SetupRagdollProfiles(RagdollState.BlendingToAnimated);

            ragdollAnimator.SetRagdollBehaviour(true, false);

            var dir = (boneHeadRagdoll.position - boneHipsRagdoll.position).GetXZ();
			TurnToMatchRagdoll(dir);
			// ragdollAnimator.pushToRagdollFlag = true;
			var onBack = boneHipsRagdoll.forward.y > 0;
	        string getupAnim = onBack ? getupBackAnimation : getupFrontAnimation;
            if (getupAnim != null)
            {
                animationHandler.PlaySingleAnimation(getupAnim, _b => { EndBlending(_b); }, true, 1f);
            }
            else
            {
                EndBlending(false);
            }

            DestroyRagdoll();

            if (Character != null)
                Character.m_bodyToBodyCollider.enabled = true;
        }
    }

	System.Collections.IEnumerator TurnDownToGetUp(Vector3 dir, Action Callback)
    {
        Vector3 fwd = boneHipsRagdoll.forward;
        while (fwd.y > -0.99f)
        {
            fwd = Utility.SlerpAboutAxis(fwd, Vector3.down, dir, 0.2f);
            boneHipsRagdoll.LookAt(boneHipsRagdoll.position + fwd, dir);
            yield return null;
        }

        Callback();
	}

    private void EndBlending(bool wasAnimationInterrupted)
    {
        // You should not reset the callback if you are or will be in Ragdolled state
        // It might happen when something like an explosion hits the character while standing up
        if (IsRagdolled || WillBeRagdolled)
            return;
        
        ragdollEndedCallback?.Invoke(wasAnimationInterrupted);
        ragdollEndedCallback = null;
        m_onSwitchToBlend = null;
        
        if (nextState != RagdollState.Dragged)
            ChangeState(RagdollState.Animated);
    }

    private void TurnToMatchRagdoll(Vector3 forward)
    {        
        var rot = Quaternion.LookRotation(forward, Vector3.up);
        MainRoot.rotation = rot;
        var euler = MainRoot.localEulerAngles;
        euler.x = 0.0f;
        euler.z = 0.0f;
        MainRoot.localEulerAngles = euler;
	}

    private void SetupRagdollProfiles(RagdollState state)
    {
        RagdollProfileState ragdollProfileState = ProfileStates[state];
        UpdateRagdollAnimationProfile(ragdollProfileState.animationProfile);
        if (ragdollGameObject != null)
        {
            UpdateRagdollCollisionProfile(ragdollProfileState.collisionProfile);
            UpdateRagdollPowerProfile(ragdollProfileState.powerProfile);
        }
        ragdollAnimator.changedStateFlag = true;
    }

    private void UpdateRagdollAnimationProfile(RagdollAnimationProfile animationProfile)
    {
        if (ragdollAnimator.Profile != animationProfile)
        {
            ragdollAnimator.Profile = animationProfile;
        }
    }

    private void UpdateRagdollCollisionProfile(RagdollCollisionProfile collisionProfile)
    {
        if (ragdollCollisionIgnorer.CollisionProfile != collisionProfile)
        {
            ragdollCollisionIgnorer.CollisionProfile = collisionProfile;
        }
    }

    private void UpdateRagdollPowerProfile(RagdollPowerProfile powerProfile)
    {
        if (ragdollSettings.PowerProfile != powerProfile)
        {
            ragdollSettings.PowerProfile = powerProfile;
        }
    }

    private void ChangeState(RagdollState state)
    {
        nextState = state;
    }

    public void ResetJoints()
    {
        var jointFixers = GetComponentsInChildren<ConfigurableJointFixer>(true);
        foreach (ConfigurableJointFixer jointFixer in jointFixers)
        {
            jointFixer.DoJointResetRoutine();
        }
    }

    public void ScheduleForceToEveryBone(ForceModel forceModel)
    {
        forceToEveryBone = forceModel;
    }

    public void StartAnimatedState()
    {
        if (IsRagdolled || WillBeRagdolled || IsBlendingToAnimated)
        {
            // string rootName = (MainRoot == null) ? transform.parent.name : MainRoot.name;
            // string condition = IsRagdolled ? "IsRagdolled" : (WillBeRagdolled ? "WillBeRagdolled" : "IsBlendingToAnimated");
            // Debug.LogError($"{rootName} is trying to set Animated mode while {condition}!");

            return;
        }

        ChangeState(RagdollState.Animated);
    }

    public void StartResponsiveState(float duration = -1.0f)
    {
        stateDuration = duration;
        stateElapsedTime = 0.0f;
		ChangeState(RagdollState.Responsive);
    }

    public void StartDraggedState()
    {
        animationHandler.StopAnimation();

        ChangeState(RagdollState.Dragged);
    }
    
    public void StartDeadState()
    {
        if (currentState == RagdollState.Dead)
            return;
        
        if (!IsRagdolled)
        {
            StartRagdolledState((b) => {});
        }
        else
        {
            AddRagDollEndedListener((b) => {});
        }
    }
    
    public void StartRagdolledState(Action<bool> _endCallback = null, Action _onRagdollStationary = null)
    {
        animationHandler.StopAnimation();

        if (_onRagdollStationary != null)
        {
            m_onSwitchToBlend -= _onRagdollStationary;
            m_onSwitchToBlend += _onRagdollStationary;
        }
        if (_endCallback != null)
        {
            ragdollEndedCallback?.Invoke(true);
            ragdollEndedCallback = _endCallback;
        }

        ChangeState(RagdollState.Ragdolled);
    }

    public void CancelRagdoll()
    {
        if (!IsRagdolled && WillBeRagdolled)
        {
            return;
        }

        animationHandler.StopAnimation();

        forceToEveryBone = null;
        ragdollEndedCallback = null;
        m_onSwitchToBlend = null;

        ChangeState(RagdollState.Animated);
    }

    public void ForceDraggedStateForPickup()
    {
        StartDraggedState();
        CreateRagdoll();
    }

    public void PrepareForDraggedState()
    {
        if (currentState == RagdollState.Ragdolled)
        {
            if (turning != null)
            {
                StopCoroutine(turning);
                turning = null;
            }
            EndBlending(true);
        }
        else if (currentState == RagdollState.BlendingToAnimated)
        {
            EndBlending(true);
            animationHandler.StopAnimation(true);
        }
    }

    public void StartBlendingToAnimatedState()
    {
        ChangeState(RagdollState.BlendingToAnimated);
    }

    public void ScheduleBreakJoints()
    {
        shouldBreakJoints = true;
    }

#if UNITY_EDITOR
    public enum RagdollBoneType
    {
        None = 0,
        Hips,
        LeftUpLeg,
        LeftLeg,
        LeftFoot,
        RightUpLeg,
        RightLeg,
        RightFoot,
        Spine,
        Spine1,
        Spine2,
        LeftShoulder,
        LeftArm,
        LeftForeArm,
        LeftHand,
        Neck,
        Head,
        RightShoulder,
        RightArm,
        RightForeArm,
        RightHand
    };

    [Serializable]
    public class RagdollTestingSetup
    {
        public RagdollBoneType boneType = RagdollBoneType.None;

        public float posAlpha = 0f;
        public float posDamping = 1f;
        public float rotAlpha = 0f;
        public float rotDamping = 1f;

        public Vector3 rotationAxis = new Vector3(1f, 0f, 0f);
        public Vector3 rotationSecondaryAxis = new Vector3(0f, 1f, 0f);

        public float lowXLimit = -1f;
        public float highXLimit = 1f;
        public float yLimit = 3f;
        public float zLimit = 3f;

        public bool useGravity = false;

        public bool select = false;

        public RagdollAnimator.RagdollBoneAnimatorOverride rbao = null;
    };

    private List<RagdollTestingSetup> testingSetups = new List<RagdollTestingSetup>();
    private bool refreshSetup = false;

    private RagdollProfileState ragdollTestingProfile = null;
    
    private void UpdateRagdollTestingState(bool hasChanged)
    {
        if (hasChanged)
        {
            CreateRagdoll();

            UpdateRagdollAnimationProfile(ragdollTestingProfile.animationProfile);
            UpdateRagdollCollisionProfile(ragdollTestingProfile.collisionProfile);
            UpdateRagdollPowerProfile(ragdollTestingProfile.powerProfile);
            ragdollAnimator.changedStateFlag = true;
            ragdollAnimator.SetRagdollBehaviour(false, false);

            if (Character != null)
                Character.m_bodyToBodyCollider.enabled = false;
        }

        if (refreshSetup)
        {
            refreshSetup = false;

            StartCoroutine(SetupRagdollTesting());
        }
    }

    private IEnumerator SetupRagdollTesting()
    {
        yield return new WaitForSeconds(0.2f);

        foreach (var bone in RagdollBones)
        {
            if (bone.IsRoot)
                continue;
            
            var setup = testingSetups.Find((rts) =>
            {
                var boneName = "mixamorig:" + Enum.GetName(typeof(RagdollBoneType), rts.boneType);

                return boneName == bone.Name;
            });
            if (setup != null)
            {
                ragdollAnimator.Profile.SetBoneProfile(bone.Name, setup.posAlpha, setup.posDamping, setup.rotAlpha, setup.rotDamping);
                ragdollAnimator.SetRagdollBoneAnimatorOverride(bone.Name, setup.rbao);

                bone.Joint.axis = setup.rotationAxis;
                bone.Joint.secondaryAxis = setup.rotationSecondaryAxis;

                var angularLimit = bone.Joint.lowAngularXLimit;
                angularLimit.limit = setup.lowXLimit;
                bone.Joint.lowAngularXLimit = angularLimit;
                angularLimit = bone.Joint.highAngularXLimit;
                angularLimit.limit = setup.highXLimit;
                bone.Joint.highAngularXLimit = angularLimit;
                angularLimit = bone.Joint.angularYLimit;
                angularLimit.limit = setup.yLimit;
                bone.Joint.angularYLimit = angularLimit;
                angularLimit = bone.Joint.angularZLimit;
                angularLimit.limit = setup.zLimit;
                bone.Joint.angularZLimit = angularLimit;

                bone.Rigidbody.useGravity = setup.useGravity;
            }
            else
            {
                bone.Rigidbody.useGravity = true;
                ragdollAnimator.Profile.RemoveBoneProfile(bone.Name);
                ragdollAnimator.RemoveRagdollBoneAnimatorOverride(bone.Name);
            }
        }

        Debug.LogError("RAGDOLLTESTING - Ragdoll Setups Applied!");
    }

    public void SelectRagdollTestingObjects()
    {
        var selectedObjects = new List<GameObject>();
        foreach (var bone in RagdollBones)
        {
            var setup = testingSetups.Find((rts) =>
            {
                var boneName = "mixamorig:" + Enum.GetName(typeof(RagdollBoneType), rts.boneType);

                return boneName == bone.Name;
            });
            if (setup != null)
            {
                if (setup.select)
                    selectedObjects.Add(bone.Rigidbody.gameObject);
            }
        }
        if (selectedObjects.Count > 0)
        {
            Selection.objects = selectedObjects.ToArray();

            Debug.LogError("RAGDOLLTESTING - Ragdoll Objects Selected!");
        }
        else
        {
            Debug.LogError("RAGDOLLTESTING - No Ragdoll Object to select!");
        }
    }

    public void ApplyRagdollTestingSetups(List<RagdollTestingSetup> setups)
    {
        testingSetups = setups;
        refreshSetup = true;
    }
    
    public void StartRagdollTestingState(List<RagdollTestingSetup> setups, RagdollProfileState testingProfile)
    {
        ragdollTestingProfile = testingProfile;
        ApplyRagdollTestingSetups(setups);

        ChangeState(RagdollState.RagdollTesting);
    }
#endif
}


#if UNITY_EDITOR
[CanEditMultipleObjects]
[CustomEditor(typeof(RagdollController))]
public class RagdollControllerEditor : UnityEditor.Editor
{
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();

		RagdollController rc = (RagdollController)target;

		if (Application.isPlaying)
		{
            if (GUILayout.Button($"Start Animated State"))
            {
                rc.StartAnimatedState();
            }

            if (GUILayout.Button($"Start Responsive State"))
            {
                rc.StartResponsiveState();
            }

            if (GUILayout.Button($"Start Ragdolled State"))
            {
                rc.StartRagdolledState();
            }

            if (GUILayout.Button($"Start BlendingToAnimated State"))
            {
                rc.StartBlendingToAnimatedState();
            }

            if (GUILayout.Button($"Reset Joints"))
            {
                rc.ResetJoints();
            }

            if (GUILayout.Button($"Adjust Ragdoll To Target Pose"))
            {
                rc.AdjustRagdollToTargetPose();
            }

            if (GUILayout.Button($"Break Ragdoll Joints"))
            {
                rc.ScheduleBreakJoints();
            }
		}
	}
}
#endif
