using UnityEngine;

public class LipSync : MonoBehaviour
{
    public float m_deltaRot = -25.0f;

    private bool m_isActive = false;
    private AudioSource m_debugAudioSource = null;
    private Transform m_jaw;
    private float m_closedRot;

    private const float m_minVOValue = -20.0f;
    private const float m_deltaVOValue = 10.0f;

    private const float m_debugMinVOValue = 0.001f;
    private const float m_debugDeltaVOValue = 0.25f;
    private const float m_debugUpdateFrequency = 0.1f;
    private const int m_debugSampleDataLength = 256;
    private float m_debugUpdateTime = 0f;
    private float m_debugVolume;
    private float[] m_debugSampleData = new float[m_debugSampleDataLength];

    public bool IsActive
    {
        get => m_isActive;
        set
        {
            if(value == false)
            {
                SetJawRot(m_closedRot);
            }
            m_isActive = value;
        }
    }

    public AudioSource DebugAudioSource { get => m_debugAudioSource; set => m_debugAudioSource = value; }

    public static LipSync Create(MACharacterBase _character)
	{
		var ls = _character.gameObject.AddComponent<LipSync>();
		ls.Init();
		return ls;
	}

    private void Awake()
    {
        Init();
    }

    private void Init()
	{
        if (m_jaw == null)
        {
            m_jaw = transform.FindChildRecursiveByName("mixamorig:Jaw");

            if (m_jaw != null)
            {
                m_closedRot = m_jaw.localEulerAngles.x;
            }
            else
            {
                Debug.LogWarning($"[LipSync] Jaw bone not found for {transform}");
            }
        }
    }

    private void LateUpdate()
    {
        if(IsActive)
        {
            if(DebugAudioSource != null)
            {
                m_debugUpdateTime += Time.deltaTime;

                if(m_debugUpdateTime >= m_debugUpdateFrequency)
                {
                    UpdateDebugVolume();
                    m_debugUpdateTime = 0.0f;
                }
            }

            float currentVOValue = DebugAudioSource != null ? GetDebugVoiceVolume() : GetNarrativeVOMeter();
            float t = Mathf.Clamp01((currentVOValue - m_minVOValue) / m_deltaVOValue);
            float ts = 1.0f - Mathf.Pow(1.0f - t, 2.5f);
            float currentRot = m_closedRot + m_deltaRot * ts;
            SetJawRot(currentRot);
            //Debug.Log("NarrativeVOMeter: " + currentVOValue);
        }
    }

    private void UpdateDebugVolume()
    {
        //DebugAudioSource.clip.GetData(m_debugSampleData, DebugAudioSource.timeSamples); //KW: causes some errors at the end of sounds
        DebugAudioSource.GetOutputData(m_debugSampleData, 0);
        m_debugVolume = 0f;

        foreach (var sample in m_debugSampleData)
        {
            m_debugVolume += Mathf.Abs(sample);
        }

        m_debugVolume /= m_debugSampleDataLength;

        m_debugVolume = m_minVOValue + Mathf.Clamp01((m_debugVolume - m_debugMinVOValue) / m_debugDeltaVOValue) * m_deltaVOValue;
    }

    private float GetNarrativeVOMeter()
    {
        int valueType = (int)AkQueryRTPCValue.RTPCValue_GameObject;
        uint playingId = AkUnitySoundEngine.AK_INVALID_PLAYING_ID;
        AkUnitySoundEngine.GetRTPCValue("NarrativeVOMeter", gameObject, (uint)playingId, out float outValue, ref valueType);
        return outValue;
    }

    private float GetDebugVoiceVolume()
    {
        return m_debugVolume;
    }

    private void SetJawRot(float _rotX)
    {
        if(m_jaw != null)
        {
            Vector3 rot = m_jaw.localEulerAngles;
            rot.x = _rotX;
            m_jaw.localEulerAngles = rot;
        }
    }
}
