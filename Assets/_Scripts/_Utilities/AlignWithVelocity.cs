using UnityEngine;

public class AlignWithVelocity : MonoBehaviour
{
    public enum EAlignType
    {
        X, Y, Z
    }
    public EAlignType m_alignType = EAlignType.Y;
    private Rigidbody m_body;
    public Quaternion m_offset;
    void Start()
    {
        if (m_body == null)
            m_body = GetComponent<Rigidbody>();
        if (m_body != null)
            m_body.constraints = RigidbodyConstraints.FreezeRotation;
        Orient();
    }

    void Update() => Orient();
    
    public Vector3 up;
    public Vector3 right;
    void Orient()
    {
        if (m_body == null) return;
        var vel = m_body.linearVelocity;
        if (vel.sqrMagnitude < .01f * .01f) return;
        var dir = vel.normalized;
        var up = Vector3.up;
        var right = Vector3.Cross(up, dir);
        up = Vector3.Cross(dir, right);
        
        
        var look = Quaternion.LookRotation(vel);
        transform.rotation = look * m_offset;
        
        /*
        switch (m_alignType)
        {
            case EAlignType.X:
                transform.rotation = Quaternion.LookRotation(up, right);
                break;
            case EAlignType.Y:
                transform.rotation = Quaternion.LookRotation(right, dir);
                break;
            case EAlignType.Z:
                transform.rotation = Quaternion.LookRotation(dir, up);
                break;
        }*/
    }
}
