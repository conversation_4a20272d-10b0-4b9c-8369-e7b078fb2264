using JetBrains.Annotations;
using System;
using System.Reflection;
using System.Runtime.InteropServices;
using System.Collections;
using System.Collections.Generic;
using Unity.Burst;
using Unity.Collections;
using Unity.Collections.LowLevel.Unsafe;
using Unity.Jobs;
using Unity.Mathematics;
using UnityEngine;
using UnityEngine.Rendering.Universal;
using UnityEngine.Serialization;
using UnityEngine.UIElements;

#if UNITY_EDITOR
using UnityEditor;
[CustomEditor(typeof(CameraRenderSettings))]
public class CameraRenderSettingsEditor : Editor
{
	static bool s_showBedrock = false, s_showAudio = false, s_showTrees = false, s_showPresence = false;
	public override void OnInspectorGUI()
	{
		base.OnInspectorGUI();
		var crs = target as CameraRenderSettings;
		var cfg = CameraRenderSettings.SplatConfig;
		if (cfg == null) return;
		bool dirty = false;

		GUILayout.Space(20);
		EditorGUILayout.LabelField("Terrain Details", EditorStyles.boldLabel);
		
		if (crs.m_grassColoursAndPresence == null)
		{
			crs.m_grassColoursAndPresence = new CameraRenderSettings.GrassColoursAndPresence[cfg.sourceTextures.Count];
			dirty = true;
		}
		else if (crs.m_grassColoursAndPresence.Length != cfg.sourceTextures.Count)
		{
			Array.Resize(ref crs.m_grassColoursAndPresence, cfg.sourceTextures.Count);
			dirty = true;
		}
		s_showPresence = EditorGUILayout.Foldout(s_showPresence, "Grass Presence:");
		if (s_showPresence)
		{
			for (int i = 0; i < crs.m_grassColoursAndPresence.Length; ++i)
			{
				var diff = cfg.sourceTextures[i]?.diffuse;
				var diffName = diff == null ? "<none>" : diff.name;

				GUILayout.BeginHorizontal();
				GUILayout.Label($"  {i}: {diffName}", GUILayout.Width(200));
				var clr = crs.m_grassColoursAndPresence[i].m_colour * 10;
				clr = EditorGUILayout.ColorField(new GUIContent(""), clr, true, false, false);
				crs.m_grassColoursAndPresence[i].m_colour = clr * .1f;
				if (GUI.changed) dirty = true;
				GUILayout.EndHorizontal();
				
				GUILayout.BeginHorizontal();
				GUILayout.Label($"     1:", GUILayout.Width(40));
				crs.m_grassColoursAndPresence[i].m_presence.x = EditorGUILayout.FloatField(crs.m_grassColoursAndPresence[i].m_presence.x);
				if (GUI.changed) dirty = true;
				crs.m_grassColoursAndPresence[i].m_presence.y = EditorGUILayout.FloatField(crs.m_grassColoursAndPresence[i].m_presence.y);
				if (GUI.changed) dirty = true;
				crs.m_grassColoursAndPresence[i].m_presence.z = EditorGUILayout.FloatField(crs.m_grassColoursAndPresence[i].m_presence.z);
				if (GUI.changed) dirty = true;
				crs.m_grassColoursAndPresence[i].m_presence.w = EditorGUILayout.FloatField(crs.m_grassColoursAndPresence[i].m_presence.w);
				if (GUI.changed) dirty = true;
				GUILayout.EndHorizontal();

				GUILayout.BeginHorizontal();
				GUILayout.Label($"     2:", GUILayout.Width(40));
				crs.m_grassColoursAndPresence[i].m_presence2.x = EditorGUILayout.FloatField(crs.m_grassColoursAndPresence[i].m_presence2.x);
				if (GUI.changed) dirty = true;
				crs.m_grassColoursAndPresence[i].m_presence2.y = EditorGUILayout.FloatField(crs.m_grassColoursAndPresence[i].m_presence2.y);
				if (GUI.changed) dirty = true;
				crs.m_grassColoursAndPresence[i].m_presence2.z = EditorGUILayout.FloatField(crs.m_grassColoursAndPresence[i].m_presence2.z);
				if (GUI.changed) dirty = true;
				crs.m_grassColoursAndPresence[i].m_presence2.w = EditorGUILayout.FloatField(crs.m_grassColoursAndPresence[i].m_presence2.w);
				if (GUI.changed) dirty = true;
				GUILayout.EndHorizontal();
			}
		}

		if (crs.m_splatIsBedrock == null || crs.m_splatIsBedrock.Length != cfg.sourceTextures.Count)
			crs.m_splatIsBedrock = new bool[cfg.sourceTextures.Count];
		s_showBedrock = EditorGUILayout.Foldout(s_showBedrock, "Splat Is Bedrock:");
		if (s_showBedrock)
		{
			for (int i = 0; i < cfg.sourceTextures.Count; ++i)
			{
				GUILayout.BeginHorizontal();
				var diff = cfg.sourceTextures[i]?.diffuse;
				var diffName = diff == null ? "<none>" : diff.name;
				GUILayout.Label($"  {i}: {diffName}", GUILayout.Width(200));
				crs.m_splatIsBedrock[i] = GUILayout.Toggle(crs.m_splatIsBedrock[i], "");
				if (GUI.changed)
					dirty = true;
				GUILayout.EndHorizontal();
			}
		}
		if (crs.m_splatAudioTypes == null || crs.m_splatAudioTypes.Length != cfg.sourceTextures.Count)
			crs.m_splatAudioTypes = new string[cfg.sourceTextures.Count];
		s_showAudio = EditorGUILayout.Foldout(s_showAudio, "Splat Audio Types:");
		if (s_showAudio)
		{
			for (int i = 0; i < cfg.sourceTextures.Count; ++i)
			{
				GUILayout.BeginHorizontal();
				var diff = cfg.sourceTextures[i]?.diffuse;
				var diffName = diff == null ? "<none>" : diff.name;
				GUILayout.Label($"  {i}: {diffName}", GUILayout.Width(200));
				crs.m_splatAudioTypes[i] = GUILayout.TextField(crs.m_splatAudioTypes[i]);
				if (GUI.changed)
					dirty = true;
				GUILayout.EndHorizontal();
			}
		}
		s_showTrees = EditorGUILayout.Foldout(s_showTrees, "Tree Audio Types:");
		if (s_showTrees)
		{
			if (crs.m_treeAudioNames == null)
			{
				crs.m_treeAudioNames = new string[0];
				crs.m_treeAudioValues = new string[0];
				dirty = true;
			}
			int count = EditorGUILayout.IntField("Count", crs.m_treeAudioNames.Length);
			if (count != crs.m_treeAudioNames.Length)
			{
				Array.Resize(ref crs.m_treeAudioNames, count);
				Array.Resize(ref crs.m_treeAudioValues, count);
				dirty = true;
			}
			for (int i = 0; i < count; ++i)
			{
				GUILayout.BeginHorizontal();
				crs.m_treeAudioNames[i] = EditorGUILayout.TextField(crs.m_treeAudioNames[i]);
				dirty |= GUI.changed;
				crs.m_treeAudioValues[i] = EditorGUILayout.TextField(crs.m_treeAudioValues[i]);
				dirty |= GUI.changed;
				GUILayout.EndHorizontal();
			}
		}
		if (dirty)
		{
			EditorUtility.SetDirty(crs);
			if (Application.isPlaying)
				crs.ApplyRenderSettings();
		}
	}
}
#endif

[ExecuteInEditMode, RequireComponent(typeof(Camera))]
public class CameraRenderSettings : MonoMe<CameraRenderSettings>
{
	[Serializable]
	public struct VisionMode
	{
		public float m_hueShift;
		public float m_saturation;
		public float m_fov;
		public VisionMode(float _hueShift, float _saturation, float _fov)
		{
			m_hueShift = _hueShift;
			m_saturation = _saturation;
			m_fov = _fov;
		}
	}
	
    [Header("Cloud settings")]
	public Texture2D m_CloudTexture;
	public float m_CloudsScrollingSpeed = 0.15f;
	public float m_CloudsTiling = 0.0004f;
	public float m_CloudsIntensityCloseup = 0.2f;
	public float m_CloudsIntensityFarAway = 1.0f;
	public float m_CameraCloseup = 135;
	public float m_CameraFarAway = 50;

	[Header("Terrain")]
	public Texture2D m_TerrainDetailMapSource;
	private Texture2D m_TerrainDetailMapWorking;
	private Texture2D m_TerrainSplatMapWorking;
	public float m_TerrainSandHeight = 99.85f;
	public float m_TerrainRockGrassPinch = 0.75f;
	public bool m_UseDistrictsFilter = true;
	public Vector2 m_NoiseScale = new Vector2(0.05f, 0.05f);

	public VisionMode m_defaultVision = new(0, -1000, 50f);
	public VisionMode m_visionMode;
	
	public Color _VignetteColour = Color.black;
	public float _VignetteTightness = 128;
	
	public float _OwnLandNoiseScale1 = .27f;
	public float _OwnLandNoiseScale2 = 3;
	public float _OwnLandNoiseScale3 = 0;
	public float _OwnLandNoiseWobbleSpeed = 1;
	public float _OwnLandNoiseBoundaryWidth = 1;
	public Color _OwnLandNoiseBoundaryColor = new Color(.5f, 1, .3f, 0);
	public Color _OwnLandBaseColor = new Color(.45f, .35f, .25f, .5f);
	public Color _OwnLandBaseColorNight = new Color(.05f, .05f, .1f, .5f);
	public Vector4 _OwnLandOverride = Vector4.zero;
	private Vector4 _OwnLandOverrideCurrent = Vector4.zero;
	public float _OwnLandHighlightIntensity = 0;
	public float _OwnLandHighlightRadiusMultiplier = 1;
	public float m_ownLandFadeWithCameraAngle = 1;
	public Texture2D _CloudShadowTexture;
	public Vector4 _CloudShadowDetails = new Vector4(0, 0, 3, 0);
	public float _CloudShadowIntensityTarget = .5f;
	public Vector2 _CloudShadowSpeed = new Vector2(0.01f, 0.01f);

	public bool _BlightEnabled = false;
	public Color _BlightBlackeningColour = new Color(.1f, .05f, 0, 0);
	public Vector3 _BlightBlackening = new Vector3(.1f, .9f, 1f);
	[ColorUsageAttribute(false, true, 0f, 32f, 0.125f, 3f)]
	public Color _BlightBrighteningColour = new Color(16, 0, 0);
	public Vector3 _BlightBrightening = new Vector3(.55f, .65f, 1f);
	public Vector2 _BlightHeightFalloff = new Vector2(.6f, 1);

	[Header("Wind settings")]
	public Texture2D m_WindNoiseTexture;
	public float m_WindStrength = 5.0f;
	public float m_WindDirection = -1.0f;

	[Header("LOD Crossfade settings")]
	public Texture2D m_BlueNoiseTexture;
	[Range(0, 0.05f)] public float m_FadeSmoothness = 0.01f;


	[Header("Lighting settings")]
	[ColorUsageAttribute(false,true)]
	public Color m_AmbientColour;
	[ColorUsageAttribute(false,true)]
	public Color m_AmbientColourDown;
	[ColorUsageAttribute(false,true)]
	public Color m_AmbientColourUp;
	[ColorUsageAttribute(false,true)]
	public Color m_AmbientColourShadowed;
	public Vector4 m_MainLightOrigin;
	public Vector4 m_MainLightExtraParameters;
	public Vector3 m_SecondaryLightAngles;
	public Color m_SecondaryLightColour;
	public float m_shadowBlurAmount = 0;
	public float m_shadowsThreshold = 0.7f;
	public float m_shadowsThresholdBlur = 0.47f;
	public Cubemap m_skyboxTexture;
	public Texture2D m_gradientRemap;

	[Header("Disaster - Fire")]
	public Texture2D m_HouseInteriorTex;

	[Header("Fog settings")]
	[Range(0, 1.0f)] public float m_distanceFogAmount = 1.0f;
	[Range(0, 1.0f)] public float m_distanceFogStart = 0.0f;
	[Range(0, 1.0f)] public float m_distanceFogEnd = 0.2f;
	[HideInInspector] public float m_distanceFogAmountReadOnly;
	public Color m_distanceFogColour;

	[Header("Grass Settings")]
	public Texture2D m_GrassTintTexture;
	public Texture2D m_GrassWindNoiseTexture;
    public float m_GrassFadeBlur = 0.01f;
    public float m_GrassColRand = 0.02f;

    [Serializable]
    public class GrassColoursAndPresence
    {
	    public Color m_colour;
	    public Vector4 m_presence;
	    public Vector4 m_presence2;
    }
    [SerializeField, HideInInspector] public GrassColoursAndPresence[] m_grassColoursAndPresence;

    [Header("Sun Settings")]
    public float m_GrassColShift = 0.05f;
    public float m_FoliageColShift = 0.01f;
	public Vector3 m_BounceLightDirection = new Vector3(-0.75f, 1, -1);
	public Color32 m_BounceLightColor = new Color32(1, (byte)0.9, 0, 1);
	public float m_BounceLightIntensity = 0.02f;

	[Header("Miscellaneous")]
	public Color32 m_CsgCutterColour;
    private Vector2 m_CameraNearFarClipPlane;
	public bool m_ShareCameraMatrixWithShaders = false;
	public bool m_OutdoorScene = false;
	public float m_OutlinePulseSpeed = 2.0f;
	public Texture2D m_CurvesTex;
	public float m_DoFAmount = 1.0f;
	[Range(0, 100.0f)] public float m_DoFRadius = 50.0f;
	public UnityEngine.Rendering.Volume m_mainVolume;
	public GameObject m_titleObject3D;

	[Header("Product Effects")]
	public Texture2D m_GlitterTex;
	public float m_GlitterTile;
	public Texture2D m_IridescenceTex;
	public bool m_SetProductEffects;
	public Texture2D m_ProductAgingTex;
	public Texture2D m_DamageTex;
	public float m_DamageTexTiling;

	[Header("Debug")]
	public bool m_enableDebug = false;

	private Camera m_camera;

	void Start()
    {
		m_distanceFogAmountReadOnly = m_distanceFogAmount;
		SetVisionMode(m_defaultVision);
		CreateSplatWorking();
		m_enableDebug = false; // default off
	}

	private static DebugConsole.Command s_blightcmd = new ("blight", (_s) => {
		Utility.SetOrToggle(ref Me._BlightEnabled, _s);
		if (Me._BlightEnabled)
			BlightManager.Me.RestartAll(true);
	});

	override protected void _OnEnable()
	{
		StartCoroutine(Co_ApplyRenderSettings());
	}
	IEnumerator Co_ApplyRenderSettings()
	{
		yield return null; // make sure we're still active next frame, in case of immediate disable such as design table  
		ApplyRenderSettings();
	}

#if UNITY_EDITOR
	void OnValidate()
	{
		ApplyRenderSettings();
	}
#endif

	public float m_shadowCastDistanceMax = 700;
	public float m_shadowCastDistanceFactor = 1.7f;
	private float m_lastMaxDistanceToTerrain = 100;
	private float GetMaxDistanceToTerrain()
	{
		var rayC = m_camera.ScreenPointToRay(new Vector3(Screen.width * .5f, Screen.height * .5f, 0));
		if (Physics.Raycast(rayC, out var hitC, 1e23f, GameManager.c_layerTerrainBit))
		{
			var planeC = new Plane(Vector3.up, hitC.point);
			var rayT = m_camera.ScreenPointToRay(new Vector3(Screen.width * .5f, Screen.height * .95f, 0));
			if (planeC.Raycast(rayT, out var topHit))
				topHit = Mathf.Clamp(topHit, hitC.distance * .65f, hitC.distance);
			else
				topHit = hitC.distance;
			m_lastMaxDistanceToTerrain = topHit * m_shadowCastDistanceFactor;
		}
		return m_lastMaxDistanceToTerrain;
	}
	
	public void SetVisionMode(VisionMode _vision)
	{
		if (GlobalData.Me == null) return; // shutting down
		m_visionMode = _vision;
		Marcos_Procedural_Sky.Me.SetVisionModeValues(_vision.m_saturation, _vision.m_hueShift);
	}

	public void SetCloudShadowIntensity(float _intensity, bool _instant = false)
	{
		_CloudShadowIntensityTarget = _intensity;
		if (_instant)
			_CloudShadowDetails.w = _intensity;
	}

	private void UpdateCloudShadows()
	{
		Shader.SetGlobalTexture("_CloudShadows", _CloudShadowTexture);
		_CloudShadowDetails.x += _CloudShadowSpeed.x * Time.deltaTime;
		_CloudShadowDetails.y += _CloudShadowSpeed.y * Time.deltaTime;
		_CloudShadowDetails.w = Mathf.Lerp(_CloudShadowDetails.w, GameManager.Me.IsInterior ? 0 : _CloudShadowIntensityTarget, .1f);
		if (_CloudShadowTexture == null) _CloudShadowDetails.w = 0;
		Shader.SetGlobalVector("_CloudShadowDetails", _CloudShadowDetails);
	}

	public float m_ssaoDistanceMultiplier = .002f;
	private void UpdateShadowDistance()
	{
		UpdateCloudShadows();
		
		var distanceToGround = GetMaxDistanceToTerrain();

		if (m_ssaoDistanceMultiplier > 0)
		{
			if (GameSettings.SRPOptions.IsURP)
			{
				URPUtility.ssaoRadius = distanceToGround * 2 * m_ssaoDistanceMultiplier;
			}
		}
		
		var shadowDistance = Mathf.Round(Mathf.Min(distanceToGround, m_shadowCastDistanceMax));
		if (GameSettings.SRPOptions.IsURP)
		{
			var asset = GameSettings.SRPOptions.URPAsset;
			asset.shadowDistance = shadowDistance;
		}
		else
		{
			var shadows = GameSettings.SRPOptions.HDShadows;
			shadows.maxShadowDistance.Override(shadowDistance);
		}
	}
	

	public void SetHighlightWorld(Vector3 _pos, float _radius)
	{
		SetHighlight(_pos, _radius * _OwnLandHighlightRadiusMultiplier, _OwnLandHighlightIntensity);
		m_autoFillHighlightIntensity = true;
	}
	public void SetHighlight(Vector3 _pos, float _radius, float _maxFade = 1)
	{
		_OwnLandOverride = new Vector4(_pos.x, _pos.z, _maxFade, _radius * _maxFade);
		if (_OwnLandOverrideCurrent.z < .0001f)
		{
			_OwnLandOverrideCurrent.x = _OwnLandOverride.x;
			_OwnLandOverrideCurrent.y = _OwnLandOverride.y;
		}
	}

	private bool m_autoFillHighlightIntensity = false;

	private void RefreshCurrentHighlight()
	{
		if (m_autoFillHighlightIntensity && _OwnLandHighlightIntensity > 0)
		{
			_OwnLandOverride.w *= _OwnLandHighlightIntensity / _OwnLandOverride.z;
			_OwnLandOverride.z = _OwnLandHighlightIntensity;
		}
	}

	private NGCommanderBase m_currentBuildingHighlight = null;
	public void SetHighlight(NGCommanderBase _building, float _maxFade = 1)
	{
		if (_maxFade < 0)
		{
			_maxFade = _OwnLandHighlightIntensity;
			m_autoFillHighlightIntensity = true;
		}

		var vis = (_building.Visuals ?? _building.transform).gameObject;
		//var bounds = ManagedBlock.GetTotalColliderBounds(vis, "Hotspot");
		var bounds = ManagedBlock.GetTotalVisualBounds(vis, "Hotspot");
		var rad = bounds.extents.xzMagnitude() * _OwnLandHighlightRadiusMultiplier;
		SetHighlight(bounds.center, rad, _maxFade);
		m_currentBuildingHighlight = _building;
	}
	
	public void SetHighlight(MABuilding _building, float _maxFade = 1)
	{
		if (_maxFade < 0)
		{
			_maxFade = _OwnLandHighlightIntensity;
			m_autoFillHighlightIntensity = true;
		}
		var vis = (_building.Visuals ?? _building.transform).gameObject;
		//var bounds = ManagedBlock.GetTotalColliderBounds(vis, "Hotspot");
		var bounds = ManagedBlock.GetTotalVisualBounds(vis, "Hotspot");
		var rad = bounds.extents.xzMagnitude() * _OwnLandHighlightRadiusMultiplier;
		SetHighlight(bounds.center, rad, _maxFade);
		m_currentBuildingHighlight = _building;
	}

	public void ClearHighlight(NGCommanderBase _building)
	{
		if (m_currentBuildingHighlight == _building) ClearHighlight();
	}

	public void ClearHighlight()
	{
		m_autoFillHighlightIntensity = false;
		m_currentBuildingHighlight = null;
		_OwnLandOverride.z = 0; // don't change other parameters, keeps the transition nice
	}

#if UNITY_EDITOR
	public static JBooth.MicroSplat.TextureArrayConfig SplatConfig
	{
		get
		{
			var terrain = Terrain.activeTerrain;
			if (terrain == null) return null;
			var ms = terrain.GetComponent<JBooth.MicroSplat.MicroSplatTerrain>();
			var diffuseArray = ms.templateMaterial.GetTexture("_Diffuse") as Texture2DArray;
			return JBooth.MicroSplat.TextureArrayConfig.FindConfig(diffuseArray);
		}
	}
#endif

	IEnumerator Co_SetLightIntensity(float _intensity)
	{
		if (GameManager.Me == null) yield break;
		var light = GameManager.Me.MainLight;
		for (int i = 0; i < 20; ++i) // let it settle
		{
			light.intensity = _intensity * .95f;
			yield return null;
		}
		light.intensity = _intensity;
	}
	private bool m_wasURP = true;
	public float m_lightIntensityURP = 6;
	public float m_lightIntensityHDRP = 250;
	private void CheckPipeline()
	{
		var terrain = Terrain.activeTerrain;
		if (terrain == null) return;
		var isURP = UnityEngine.Rendering.GraphicsSettings.currentRenderPipeline is UnityEngine.Rendering.Universal.UniversalRenderPipelineAsset;
		if (isURP != m_wasURP)
		{
			m_wasURP = isURP;
			// just switched, make sure things are as they should be
			//StartCoroutine(Co_SetLightIntensity(isURP ? m_lightIntensityURP : m_lightIntensityHDRP));
		}
		if (Shader.IsKeywordEnabled("_HDRP") == isURP)
		{
			if (isURP) Shader.DisableKeyword("_HDRP");
			else Shader.EnableKeyword("_HDRP");
		}

#if UNITY_EDITOR // TODO - fix this for stand-alone builds
		var ms = terrain.GetComponent<JBooth.MicroSplat.MicroSplatTerrain>();
		var keywords = JBooth.MicroSplat.MicroSplatUtilities.FindOrCreateKeywords(ms.templateMaterial);
		const string cKeywordHDRP = "_MSRENDERLOOP_UNITYHDRP2022";
		const string cKeywordURP = "_MSRENDERLOOP_UNITYURP2022";
		var shaderURP = keywords.IsKeywordEnabled(cKeywordURP);
		if (isURP != shaderURP)
		{
			Debug.LogError("Switching microsplat render loop");
			keywords.DisableKeyword(isURP ? cKeywordHDRP : cKeywordURP);
			keywords.EnableKeyword(isURP ? cKeywordURP : cKeywordHDRP);
			var compiler = new MicroSplatShaderGUI.MicroSplatCompiler();
			compiler.Compile(ms.templateMaterial);
		}
#endif
	}
	
	private float m_districtFilterIntensity = 1;
	public void SetDistrictFilterIntensity(float _f) => m_districtFilterIntensity = _f;

	private void Update()
	{
		if (m_camera == null)
			m_camera = GetComponent<Camera>();

		CheckPipeline();

		CheckForcedSplatUpdate();
		
		if (Utility.IsShuttingDown) return;
#if !(COMBAT_TESTING_ENABLED && UNITY_EDITOR)
		UpdateShadowDistance();
#endif
		
		_OwnLandOverrideCurrent = Vector4.Lerp(_OwnLandOverrideCurrent, _OwnLandOverride, .15f);
		float overrideR = Mathf.Max(.001f, _OwnLandOverride.w / (_OwnLandOverrideCurrent.z + .01f));
		_OwnLandOverrideCurrent.w = 1.0f / (overrideR * overrideR);
		Shader.SetGlobalVector("_OwnLandOverride".ShaderId(), _OwnLandOverrideCurrent);
		var ownLandFade = Mathf.InverseLerp(-.3f, -.74f, m_camera.transform.forward.y);
		ownLandFade = Mathf.Lerp(1, ownLandFade, m_ownLandFadeWithCameraAngle);
		if (GameManager.Me.IsPossessing)
			ownLandFade = 0;
		Shader.SetGlobalFloat("_OwnLandFade", ownLandFade * m_districtFilterIntensity);

		Shader.SetGlobalVector("_RTHandleScaleDirect".ShaderId(), UnityEngine.Rendering.RTHandles.rtHandleProperties.rtHandleScale);
		// TODOHDRP
		//Shader.SetGlobalVector("_RTHandleScaleHistoryDirect", UnityEngine.Rendering.HighDefinition.HDCamera.GetOrCreate(GetComponent<Camera>()).historyRTHandleProperties.rtHandleScale);
		var lightHolder = transform?.parent ?? transform;
		foreach (var light in lightHolder.gameObject.GetComponentsInChildren<Light> ())
		{
			if (light.type == LightType.Directional)
			{
				Shader.SetGlobalVector("_WorldSpaceLightPos0".ShaderId(), -light.transform.forward);
				Shader.SetGlobalVector("_LightColor0".ShaderId(), light.color);
			}
		}
		
		if(m_enableDebug)
		{
			ApplyRenderSettings();
		}
		SetShaderDefauls_PerFrameMisc();
	}

	public void SetDoF(bool enable)
	{
		if(enable)
		{
			Shader.SetGlobalFloat("_DoFAmount".ShaderId(), m_DoFAmount);
			Shader.SetGlobalFloat("_DoFRadius".ShaderId(), m_DoFRadius);
		}
		else
		{
			Shader.SetGlobalFloat("_DoFAmount".ShaderId(), 0.0f);
		}
	}

	void SetBlightProperties()
	{
		Shader.SetGlobalColor("_BlightBlackeningColour", _BlightBlackeningColour);
		Shader.SetGlobalVector("_BlightBlackening", _BlightBlackening);
		Shader.SetGlobalColor("_BlightBrighteningColour", _BlightBrighteningColour);
		Shader.SetGlobalVector("_BlightBrightening", _BlightBrightening);
		Shader.SetGlobalVector("_BlightHeightFalloff", new Vector4(_BlightHeightFalloff.x, _BlightHeightFalloff.y, 0, 0));
	}

	//[EditorDebug]
	public void ApplyRenderSettings()
	{
		m_camera = GetComponent<Camera>();
		SetShaderDefaults_Clouds();
		SetShaderDefaults_Wind();
		SetShaderDefaults_LODFade();
		SetShaderDefaults_Lighting();
		//SetShaderDefaults_Fire();
		SetShaderDefaults_Fog();
		SetShaderDefaults_Grass();
		SetShaderDefaults_Miscellaneous();
		SetShaderDefaults_ProductEffects();
		SetShaderDefaults_Terrain();
		
		SetBlightProperties();
		RefreshCurrentHighlight();
		
		CheckDeferredDetailRects();
		
		SetGrassColourAndHeightData();
	}

	void SetShaderDefaults_Fog()
	{
		Shader.SetGlobalFloat("_DistanceFogAmount".ShaderId(), m_distanceFogAmount);
		Shader.SetGlobalFloat("_DistanceFogStart".ShaderId(), m_distanceFogStart);
		Shader.SetGlobalFloat("_DistanceFogEnd".ShaderId(), m_distanceFogEnd);

		
		Color linearColour = m_distanceFogColour.linear;
		Shader.SetGlobalColor("_DistanceFogColour".ShaderId(), linearColour);
	}


	void SetShaderDefaults_Clouds()
	{
		Shader.SetGlobalFloat("_cloudsScrollingSpeed".ShaderId(), m_CloudsScrollingSpeed);
		Shader.SetGlobalFloat("_cloudsTiling".ShaderId(), m_CloudsTiling);
		Shader.SetGlobalFloat("_cloudsIntensity".ShaderId(), m_CloudsIntensityFarAway);
		Shader.SetGlobalFloat("_cloudsIntensityGroundLevel".ShaderId(), m_CloudsIntensityCloseup);
		Shader.SetGlobalFloat("_cloudsHeightLow".ShaderId(), m_CameraCloseup);
		Shader.SetGlobalFloat("_cloudsHeightHigh".ShaderId(), (float)(1.0f/m_CameraFarAway));


    	Shader.SetGlobalTexture("_cloudTex".ShaderId(), m_CloudTexture);
        
    }

	void SetShaderDefaults_Terrain()
	{
		Shader.SetGlobalFloat("_TerrainSandHeight".ShaderId(), m_TerrainSandHeight);
		Shader.SetGlobalFloat("_TerrainRockGrassPinch".ShaderId(), m_TerrainRockGrassPinch);
		Shader.SetGlobalVector("_DistrictsOutlineNoiseST".ShaderId(), new Vector4(m_NoiseScale.x, m_NoiseScale.y, 0, 0));

		Shader.SetGlobalFloat("_OwnLandNoiseScale1".ShaderId(), _OwnLandNoiseScale1);
		Shader.SetGlobalFloat("_OwnLandNoiseScale2".ShaderId(), _OwnLandNoiseScale2);
		Shader.SetGlobalFloat("_OwnLandNoiseScale3".ShaderId(), _OwnLandNoiseScale3);
		Shader.SetGlobalFloat("_OwnLandNoiseWobbleSpeed".ShaderId(), _OwnLandNoiseWobbleSpeed);
		Shader.SetGlobalFloat("_OwnLandNoiseBoundaryWidth".ShaderId(), this._OwnLandNoiseBoundaryWidth);
		Shader.SetGlobalVector("_OwnLandNoiseBoundaryColor".ShaderId(), this._OwnLandNoiseBoundaryColor);
		//Shader.SetGlobalVector("_OwnLandBaseColor".ShaderId(), this._OwnLandBaseColor); // now handled by Marcos_Procedural_Sky

		ApplyTerrainTextures();
	}

	void SetShaderDefaults_Grass()
	{
		//fetch distance fade from terrain population
		float grassFade = TerrainPopulation.Me?.m_maxGrassDistance ?? 100f;
		float grassFadeClip = Remap(grassFade, m_camera.nearClipPlane, m_camera.farClipPlane, 0, 1);
		Shader.SetGlobalFloat("_GrassFadeStart".ShaderId(), grassFade);


		Shader.SetGlobalTexture("_GrassTintTexture".ShaderId(), m_GrassTintTexture);
		Shader.SetGlobalTexture("_GrassWindNoiseTexture".ShaderId(), m_GrassWindNoiseTexture);
		Shader.SetGlobalFloat("_GrassFadeBlur".ShaderId(), m_GrassFadeBlur);
		Shader.SetGlobalFloat("_GrassColRand".ShaderId(), m_GrassColRand);
		Shader.SetGlobalFloat("_GrassColShift".ShaderId(), m_GrassColShift);
		Shader.SetGlobalFloat("_FoliageColShift".ShaderId(), m_FoliageColShift);
		Shader.SetGlobalVector("_BounceLightDirection".ShaderId(), new Vector4(m_BounceLightDirection.x, m_BounceLightDirection.y, m_BounceLightDirection.z, 1));
		Shader.SetGlobalColor("_BounceLightColor".ShaderId(), m_BounceLightColor);
		Shader.SetGlobalFloat("_BounceLightIntensity".ShaderId(), m_BounceLightIntensity);
	}

	void SetShaderDefaults_Miscellaneous()
	{
		
		if (HardwareLevels.RenderDepthNormals || m_enableDebug)
		{
			m_camera.depthTextureMode = DepthTextureMode.DepthNormals;
		}
		else
		{
			m_camera.depthTextureMode = DepthTextureMode.None;
		}

		Shader.SetGlobalColor("_CsgCutterColour".ShaderId(), m_CsgCutterColour);
		m_CameraNearFarClipPlane = new Vector2(m_camera.nearClipPlane, m_camera.farClipPlane);
		Shader.SetGlobalVector("_CameraNearFar".ShaderId(), m_CameraNearFarClipPlane);

		Shader.SetGlobalFloat("_OutlinePulseSpeed".ShaderId(), m_OutlinePulseSpeed);
		ShaderFunctions.SetGlobalShaderTextureIfNotSet(m_CurvesTex, "_CurvesTex");

		SetDoF(true);

		if (m_OutdoorScene)
		{
			Shader.EnableKeyword("_OUTDOOR_SCENE");
		}
		else
		{
			Shader.DisableKeyword("_OUTDOOR_SCENE");
		}

		//SetShader_DistrictFilter();
	}
	void SetShader_DistrictFilter()
	{
		if (DistrictManager.Me != null)
			DistrictManager.Me.ToggleMask(m_UseDistrictsFilter && DesignTableManager.Me.IsInDesignInPlaceActively == false);
		else
		{
			Shader.SetGlobalTexture("_OwnLandMask", Texture2D.whiteTexture);
			Shader.SetGlobalTexture("_OwnLandMaskVS", Texture2D.whiteTexture);
		}
	}

	public void SetUseDistrictFilter(bool _use)
	{
		m_UseDistrictsFilter = _use;
		SetShader_DistrictFilter();
		
	}

	public void SetVignette(Color _colour)
	{
		_VignetteColour = _colour;
	}

	void SetShaderDefauls_PerFrameMisc()
	{
		if (m_ShareCameraMatrixWithShaders || m_enableDebug)
		{
			if(m_camera == null)
			{
				m_camera = GetComponent<Camera>();
			}
			Matrix4x4 cameraMatrix = m_camera.cameraToWorldMatrix;
			Matrix4x4 viewProjectInverseMatrix = (m_camera.projectionMatrix * m_camera.worldToCameraMatrix).inverse;
			Shader.SetGlobalMatrix("_CameraMatrix".ShaderId(), cameraMatrix);

			Vector4 cameraTranslation = new Vector4(cameraMatrix[0,3], cameraMatrix[1,3], cameraMatrix[2,3], cameraMatrix[3,3]);
			Shader.SetGlobalVector("_CameraTranslation".ShaderId(), cameraTranslation);
			Shader.SetGlobalMatrix("_ViewProjectInverse".ShaderId(), viewProjectInverseMatrix);

		}
		Shader.SetGlobalVector("_CameraWorldPosition", m_camera.transform.position);
		Shader.SetGlobalVector("_CameraWorldForward", m_camera.transform.forward);
		
		Shader.SetGlobalVector("_VignetteColour", new Vector4(_VignetteColour.r, _VignetteColour.g, _VignetteColour.b, _VignetteTightness));

		//var lerp = GlobalData.Me.Darkness();
		//lerp = 1 - (1 - lerp) * (1 - lerp);
		//lerp = lerp * lerp * (3.0f - lerp - lerp);
		//Shader.SetGlobalVector("_OwnLandBaseColor".ShaderId(), Color.Lerp(_OwnLandBaseColor, _OwnLandBaseColorNight, lerp));

		SetShader_DistrictFilter();
	}

	void SetShaderDefaults_ProductEffects()
	{
		if(m_SetProductEffects)
		{
			Shader.SetGlobalTexture("_GlitterTex".ShaderId(), m_GlitterTex);
			Shader.SetGlobalFloat("_GlitterTile".ShaderId(), m_GlitterTile);
			Shader.SetGlobalTexture("_IridescenceTex".ShaderId(), m_IridescenceTex);

			Shader.SetGlobalTexture("_ProductAgingTex".ShaderId(), m_ProductAgingTex);
			Shader.SetGlobalFloat("_DamageTexTiling".ShaderId(), m_DamageTexTiling);
			Shader.SetGlobalTexture("_DamageTex".ShaderId(), m_DamageTex);
		}
	}

	void SetShaderDefaults_Wind()
	{
		Shader.SetGlobalFloat("_WindStrength".ShaderId(), m_WindStrength);
		Shader.SetGlobalFloat("_WindDirection".ShaderId(), m_WindDirection);
		Shader.SetGlobalTexture("_WindNoise".ShaderId(), m_WindNoiseTexture);
	}
	
	void SetShaderDefaults_LODFade()
	{
		Shader.SetGlobalFloat("_FadeSmoothness".ShaderId(), m_FadeSmoothness);
		Shader.SetGlobalTexture("_BlueNoiseCrossfade".ShaderId(), m_BlueNoiseTexture);
	}

	void SetShaderDefaults_Lighting()
	{
		Shader.SetGlobalColor("_AmbientColour".ShaderId(), m_AmbientColour);
		Shader.SetGlobalColor("_AmbientColourDown".ShaderId(), m_AmbientColourDown);
		Shader.SetGlobalColor("_AmbientColourUp".ShaderId(), m_AmbientColourUp);
		Shader.SetGlobalColor("_AmbientColourShadowed".ShaderId(), m_AmbientColourShadowed);
		Shader.SetGlobalVector("_MainLightOrigin".ShaderId(), m_MainLightOrigin);
		Shader.SetGlobalVector("_MainLightExtraParameters".ShaderId(), m_MainLightExtraParameters);
		Shader.SetGlobalVector("_SecondaryLightDirection".ShaderId(), Quaternion.Euler(m_SecondaryLightAngles) * Vector3.forward);
		Shader.SetGlobalColor("_SecondaryLightColour".ShaderId(), m_SecondaryLightColour);
		Shader.SetGlobalFloat("_ShadowProjectionTexture_Blurred_Amount".ShaderId(), m_shadowBlurAmount);
		Shader.SetGlobalFloat("_ShadowsThreshold".ShaderId(), m_shadowsThreshold);
		Shader.SetGlobalFloat("_ShadowsThreshold_Blur".ShaderId(), m_shadowsThresholdBlur);
		
		Shader.SetGlobalTexture("_SkyboxCubemap".ShaderId(), m_skyboxTexture);
		Shader.SetGlobalTexture("_GradientMap".ShaderId(), m_gradientRemap);

		if(HardwareLevels.ShadowDistanceMod > 0.1)
		{
			Shader.EnableKeyword("_DYNAMIC_SHADOWS_ENABLED");
		}
		else
		{
			Shader.DisableKeyword("_DYNAMIC_SHADOWS_ENABLED");
		}

		if (HardwareLevels.UseBlobShadows)
		{
			m_camera.LayerCullingShow("BlobShadows");
		}
		else
		{
			m_camera.LayerCullingHide("BlobShadows");
		}

		if(m_enableDebug)
		{
			_CheckIfLightsCastShadows();
		}

	}
	
	public static int c_SplatChannels => Me.m_texData.Length * 4;
	private Vector3 m_splatOpMin, m_splatOpMax;
	private int m_splatOpMinX, m_splatOpMinZ, m_splatOpMaxX, m_splatOpMaxZ;
	private Texture2D[] m_textures;
	private NativeArray<NativeArray<byte>> m_texData;
	private Texture2D[] m_backupTextures;
	private NativeArray<NativeArray<byte>> m_backupTexData;
	private int m_splatOpDeferCount = 0;

	[SerializeField, HideInInspector] public bool[] m_splatIsBedrock;
	[SerializeField, HideInInspector] public string[] m_splatAudioTypes;
	public Dictionary<string, string> m_treeAudioTypes;
	[SerializeField, HideInInspector] public string[] m_treeAudioNames;
	[SerializeField, HideInInspector] public string[] m_treeAudioValues;
	private TextAsset m_bedrockAsset;
	private NativeArray<byte> m_bedrockData;

	void PrepareTreeAudio()
	{
		if (m_treeAudioTypes != null && m_treeAudioTypes.Count == m_treeAudioNames.Length) return;
		m_treeAudioTypes = new();
		for (int i = 0; i < m_treeAudioNames.Length; ++i)
			m_treeAudioTypes[m_treeAudioNames[i]] = m_treeAudioValues[i];
	}

	public string TreeAudioFromTreeName(string _name)
	{
		PrepareTreeAudio();
		if (m_treeAudioTypes.TryGetValue(_name, out var type)) return type;
		return null;
	}

	Color32[] m_audioColours =
	{
		new (0, 0, 255, 255),
		new (0, 255, 0, 255),
		new (192, 128, 0, 255),
		new (128, 128, 128, 255),
		new (255, 255, 0, 255),
	};
	string[] m_audioTypes =
	{
		"Sea",
		"Grass",
		"Dirt",
		"Stone",
		"Mud",
	};
	
	byte[] m_audioData;

	public string GetAudioDataFromPosition(Vector3 _pos)
	{
		if (m_audioData == null) return ""; // not in town
		var x = GlobalData.TerrainXclamped(_pos.x) / 4;
		var z = GlobalData.TerrainXclamped(_pos.z) / 4;
		int value = m_audioData[x + z * 1024];
		return m_audioTypes[value];
	}

	public static int Color32Cmp(Color32 _a, Color32 _b)
	{
		var dr = _a.r - _b.r;
		var dg = _a.g - _b.g;
		var db = _a.b - _b.b;
		return dr * dr + dg * dg + db * db;
	}

	Dictionary<Color32, int> m_audioDataLookup = new();

	int LookupAudioDataRaw(Color32 c)
	{
		if (m_audioDataLookup.TryGetValue(c, out var value)) return value;
		int bestDiff = 0x7FFFFFFF;
		int bestIndex = -1;
		for (int j = 0; j < m_audioColours.Length; ++j)
		{
			var cmp = m_audioColours[j];
			int diff = Color32Cmp(cmp, c);
			if (diff < bestDiff)
			{
				bestDiff = diff;
				bestIndex = j;
			}
		}
		if (bestIndex != -1)
		{
			m_audioDataLookup[c] = bestIndex;
			return bestIndex;
		}
		Debug.LogError($"Unknown audio colour {c.r} {c.g} {c.b}");
		m_audioDataLookup[c] = 0;
		return 0;
	}    

	const int c_coastMapDownshift = 2;
	const int c_coastMapDownscale = 1 << c_coastMapDownshift;
	private byte[] m_coastData;
	private int m_coastDataStride;

	public float LookupCoastDistance3D(Vector3 _pos)
	{
		var distance2D = LookupCoastDistance(_pos);
		var distanceY = Mathf.Max(0, _pos.y - GlobalData.c_seaLevel);
		return Mathf.Sqrt(distance2D * distance2D + distanceY * distanceY);
	}

	public float LookupCoastDistance(Vector3 _pos)
	{
		if (m_coastData == null)
		{
			m_coastData = Resources.Load<TextAsset>("coastmap").bytes;
			m_coastDataStride = (int)Mathf.Sqrt(m_coastData.Length);
		}
		var x = GlobalData.TerrainXclamped(_pos.x) >> c_coastMapDownshift;
		var z = GlobalData.TerrainZclamped(_pos.z) >> c_coastMapDownshift;
		int index = x + z * m_coastDataStride;
		return m_coastData[index];
	}
	
	private static DebugConsole.Command s_dumpCoastMapCmd = new ("dumpcoastmap", _s => {
		var coastData = Me.m_coastData;
		var clrs32 = new Color32[coastData.Length];
		var camPos = Camera.main.transform.position;
		var camX = GlobalData.TerrainXclamped(camPos.x) >> c_coastMapDownshift;
		var camZ = GlobalData.TerrainZclamped(camPos.z) >> c_coastMapDownshift;
		var camIndex = camX + camZ * Me.m_coastDataStride;
		for (int i = 0; i < coastData.Length; ++i)
		{
			byte c = coastData[i];
			//c = (byte)(((c + 1) & 255) * 255 / 128);
			var g = (byte)(i == camIndex ? 255 : 0);//(byte)((debugMap[i] & 1) * 32);
			var b = (byte)(c <= 1 ? 255 : 0);//(byte)(((debugMap[i] >> 1)& 1) * 32);
			clrs32[i] = new Color32(c, g, b, 255);
		}
		var tex = new Texture2D(Me.m_coastDataStride, Me.m_coastDataStride, TextureFormat.RGBA32, false);
		tex.SetPixels32(clrs32);
		tex.Apply();
		var bytes = tex.EncodeToPNG();
		System.IO.File.WriteAllBytes("coastmap.png", bytes);
	});

	void GenerateBedrockData(bool _generate = true)
	{
		if (m_audioData == null)
		{
			var audioMap = Resources.Load<Texture2D>("audiomap");
			var audioPixels = audioMap.GetPixels32();
			m_audioData = new byte[audioPixels.Length];
			for (int i = 0; i < audioPixels.Length; ++i)
				m_audioData[i] = (byte)LookupAudioDataRaw(audioPixels[i]);
		}
		
		if (m_splatIsBedrock == null) return;
		m_bedrockAsset = Resources.Load<TextAsset>("bedrock");
		m_bedrockData = m_bedrockAsset.GetData<byte>();
	}
#if UNITY_EDITOR
	[UnityEditor.MenuItem("22Cans/Audio/Generate Coast Map")]
	static void GenerateCoastMap()
	{
		var crs = Me.GetComponentInChildren<CameraRenderSettings>();
		var terrain = Terrain.activeTerrain;
		var td = terrain.terrainData;
		var heights = td.GetHeights(0, 0, td.heightmapResolution, td.heightmapResolution);
		int coastMapSize = td.heightmapResolution / c_coastMapDownscale;
		byte[] coastMap = new byte[coastMapSize * coastMapSize];
		byte[] debugMap = new byte[coastMapSize * coastMapSize];
		const byte c_land = 0;
		const byte c_sea = 255;
		const byte c_coastClosest = 1;
		for (int z = 0; z < coastMapSize; ++z)
		{
			for (int x = 0; x < coastMapSize; ++x)
			{
				int numSea = 0, numLand = 0;
				for (int zz = 0; zz < c_coastMapDownscale; ++zz)
				{
					for (int xx = 0; xx < c_coastMapDownscale; ++xx)
					{
						int index = (x * c_coastMapDownscale + xx) + (z * c_coastMapDownscale + zz) * td.heightmapResolution;
						var h = heights[z * c_coastMapDownscale + zz, x * c_coastMapDownscale + xx] * GlobalData.HFtoH;
						if (h < GlobalData.c_seaLevel) ++numSea;
						else ++numLand;
					}
				}
				if (numSea == 0)
					coastMap[x + z * coastMapSize] = c_land;
				else if (numLand == 0)
					coastMap[x + z * coastMapSize] = c_sea;
				else
					coastMap[x + z * coastMapSize] = c_coastClosest;
				
				if (numSea == 0) debugMap[x + z * coastMapSize] = 1;
				else if (numLand == 0) debugMap[x + z * coastMapSize] = 2;
				else debugMap[x + z * coastMapSize] = 3;
			}
		}
		for (int z = 1; z < coastMapSize - 1; ++z)
		{
			for (int x = 1; x < coastMapSize - 1; ++x)
			{
				int index = x + z * coastMapSize;
				if (coastMap[index] == c_land && (coastMap[index - 1] == c_sea || coastMap[index + 1] == c_sea || coastMap[index - coastMapSize] == c_sea || coastMap[index + coastMapSize] == c_sea))
					coastMap[index] = c_coastClosest;
			}
		}
		bool changed = true;
		while (changed)
		{
			changed = false;
			for (int z = 1; z < coastMapSize - 1; ++z)
			{
				for (int x = 1; x < coastMapSize - 1; ++x)
				{
					int index = x + z * coastMapSize;
					//if (coastMap[index] == c_sea || coastMap[index] == c_land)
					{
						int nx = (coastMap[index - 1] - 1) & 255, px = (coastMap[index + 1] - 1) & 255, nz = (coastMap[index - coastMapSize] - 1) & 255, pz = (coastMap[index + coastMapSize] - 1) & 255;
						int min = Mathf.Min(nx, px, nz, pz);
						if (min < 254 && min + 2 < ((coastMap[index] - 1) & 255))//254)
						{
							min = Mathf.Min(min + 2, 253); // avoid overflow
							coastMap[index] = (byte)min;
							changed = true;
						}
					}
				}
			}
		}
		System.IO.File.WriteAllBytes("Assets/Resources/coastmap.bytes", coastMap);
	}

	[UnityEditor.MenuItem("22Cans/Audio/Generate Audio Map")]
	static void GenerateAudioMap()
	{
		var crs = Me.GetComponentInChildren<CameraRenderSettings>();
		var terrain = Terrain.activeTerrain;
		var td = terrain.terrainData;
		var splats = td.alphamapTextures;
		int num = splats.Length;
		var splatData = new NativeArray<NativeArray<byte>>(num, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
		for (int i = 0; i < num; ++i)
			splatData[i] = splats[i].GetRawTextureData<byte>();
		int fullSize = td.heightmapResolution - 1;
		int bitmapSize = fullSize / 4;
		Color32[] colors = new Color32[bitmapSize * bitmapSize];
		int[] types = new int[32];
		Dictionary<string, int> typeMap = new() {
			{ "Sea", 0 },
			{ "Grass", 1 },
			{ "Dirt", 2 },
			{ "Stone", 3 },
			{ "Mud", 4 },
		};
		for (int z = 0; z < bitmapSize; ++z)
		{
			for (int x = 0; x < bitmapSize; ++x)
			{
				for (int i = 0; i < types.Length; ++i) types[i] = 0; 
				for (int zz = 0; zz < 4; ++zz)
				{
					for (int xx = 0; xx < 4; ++xx)
					{
						string type = "";
						int index = (x * 4 + xx) + (z * 4 + zz) * fullSize;
						var h = td.GetHeight(x * 4 + xx, z * 4 + zz);
						if (h < GlobalData.c_seaLevel)
						{
							type = "Sea";
						}
						else
						{
							int bestValue = 0;
							int bestIndex = 0;
							int index4 = index * 4;
							for (int i = 0; i < num; ++i)
							{
								for (int j = 0; j < 4; ++j)
								{
									int value = splatData[i][index4 + j];
									if (value > bestValue)
									{
										bestValue = value;
										bestIndex = i * 4 + j;
									}
								}
							}
							type = crs.m_splatAudioTypes[bestIndex];
						}
						var typeIndex = typeMap[type];
						types[typeIndex]++;
					}
				}
				int maxType = 0;
				for (int i = 1; i < types.Length; ++i)
					if (types[i] > types[maxType])
						maxType = i;
				Color32 c = crs.m_audioColours[maxType];
				colors[x + z * bitmapSize] = c;
			}
		}
		var tex = new Texture2D(bitmapSize, bitmapSize, TextureFormat.RGBA32, false);
		tex.SetPixels32(colors);
		tex.Apply(false, false);
		var bytes = tex.EncodeToPNG();
		System.IO.File.WriteAllBytes("Assets/Resources/audiomap.png", bytes);
		Debug.LogError($"Generated base audio map at Assets/Resources/audiomap.png");
	}

	[UnityEditor.MenuItem("22Cans/Art/Generate Bedrock Data")]
	static void GenerateBedrockDataFromSplats()
	{
		var data = new NativeArray<byte>(GlobalData.c_heightmapW * GlobalData.c_heightmapH, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
		var sw = System.Diagnostics.Stopwatch.StartNew();
		//var crs = Camera.main.transform.GetComponentInChildren<CameraRenderSettings>();
		var crs = GameObject.Find("TownCamera").GetComponent<CameraRenderSettings>();
		var splats = Terrain.activeTerrain.terrainData.alphamapTextures;
		int num = splats.Length;
		var splatData = new NativeArray<NativeArray<byte>>(num, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
		for (int i = 0; i < num; ++i)
			splatData[i] = splats[i].GetRawTextureData<byte>();
		var job = new BedrockJob(splatData, data, crs.m_splatIsBedrock);
		job.Schedule().Complete();
		int count = job.Finish();
		sw.Stop();
		Debug.LogError($"Bedrock data generated [{crs.transform.Path()}] in {sw.ElapsedMilliseconds}ms using {count} buffer entries of {BedrockJob.c_maxTempData}");
		splatData.Dispose();
#if UNITY_EDITOR
		System.IO.File.WriteAllBytes("Assets/Resources/bedrock.bytes", data.ToArray());
#endif
		int bedrockBitmap = 0;
		for (int i = 0; i < data.Length; ++i)
		{
			bedrockBitmap |= 1 << (data[i] - 1);
		}
		int nextIndex = 0;
		Color[] debugColours = new Color[32];
		int[] indexLookup = new int[16];
		for (int i = 0; i < 32; ++i)
		{
			if ((bedrockBitmap & (1 << i)) != 0)
			{
				int usedIndex = nextIndex ++;
				indexLookup[usedIndex] = i;
				debugColours[i] = new Color((usedIndex >> 0) & 1, (usedIndex >> 1) & 1, (usedIndex >> 2) & 1);
				Debug.LogError($"Bedrock texture {i} used - RGB {debugColours[i]}");
			}
		}
		int panelSize = GlobalData.c_heightmapW / 4;
		var debugTex = new Texture2D(GlobalData.c_heightmapW + panelSize, GlobalData.c_heightmapH);
		for (int y = 0; y < GlobalData.c_heightmapH; ++y)
			for (int x = 0; x < GlobalData.c_heightmapW; ++x)
				debugTex.SetPixel(x, y, debugColours[data[x + y * GlobalData.c_heightmapW] - 1]);
		for (int y = 0; y < GlobalData.c_heightmapH; ++y)
			for (int x = 0; x < panelSize; ++x)
				debugTex.SetPixel(x + GlobalData.c_heightmapW, y, Color.grey);
		const float c_textScale = 6;
		var cfg = CameraRenderSettings.SplatConfig;
		for (int i = 0; i < nextIndex; ++i)
		{
			var clr = new Color((i >> 0) & 1, (i >> 1) & 1, (i >> 2) & 1);
			string label = null;
			if (cfg != null)
			{
				var diff = cfg.sourceTextures[indexLookup[i]]?.diffuse;
				if (diff != null) label = diff.name.Replace("MA_TX_", "").Replace("_Basemap", "", StringComparison.OrdinalIgnoreCase).Replace("_BaseColor", "", StringComparison.OrdinalIgnoreCase).Replace("_Albedo", "", StringComparison.OrdinalIgnoreCase);
			}
			Font8x8.DrawString(debugTex, GlobalData.c_heightmapW + (int)(c_textScale * 4), (int) (c_textScale * 4 + 12 * c_textScale * i), label ?? $"Layer {indexLookup[i]}", clr, c_textScale);
		}
		debugTex.Apply(false, false);
		System.IO.File.WriteAllBytes("Assets/_Art/DebugBedrock.png", debugTex.EncodeToPNG());
		data.Dispose();
	}


	public string GetSplatName(int _index)
	{
		var cfg = CameraRenderSettings.SplatConfig;
		if (cfg != null)
		{
			var label = "<none>";
			var diff = cfg.sourceTextures[_index]?.diffuse;
			if (diff != null) label = diff.name.Replace("MA_TX_", "").Replace("_Basemap", "", StringComparison.OrdinalIgnoreCase).Replace("_BaseColor", "", StringComparison.OrdinalIgnoreCase).Replace("_Albedo", "", StringComparison.OrdinalIgnoreCase);
			return label;
		}
		return "<cfg not found>";
	}
#else
	public string GetSplatName(int _index) => "";
#endif

	[BurstCompile]
	public struct BedrockJob : IJob
	{
		[ReadOnly] NativeArray<byte> m_texData1;
		[ReadOnly] NativeArray<byte> m_texData2;
		[ReadOnly] NativeArray<byte> m_texData3;
		[ReadOnly] NativeArray<byte> m_texData4;
		NativeArray<byte> m_data;
		NativeArray<int> m_temp;
		NativeArray<int> m_temp2;
		NativeArray<int> m_rnd;
		NativeArray<int> m_counts;
		NativeArray<int> m_output;
		[ReadOnly] NativeArray<bool> m_splatIsBedrock;

		public const int c_maxTempData = 65536 * 16; // by inspection; max used so far ~29k

		const int c_numSplatTextures = 4;
		public BedrockJob(NativeArray<NativeArray<byte>> _texData, NativeArray<byte> _data, bool[] _splatIsBedrock)
		{
			m_texData1 = _texData[0];
			m_texData2 = _texData[1];
			m_texData3 = _texData[2];
			m_texData4 = _texData[3];
			m_data = _data;
			m_temp = new NativeArray<int>(c_maxTempData, Allocator.TempJob, NativeArrayOptions.ClearMemory);
			m_temp2 = new NativeArray<int>(c_maxTempData, Allocator.TempJob, NativeArrayOptions.ClearMemory);
			m_splatIsBedrock = new NativeArray<bool>(_splatIsBedrock.Length, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
			for (int i = 0; i < _splatIsBedrock.Length; ++i) m_splatIsBedrock[i] = _splatIsBedrock[i];
			
			m_rnd = new NativeArray<int>(17, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
			uint seed = 0x1237117;
			for (int i = 0; i < 17; ++i) m_rnd[i] = ((int)Utility.XorShift(ref seed) & 255) >> ((int) Utility.XorShift(ref seed) & 3);
			
			m_counts = new NativeArray<int>(17, Allocator.TempJob, NativeArrayOptions.ClearMemory);
			m_output = new NativeArray<int>(1, Allocator.TempJob, NativeArrayOptions.ClearMemory);
		}


		public void Execute()
		{
			int nextCheck = 0;
			for (int z = 0; z < GlobalData.c_heightmapH; ++z)
			{
				for (int x = 0; x < GlobalData.c_heightmapW; ++x)
				{
					int xzIndex = x + z * GlobalData.c_heightmapW;
					int texIndex = xzIndex * 4;
					byte value = 0, valueLevel = 0, level;
					if (m_splatIsBedrock[0] && (level = m_texData1[texIndex + 0]) > valueLevel) { value = 1; valueLevel = level; }
					if (m_splatIsBedrock[1] && (level = m_texData1[texIndex + 1]) > valueLevel) { value = 2; valueLevel = level; }
					if (m_splatIsBedrock[2] && (level = m_texData1[texIndex + 2]) > valueLevel) { value = 3; valueLevel = level; }
					if (m_splatIsBedrock[3] && (level = m_texData1[texIndex + 3]) > valueLevel) { value = 4; valueLevel = level; }
					if (m_splatIsBedrock[4] && (level = m_texData2[texIndex + 0]) > valueLevel) { value = 5; valueLevel = level; }
					if (m_splatIsBedrock[5] && (level = m_texData2[texIndex + 1]) > valueLevel) { value = 6; valueLevel = level; }
					if (m_splatIsBedrock[6] && (level = m_texData2[texIndex + 2]) > valueLevel) { value = 7; valueLevel = level; }
					if (m_splatIsBedrock[7] && (level = m_texData2[texIndex + 3]) > valueLevel) { value = 8; valueLevel = level; }
					if (m_splatIsBedrock[8] && (level = m_texData3[texIndex + 0]) > valueLevel) { value = 9; valueLevel = level; }
					if (m_splatIsBedrock[9] && (level = m_texData3[texIndex + 1]) > valueLevel) { value = 10; valueLevel = level; }
					if (m_splatIsBedrock[10] && (level = m_texData3[texIndex + 2]) > valueLevel) { value = 11; valueLevel = level; }
					if (m_splatIsBedrock[11] && (level = m_texData3[texIndex + 3]) > valueLevel) { value = 12; valueLevel = level; }
					if (m_splatIsBedrock[12] && (level = m_texData4[texIndex + 0]) > valueLevel) { value = 13; valueLevel = level; }
					if (m_splatIsBedrock[13] && (level = m_texData4[texIndex + 1]) > valueLevel) { value = 14; valueLevel = level; }
					if (m_splatIsBedrock[14] && (level = m_texData4[texIndex + 2]) > valueLevel) { value = 15; valueLevel = level; }
					if (m_splatIsBedrock[15] && (level = m_texData4[texIndex + 3]) > valueLevel) { value = 16; valueLevel = level; }
					m_data[xzIndex] = value;
				}
			}

			var currentList = m_temp;
			var nextList = m_temp2;

			for (int z = 0; z < GlobalData.c_heightmapH; ++z)
			{
				for (int x = 0; x < GlobalData.c_heightmapW; ++x)
				{
					int index = x + z * GlobalData.c_heightmapW;
					if (m_data[index] != 0)
						if ((x > 0 && m_data[index - 1] == 0) || (x < GlobalData.c_heightmapW - 1 && m_data[index + 1] == 0) ||
						    (z > 0 && m_data[index - GlobalData.c_heightmapW] == 0) || (z < GlobalData.c_heightmapH - 1 && m_data[index + GlobalData.c_heightmapW] == 0))
							nextList[nextCheck++] = x + z * 65536;
				}
			}

			for (int i = 0; i < 17; ++i) m_temp[m_temp.Length - i - 1] = 0;

			int firstReal = nextCheck;
			int nextIteration = 0;
			int iterationIndex = 0;
			int biggestCount = 0;
			uint rseed = 0x1237317;
			while (nextCheck > 0)
			{
				(currentList, nextList) = (nextList, currentList);

				int count = nextCheck;
				if (count > m_output[0])
					m_output[0] = count;
				nextCheck = 0;

				++iterationIndex;

				// randomise all entries
				var seed = (uint) (iterationIndex * 7162537 + 12347);
				for (int j = 0; j < count; ++j)
				{
					long rr = (long) Utility.XorShift(ref seed);
					var r = (int) ((rr * count) >> 32);
					(currentList[j], currentList[r]) = (currentList[r], currentList[j]);
				}
				/*if (count > biggestCount) biggestCount = count;
				Debug.LogError($"Iteration {iterationIndex} with {count} entries, biggest count so far {biggestCount} (first {firstReal})");
				if (((iterationIndex - 1) % 100) == 0)
				{
					// dump iteration
					var bm = new Texture2D(GlobalData.c_heightmapW, GlobalData.c_heightmapH);
					for (int j = 0; j < m_data.Length; ++j)
					{
						var clrIndex = (m_data[j] + 1) / 2;
						bm.SetPixel(j % GlobalData.c_heightmapW, j / GlobalData.c_heightmapW, new Color((clrIndex >> 0) & 1, (clrIndex >> 1) & 1, (clrIndex >> 2) & 1, 1));
					}
					bm.Apply();
					System.IO.File.WriteAllBytes($"{Application.persistentDataPath}/bedrock{$"000{iterationIndex / 100}"[^4..]}.png", bm.EncodeToPNG());
				}*/

				for (int i = 0; i < count; ++i)
				{
					int check = currentList[i];
					int x = check & 0xFFFF, z = check >> 16;
					int index = x + z * GlobalData.c_heightmapW;
					if (x > 0 && m_data[index - 1] == 0)
					{
						m_data[index - 1] = GetBest(x - 1, z, ref rseed);
						nextList[nextCheck++] = (x - 1) + z * 65536;
					}
					if (x < GlobalData.c_heightmapW - 1 && m_data[index + 1] == 0)
					{
						m_data[index + 1] = GetBest(x + 1, z, ref rseed);
						nextList[nextCheck++] = (x + 1) + z * 65536;
					}
					if (z > 0 && m_data[index - GlobalData.c_heightmapW] == 0)
					{
						m_data[index - GlobalData.c_heightmapW] = GetBest(x, z - 1, ref rseed);
						nextList[nextCheck++] = x + (z - 1) * 65536;
					}
					if (z < GlobalData.c_heightmapH - 1 && m_data[index + GlobalData.c_heightmapW] == 0)
					{
						m_data[index + GlobalData.c_heightmapW] = GetBest(x, z + 1, ref rseed);
						nextList[nextCheck++] = x + (z + 1) * 65536;
					}
				}
			}
		}

		byte GetBest(int _x, int _z, ref uint _seed)
		{
			byte vL = 0, vR = 0, vU = 0, vD = 0;
			byte vLU = 0, vRU = 0, vLD = 0, vRD = 0;
			if (_x > 0) vL = m_data[_x - 1 + _z * GlobalData.c_heightmapW];
			if (_x < GlobalData.c_heightmapW - 1) vR = m_data[_x + 1 + _z * GlobalData.c_heightmapW];
			if (_z > 0) vU = m_data[_x + (_z - 1) * GlobalData.c_heightmapW];
			if (_z < GlobalData.c_heightmapH - 1) vD = m_data[_x + (_z + 1) * GlobalData.c_heightmapW];
			if (_x > 0 && _z > 0) vLU = m_data[_x - 1 + (_z - 1) * GlobalData.c_heightmapW];
			if (_x < GlobalData.c_heightmapW - 1 && _z > 0) vRU = m_data[_x + 1 + (_z - 1) * GlobalData.c_heightmapW];
			if (_x > 0 && _z < GlobalData.c_heightmapH - 1) vLD = m_data[_x - 1 + (_z + 1) * GlobalData.c_heightmapW];
			if (_x < GlobalData.c_heightmapW - 1 && _z < GlobalData.c_heightmapH - 1) vRD = m_data[_x + 1 + (_z + 1) * GlobalData.c_heightmapW];
			m_counts[vL]++;
			m_counts[vR]++;
			m_counts[vU]++;
			m_counts[vD]++;
			m_counts[vLU]++;
			m_counts[vRU]++;
			m_counts[vLD]++;
			m_counts[vRD]++;
			int total = 0;
			for (int i = 1; i < 17; ++i)
				m_counts[i] *= m_rnd[i];
			for (int i = 1; i < 17; ++i)
				total += m_counts[i];
			if (total == 0) return 0;
			var r = ((Utility.XorShift(ref _seed) & 0xFFFF) * total) >> 16;
			int best = 0;
			for (int i = 1; i < 17; ++i)
			{
				r -= m_counts[i];
				if (r < 0)
				{
					best = i;
					break;
				}
			}
			m_counts[vL] = 0;
			m_counts[vR] = 0;
			m_counts[vU] = 0;
			m_counts[vD] = 0;
			m_counts[vLU] = 0;
			m_counts[vRU] = 0;
			m_counts[vLD] = 0;
			m_counts[vRD] = 0;
			return (byte)best;
		}

		public int Finish()
		{
			int count = m_output[0];
			m_temp2.Dispose();
			m_temp.Dispose();
			m_rnd.Dispose();
			m_counts.Dispose();
			m_output.Dispose();
			return count;
		}
	}

	public int BedrockAtPoint(Vector3 _p)
	{
		if (m_bedrockData.Length == 0) return 0;
		int x = GlobalData.TerrainX(_p.x), z = GlobalData.TerrainZ(_p.z);
		return m_bedrockData[x + z * GlobalData.c_heightmapW] - 1;
	}

	static DebugConsole.Command s_bedrocktosplat = new("bedrocktosplat", _s => Me.DEBUG_CopyBedrockToSplat());

	bool m_DEBUG_isBedrock = false;
	void DEBUG_CopyBedrockToSplat()
	{
		if (m_DEBUG_isBedrock)
		{
			ResetSplat(0, 0, GlobalData.c_heightmapW, GlobalData.c_heightmapH);
		}
		else
		{
			var job = new SplatBedrockJob(new int2(2048, 2048), 2047, 1, 4096, new int2(0, 0), GlobalData.c_heightmapW, m_texData);
			job.Schedule(4096 * 4096, 256).Complete();
		}
		ApplySplatData();
		m_DEBUG_isBedrock = !m_DEBUG_isBedrock;
	}

	public float[] GetSplatInArea(Vector3 _center, float _radius)
	{
		if (m_texData.Length < 4) return new float[16];
		var job = new SplatInAreaJob(m_texData, _center, _radius);
		job.Schedule().Complete();
		return job.GetOutput();
	}

	[BurstCompile]
	public struct SplatInAreaJob : IJob
	{
		[ReadOnly] NativeArray<byte> m_texData1;
		[ReadOnly] NativeArray<byte> m_texData2;
		[ReadOnly] NativeArray<byte> m_texData3;
		[ReadOnly] NativeArray<byte> m_texData4;
		[ReadOnly] int m_x, m_z, m_w, m_h;
		NativeArray<float> m_output;

		const int c_numSplatTextures = 4;

		public SplatInAreaJob(NativeArray<NativeArray<byte>> _texData, Vector3 _center, float _radius)
		{
			m_texData1 = _texData[0];
			m_texData2 = _texData[1];
			m_texData3 = _texData[2];
			m_texData4 = _texData[3];
			m_output = new NativeArray<float>(16, Allocator.TempJob, NativeArrayOptions.ClearMemory);
			
			m_x = GlobalData.TerrainXclamped(_center.x - _radius);
			m_z = GlobalData.TerrainZclamped(_center.z - _radius);
			m_w = GlobalData.TerrainXclamped(_center.x + _radius + .99f) - m_x;
			m_h = GlobalData.TerrainZclamped(_center.z + _radius + .99f) - m_z;
		}

		public void Execute()
		{
			for (int z = 0; z < m_h; ++z)
			{
				int baseIndex = ((z + m_z) * GlobalData.c_heightmapW + m_x) * 4;
				for (int x = 0; x < m_w; ++x)
				{
					var x4 = x << 2;
					m_output[0] += m_texData1[baseIndex + x4 + 0];
					m_output[1] += m_texData1[baseIndex + x4 + 1];
					m_output[2] += m_texData1[baseIndex + x4 + 2];
					m_output[3] += m_texData1[baseIndex + x4 + 3];
					m_output[4] += m_texData2[baseIndex + x4 + 0];
					m_output[5] += m_texData2[baseIndex + x4 + 1];
					m_output[6] += m_texData2[baseIndex + x4 + 2];
					m_output[7] += m_texData2[baseIndex + x4 + 3];
					m_output[8] += m_texData3[baseIndex + x4 + 0];
					m_output[9] += m_texData3[baseIndex + x4 + 1];
					m_output[10] += m_texData3[baseIndex + x4 + 2];
					m_output[11] += m_texData3[baseIndex + x4 + 3];
					m_output[12] += m_texData4[baseIndex + x4 + 0];
					m_output[13] += m_texData4[baseIndex + x4 + 1];
					m_output[14] += m_texData4[baseIndex + x4 + 2];
					m_output[15] += m_texData4[baseIndex + x4 + 3];
				}
			}
			float totalOut = 0;
			for (int i = 0; i < 16; ++i)
				totalOut += m_output[i];
			if (totalOut > 0)
				for (int i = 0; i < 16; ++i)
					m_output[i] /= totalOut;
		}

		public float[] GetOutput()
		{
			var output = new float[16];
			for (int i = 0; i < 16; ++i)
				output[i] = m_output[i];
			m_output.Dispose();
			return output;
		}
	}

	public void LoadTerrainData()
	{
		m_textures = GlobalData.Me.m_terrainData.alphamapTextures;
		m_backupTextures = GlobalData.Me.m_originalTerrainData.alphamapTextures;
		int num = m_textures.Length;
		m_texData = new NativeArray<NativeArray<byte>>(num, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
		m_backupTexData = new NativeArray<NativeArray<byte>>(num, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
		for (int i = 0; i < num; ++i)
		{
			m_texData[i] = m_textures[i].GetRawTextureData<byte>();
			m_backupTexData[i] = m_backupTextures[i].GetRawTextureData<byte>();
			Shader.SetGlobalTexture($"_Splat{i+1}", m_textures[i]);
		}
		
		GenerateBedrockData();
	}

	private int index = 0;
	private string debugFolder = "SplatDebugging";
	public void DebugTextures(string _calledFrom, int4 _channels, bool _withBackups = false) //WARNING: EXPENSIVE
	{
		if (!System.IO.Directory.Exists(debugFolder))
			System.IO.Directory.CreateDirectory(debugFolder);
		int w = GlobalData.Me.m_terrainData.alphamapWidth;
		int h = GlobalData.Me.m_terrainData.alphamapHeight;
		foreach (var texData in m_texData)
		{
			int len = texData.Length;
			var data1 = new NativeArray<byte>(len, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
			var data2 = new NativeArray<byte>(len, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
			var job = new SplitChannelsJob(_channels, texData, data1, data2);
			job.Schedule(len / 4, 1024).Complete();
			var tex1 = new Texture2D(w, h);
			tex1.SetPixelData(data1, 0);
			var png1 = tex1.EncodeToPNG();
			System.IO.File.WriteAllBytes($"{debugFolder}/{index}-{_calledFrom}(1).png", png1);
			var tex2 = new Texture2D(w, h);
			tex2.SetPixelData(data2, 0);
			var png2 = tex2.EncodeToPNG();
			System.IO.File.WriteAllBytes($"{debugFolder}/{index++}-{_calledFrom}(2).png", png2);
			data1.Dispose();
			data2.Dispose();
		}
		if (!_withBackups)
			return;
		foreach (var texData in m_backupTexData)
		{
			int len = texData.Length;
			var data1 = new NativeArray<byte>(len, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
			var data2 = new NativeArray<byte>(len, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
			var job = new SplitChannelsJob(_channels, texData, data1, data2);
			job.Schedule(len / 4, 1024).Complete();
			var tex1 = new Texture2D(w, h);
			tex1.SetPixelData(data1, 0);
			var png1 = tex1.EncodeToPNG();
			System.IO.File.WriteAllBytes($"{debugFolder}/{index}-{_calledFrom}(1).png", png1);
			var tex2 = new Texture2D(w, h);
			tex2.SetPixelData(data2, 0);
			var png2 = tex2.EncodeToPNG();
			System.IO.File.WriteAllBytes($"{debugFolder}/{index++}-{_calledFrom}(2).png", png2);
			data1.Dispose();
			data2.Dispose();
		}
	}

	[BurstCompile]
	public struct SplitChannelsJob : IJobParallelFor
	{
		[ReadOnly] public int4 m_channels;
		[ReadOnly] public NativeArray<byte> m_dataO;

		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_data1;
		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_data2;

		public SplitChannelsJob(int4 _channels, NativeArray<byte> _dataO, NativeArray<byte> _data1, NativeArray<byte> _data2)
		{
			m_channels = _channels;
			m_dataO = _dataO;
			m_data1 = _data1;
			m_data2 = _data2;
		}

		public void Execute(int index)
		{
			index *= 4;
			(m_data1[index], m_data1[index + 1], m_data1[index + 2], m_data1[index + 3]) = (0, 0, 0, 255);
			(m_data2[index], m_data2[index + 1], m_data2[index + 2], m_data2[index + 3]) = (0, 0, 0, 255);

			int channel = m_channels[0];
			var data = channel > 3 ? m_data2 : m_data1;
			data[index + channel % 4] = m_dataO[index];

			channel = m_channels[1];
			data = channel > 3 ? m_data2 : m_data1;
			data[index + channel % 4] = m_dataO[index + 1];

			channel = m_channels[2];
			data = channel > 3 ? m_data2 : m_data1;
			data[index + channel % 4] = m_dataO[index + 2];

			channel = m_channels[3];
			data = channel > 3 ? m_data2 : m_data1;
			data[index + channel % 4] = m_dataO[index + 3];
		}
	}

	protected override void _OnDestroy()
	{
		if (System.IO.Directory.Exists(debugFolder))
			System.IO.Directory.Delete(debugFolder, true);

		m_texData.Dispose();
		m_backupTexData.Dispose();
		m_highLevelGrassPresence.Dispose();
#if SEPARATE_DETAIL_MAP
		m_rawDetailOriginal.Dispose();
		m_rawDetail.Dispose();
#endif
	}

	public void BeginSplatOperations(Vector3 _min, Vector3 _max)
	{
		++m_splatOpDeferCount;
		if (m_splatOpDeferCount > 1)
		{
			if ((_min - m_splatOpMin).sqrMagnitude > 0 || (_max - m_splatOpMax).sqrMagnitude > 0)
			{
				Debug.LogError($"Error: nested splat operations");
			}
			return;
		}

		m_splatOpMin = _min; m_splatOpMax = _max;
		m_splatOpMinX = GlobalData.TerrainX(_min.x);
		m_splatOpMinZ = GlobalData.TerrainZ(_min.z);
		m_splatOpMaxX = GlobalData.TerrainXc(_max.x);
		m_splatOpMaxZ = GlobalData.TerrainZc(_max.z);

		int w = m_splatOpMaxX - m_splatOpMinX, h = m_splatOpMaxZ - m_splatOpMinZ;
		if (m_splatOpMaxX > GlobalData.c_heightmapW) w = GlobalData.c_heightmapW - m_splatOpMinX;
		if (m_splatOpMaxZ > GlobalData.c_heightmapH) h = GlobalData.c_heightmapH - m_splatOpMinZ;
	}

	public void ResetSplat(int _x, int _z, int _w, int _h)
	{
		// Note: it's 3x faster to run the NativeArray.Copy commands from main rather than a job, and 100x faster to run a single copy when copying whole rows
		int textures = c_SplatChannels / 4;
		int mapW = GlobalData.c_heightmapW;
		int mapH = GlobalData.c_heightmapH;
		if (_x == 0 && _w == mapW) // whole rows, row data is contiguous
		{
			int index = 4 * mapW * _z;
			int copyBytes = _w * _h * 4;
			for (int i = 0; i < textures; i++)
				NativeArray<byte>.Copy(m_backupTexData[i], index, m_texData[i], index, copyBytes);
		}
		else
		{
			int indBase = 4 * (_x + mapW * _z);
			int stride = mapW << 2;
			int copyBytes = _w * 4;
			for (int i = 0; i < textures; i++)
			{
				for (int y = 0, ind = indBase; y < _h; ++y, ind += stride)
					NativeArray<byte>.Copy(m_backupTexData[i], ind, m_texData[i], ind, copyBytes);
				//var job = new RestoreSplatJob(_x, _z, _w * 4, GlobalData.c_heightmapW, m_backupTexData[i], m_texData[i]);
				//job.Schedule(_h, 2048).Complete();
			}
		}
	}

	[BurstCompile]
	public struct RestoreSplatJob : IJobParallelFor
	{
		[ReadOnly] public int m_sx;
		[ReadOnly] public int m_sz;
		[ReadOnly] public int m_w;
		[ReadOnly] public int m_mapW;

		[ReadOnly] NativeArray<byte> m_from;

		[NativeDisableContainerSafetyRestriction]
		NativeArray<byte> m_to;

		public RestoreSplatJob(int _sx, int _sz, int _w, int _mapW, NativeArray<byte> _from, NativeArray<byte> _to)
		{
			m_sx = _sx;
			m_sz = _sz;
			m_w = _w;
			m_mapW = _mapW;
			m_from = _from;
			m_to = _to;
		}

		public readonly void Execute(int index)
		{
			int ind = 4 * (m_sx + m_mapW * (m_sz + index));
			NativeArray<byte>.Copy(m_from, ind, m_to, ind, m_w);
		}
	}

	public void EndSplatOperations()
	{
		if (m_splatOpDeferCount <= 0)
		{
			Debug.LogError($"Error: too many EndSplatOperations");
			return;
		}
		--m_splatOpDeferCount;
		if (m_splatOpDeferCount > 0) return;

		UpdateHighLevelGrassPresence();

		ApplySplatData();
	}
	public void ApplySplatData()
	{
		foreach (var tex in m_textures)
			tex.Apply(false, false);
	}

	/*void OnDrawGizmos()
	{
		if (GlobalData.Me == null) return;
		for (int y = 0; y < c_grassPatchCountH; ++y)
		{
			for (int x = 0; x < c_grassPatchCountW; ++x)
			{
				var clr = Color.black;
				clr.r = m_highLevelGrassPresence[x + y * c_grassPatchCountW] & 1;
				clr.g = (m_highLevelGrassPresence[x + y * c_grassPatchCountW] >> 1) & 1;
				var offs = new Vector3((x + .5f) * c_grassPatchCells / GlobalData.c_terrainXZScale, 0, (y + .5f) * c_grassPatchCells / GlobalData.c_terrainXZScale);
				var pos = offs + GlobalData.c_terrainOrigin;
				pos = pos.GroundPosition(1);
				Gizmos.color = clr;
				Gizmos.DrawCube(pos, Vector3.one * c_grassPatchSize * .5f);
			}
		}
	}*/
	
	[BurstCompile]
	public struct HighLevelSplatJob : IJobParallelFor
	{
		[WriteOnly] public NativeArray<byte> m_highLevelGrassPresence;
		[ReadOnly] public NativeArray<byte> m_texData0;
		[ReadOnly] public NativeArray<byte> m_texData1;
		[ReadOnly] public NativeArray<byte> m_texData2;
		[ReadOnly] public NativeArray<byte> m_texData3;
		[ReadOnly] public NativeArray<byte> m_splatPresence;
		[ReadOnly] public int m_numChannels;

		[ReadOnly] int m_splatOpMinX;
		[ReadOnly] int m_splatOpMinZ;
		[ReadOnly] int m_splatOpMaxX;
		[ReadOnly] int m_splatOpMaxZ;

		public HighLevelSplatJob(int _dummy)
		{
			m_texData0 = Me.m_texData[0];
			m_texData1 = Me.m_texData[1];
			m_texData2 = Me.m_texData[2];
			m_texData3 = Me.m_texData[3];
			m_highLevelGrassPresence = Me.m_highLevelGrassPresence;
			m_splatPresence = new NativeArray<byte>(c_SplatChannels, Allocator.TempJob, NativeArrayOptions.UninitializedMemory);
			m_numChannels = c_SplatChannels;
			m_splatOpMinX = Me.m_splatOpMinX / c_grassPatchCells;
			m_splatOpMinZ = Me.m_splatOpMinZ / c_grassPatchCells;
			m_splatOpMaxX = (Me.m_splatOpMaxX + c_grassPatchCells - 1) / c_grassPatchCells;
			m_splatOpMaxZ = (Me.m_splatOpMaxZ + c_grassPatchCells - 1) / c_grassPatchCells;

			for (int i = 0; i < c_SplatChannels; i++)
			{
				var values = Me.m_grassColoursAndPresence[i].m_presence;
				var values2 = Me.m_grassColoursAndPresence[i].m_presence2;
				byte valuesByteJ = 0;
				if (values.x > 0) valuesByteJ |= 1;
				if (values.y > 0) valuesByteJ |= 2;
				if (values.z > 0) valuesByteJ |= 4;
				if (values.w > 0) valuesByteJ |= 8;
				if (values2.x > 0) valuesByteJ |= 16;
				if (values2.y > 0) valuesByteJ |= 32;
				if (values2.z > 0) valuesByteJ |= 64;
				if (values2.w > 0) valuesByteJ |= 128;
				m_splatPresence[i] = valuesByteJ;
			}
		}

		public void Dispose()
		{
			m_splatPresence.Dispose();
		}

		public void Execute(int _index)
		{
			int textures = m_numChannels / 4;
			int x = _index % c_grassPatchCountW;
			int y = _index / c_grassPatchCountW;
			if (x < m_splatOpMinX || y < m_splatOpMinZ || x >= m_splatOpMaxX || y >= m_splatOpMaxZ)
				return;
			int yBase = y * c_grassPatchCells;
			int xBase = x * c_grassPatchCells;
			int finalValue = 0;
			for (int yy = 0; yy < c_grassPatchCells; ++yy)
			{
				int indexBase = ((yBase + yy) * GlobalData.c_heightmapW + xBase) << 2;
				for (int xx = 0; xx < c_grassPatchCells; ++xx, indexBase += 4)
				{
					finalValue |= Lookup(m_texData0, indexBase, 0);
					finalValue |= Lookup(m_texData1, indexBase, 4);
					finalValue |= Lookup(m_texData2, indexBase, 8);
					finalValue |= Lookup(m_texData3, indexBase, 12);
				}
			}
			m_highLevelGrassPresence[_index] = (byte)finalValue;
		}

		byte Lookup(NativeArray<byte> _tex, int _indexBase, int _splatIndex)
		{
			byte finalValue = 0;
			for (int j = 0, index = _indexBase; j < 4; ++j, ++index, ++_splatIndex)
			{
				var splatByte = _tex[index];
				if (splatByte > 0) finalValue |= m_splatPresence[_splatIndex];
			}
			return finalValue;
		}
		
	}

	public const int c_grassPatchSize = 8;
	const int c_splatCellsPerMeter = (int) GlobalData.c_terrainXZScale;
	public const int c_grassPatchCells = c_grassPatchSize * c_splatCellsPerMeter;
	public const int c_grassPatchCountW = GlobalData.c_heightmapW / c_grassPatchCells;
	public const int c_grassPatchCountH = GlobalData.c_heightmapH / c_grassPatchCells;
	private NativeArray<byte> m_highLevelGrassPresence = new (c_grassPatchCountW * c_grassPatchCountH, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
	public NativeArray<byte> HighLevelGrassPresence => m_highLevelGrassPresence;
	void UpdateHighLevelGrassPresence()
	{
		//var t = DateTime.UtcNow.Ticks;
		var job = new HighLevelSplatJob(0);
		job.Schedule(c_grassPatchCountW * c_grassPatchCountH, 16).Complete();
		job.Dispose();
		//t = DateTime.UtcNow.Ticks - t;
		//Debug.LogError($"UpdateHighLevelGrassPresence took {t / 10000}ms");
		return;
		
		int textures = c_SplatChannels / 4;
		var valuesByte = new byte[4];
		for (int i = 0; i < textures; i++)
		{
			var splat = m_texData[i];
			for (int j = 0; j < 4; ++j)
			{
				var values = m_grassColoursAndPresence[i * 4 + j].m_presence;
				byte valuesByteJ = 0;
				if (values.x > 0) valuesByteJ |= 1;
				if (values.y > 0) valuesByteJ |= 2;
				if (values.z > 0) valuesByteJ |= 4;
				if (values.w > 0) valuesByteJ |= 8;
				valuesByte[j] = valuesByteJ;
			}
			for (int y = 0; y < c_grassPatchCountH; ++y)
			{
				int yBase = y * c_grassPatchCells;
				for (int x = 0; x < c_grassPatchCountW; ++x)
				{
					int xBase = x * c_grassPatchCells;
					byte finalValue = (i == 0) ? (byte)0 : m_highLevelGrassPresence[x + y * c_grassPatchCountW];
					for (int yy = 0; yy < c_grassPatchCells; ++yy)
					{
						for (int xx = 0; xx < c_grassPatchCells; ++xx)
						{
							int fx = xBase + xx, fy = yBase + yy;
							int indexBase = (fx + fy * GlobalData.c_heightmapW) * 4;
							for (int j = 0; j < 4; ++j)
								if (splat[indexBase + j] > 0)
									finalValue |= valuesByte[j];
						}
					}
					m_highLevelGrassPresence[x + y * c_grassPatchCountW] = finalValue;
				}
			}
		}
	}
	

	public void SetSplatPoint(int _x, int _z, int _channel, float _value)
	{
		if (_x < m_splatOpMinX || _z < m_splatOpMinZ || _x > m_splatOpMaxX || _z > m_splatOpMaxZ)
			return;
		int tex = _channel / 4;
		int texChannel = _channel % 4;
		var texData = m_texData[tex];
		texData[4 * (_x + GlobalData.c_heightmapW * _z) + texChannel] = (byte)(255 * _value);
	}

	public int GetStrongestSplatPoint(Vector3 _pos)
	{
		return GetStrongestSplatPoint(GlobalData.TerrainX(_pos.x), GlobalData.TerrainZ(_pos.z));
	}

	public int GetStrongestSplatPoint(int _x, int _z)
	{
		if (_x < m_splatOpMinX || _z < m_splatOpMinZ || _x > m_splatOpMaxX || _z > m_splatOpMaxZ)
			return 0;
		int baseIndex = 4 * (_x + GlobalData.c_heightmapW * _z);
		int highestSplat = 0;
		int highestIndex = 0;
		for (int i = 0; i < 4; ++i)
		{
			var texData = m_texData[i];
			for (int j = 0; j < 4; ++j)
			{
				if (texData[baseIndex + j] > highestSplat)
				{
					highestSplat = texData[baseIndex + j];
					highestIndex = i * 4 + j;
				}
			}
		}
		return highestIndex;
	}

	bool m_needSplatUpdate = false;
	void CheckForcedSplatUpdate()
	{
		if (m_needSplatUpdate)
		{
			ApplySplatData();
			m_needSplatUpdate = false;
		}
	}
	
	public void OverrideSplatPoint(int _x, int _z, int _channel, float _value, bool _noCheck = false)
	{
		if (!_noCheck && (_x < m_splatOpMinX || _z < m_splatOpMinZ || _x > m_splatOpMaxX || _z > m_splatOpMaxZ))
			return;
		if (_noCheck) m_needSplatUpdate = true;
		int tex = _channel / 4;
		int texChannel = _channel % 4;
		var texData = m_texData[tex];
		var index = 4 * (_x + GlobalData.c_heightmapW * _z) + texChannel;
		var current = (int)texData[index];
		int change = (int) (255 * _value);
		current += change;
		if (current > 255)
		{
			current = 255;
			int indexBase = index - texChannel; 
			for (int i = 0; i < m_texData.Length; ++i)
			{
				var texOther = m_texData[i];
				for (int j = 0; j < 4; ++j)
				{
					if (i == tex && j == texChannel) continue;
					var currentOther = (int)texOther[indexBase + j];
					currentOther -= change;
					if (currentOther < 0) currentOther = 0;
					texOther[indexBase + j] = (byte)currentOther;
				}
			}
		}
		texData[index] = (byte)current;
	}

	public void SetSplatPath(NativeishList<float3> _path, float _radius, float _increment, int _channel, float _intensity, uint _seed, float _randomness)
	{
		if (m_textures == null || m_textures.Length == 0)
			return;

		if (m_splatOpDeferCount == 0)
		{
			Debug.LogError($"SetSplat called outside of Begin/EndSplatOperations");
			return;
		}

		var channels = new NativeArray<byte>(c_SplatChannels, Allocator.TempJob, NativeArrayOptions.ClearMemory);
		if (_channel < 16)
			channels[_channel] = 255;
		_radius *= GlobalData.c_terrainXZScale;
		int r = Mathf.CeilToInt(_radius);
		int numSteps = (int)(1 / _increment) + 1;

		var job = new SplatPathJob(GlobalData.c_heightmapW, r, m_splatOpMinX, m_splatOpMinZ, m_splatOpMaxX, m_splatOpMaxZ, _radius, _increment, _intensity, channels, ref _path, m_texData, _seed, _randomness, _channel >= 16);
		job.Schedule(numSteps, 1).Complete();

		channels.Dispose();
	}

	public void SetSplatCircle(Vector3 _pos, float _radius, int _channel, float _intensity)
	{
		if (m_textures == null || m_textures.Length == 0) 
			return;

		if (m_splatOpDeferCount == 0)
		{
			Debug.LogError($"SetSplat called outside of Begin/EndSplatOperations");
			return;
		}

		_radius *= GlobalData.c_terrainXZScale;
		int r = Mathf.CeilToInt(_radius);

		int splatMapX = GlobalData.TerrainX(_pos.x);
		int splatMapZ = GlobalData.TerrainZ(_pos.z);

		int startX = Mathf.Max(m_splatOpMinX, splatMapX - r), endX = Mathf.Min(m_splatOpMaxX, splatMapX + r);
		int width = endX - startX;
		if (width <= 0)
			return;
		int startZ = Mathf.Max(m_splatOpMinZ, splatMapZ - r), endZ = Mathf.Min(m_splatOpMaxZ, splatMapZ + r);
		int height = endZ - startZ;
		if (height <= 0)
			return;

		var channels = new NativeArray<byte>(c_SplatChannels, Allocator.TempJob, NativeArrayOptions.ClearMemory);
		channels[_channel] = 255;

		var job = new SplatJob(new int2(splatMapX, splatMapZ), _radius, _intensity, channels, width, new int2(startX, startZ), GlobalData.c_heightmapW, m_texData);
		job.Schedule(width * height, 256).Complete();

		channels.Dispose();
	}

	public void SetSplatCircleBedrock(Vector3 _pos, float _radius, float _intensity)
	{
		if (m_textures == null || m_textures.Length == 0)
			return;

		if (m_splatOpDeferCount == 0)
		{
			Debug.LogError($"SetSplat called outside of Begin/EndSplatOperations");
			return;
		}

		_radius *= GlobalData.c_terrainXZScale;
		int r = Mathf.CeilToInt(_radius);

		int splatMapX = GlobalData.TerrainX(_pos.x);
		int splatMapZ = GlobalData.TerrainZ(_pos.z);

		int startX = Mathf.Max(m_splatOpMinX, splatMapX - r), endX = Mathf.Min(m_splatOpMaxX, splatMapX + r);
		int width = endX - startX;
		if (width <= 0)
			return;
		int startZ = Mathf.Max(m_splatOpMinZ, splatMapZ - r), endZ = Mathf.Min(m_splatOpMaxZ, splatMapZ + r);
		int height = endZ - startZ;
		if (height <= 0)
			return;

		var job = new SplatBedrockJob(new int2(splatMapX, splatMapZ), _radius, _intensity, width, new int2(startX, startZ), GlobalData.c_heightmapW, m_texData);
		job.Schedule(width * height, 256).Complete();
	}

	[BurstCompile]
	public struct SplatPathJob : IJobParallelFor
	{
		[ReadOnly] public int m_splatMapWidth;
		[ReadOnly] public int m_intRadius;
		[ReadOnly] public int m_splatOpMinX;
		[ReadOnly] public int m_splatOpMinZ;
		[ReadOnly] public int m_splatOpMaxX;
		[ReadOnly] public int m_splatOpMaxZ;
		[ReadOnly] public float m_radius;
		[ReadOnly] public float m_radiusSqr;
		[ReadOnly] public float m_tStep;
		[ReadOnly] public float m_intensity;
		[ReadOnly] public NativeArray<byte> m_channels;
		[ReadOnly] public NativeArray<float3> m_path;
		[ReadOnly] public int m_pathLength;
		[ReadOnly] public uint m_seed;
		[ReadOnly] public float m_randomness;
		[ReadOnly] NativeArray<byte> m_bedrock;
		[ReadOnly] bool m_isBedrock;

		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_tex1;
		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_tex2;
		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_tex3;
		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_tex4;

		public SplatPathJob(int _smWidth, int _iRad, int _minX, int _minZ, int _maxX, int _maxZ, float _r, float _tStep, float _intensity, NativeArray<byte> _channels, ref NativeishList<float3> _path, NativeArray<NativeArray<byte>> _texData, uint _seed, float _randomness, bool _isBedrock)
		{
			m_bedrock = Me.m_bedrockData;
			m_isBedrock = _isBedrock;
			m_splatMapWidth = _smWidth;
			m_intRadius = _iRad;
			m_splatOpMinX = _minX;
			m_splatOpMinZ = _minZ;
			m_splatOpMaxX = _maxX;
			m_splatOpMaxZ = _maxZ;
			m_radius = _r;
			m_radiusSqr = m_radius * m_radius;
			m_tStep = _tStep;
			m_intensity = _intensity / m_radiusSqr;
			m_channels = _channels;
			m_path = _path.GetBackingArray();
			m_pathLength = _path.Length;
			m_tex1 = _texData[0];
			m_tex2 = _texData[1];
			m_tex3 = _texData[2];
			m_tex4 = _texData.Length > 3 ? _texData[3] : default;
			m_seed = _seed;
			m_randomness = _randomness;
		}

		public void Execute(int index)
		{
			float t = index * m_tStep;
			var pos = CatmullRom(m_path, m_pathLength, t);
			uint seed = (uint)(t * 100000 + m_seed);
			pos.x += (Utility.XorShift01(ref seed) * 2 - 1) * m_randomness;
			pos.y += (Utility.XorShift01(ref seed) * 2 - 1) * m_randomness;
			
			float splatMapXf = GlobalData.TerrainXf(pos.x);
			float splatMapZf = GlobalData.TerrainZf(pos.y);
			
			int splatMapX = (int)splatMapXf, splatMapZ = (int)splatMapZf;

			int startX = Mathf.Max(m_splatOpMinX, splatMapX - m_intRadius), endX = Mathf.Min(m_splatOpMaxX, splatMapX + m_intRadius);
			int width = endX - startX;
			if (width <= 0)
				return;
			int startZ = Mathf.Max(m_splatOpMinZ, splatMapZ - m_intRadius), endZ = Mathf.Min(m_splatOpMaxZ, splatMapZ + m_intRadius);
			int height = endZ - startZ;
			if (height <= 0)
				return;
			if (m_isBedrock)
				SplatBedrock(splatMapXf, splatMapZf, startX, startZ, width, height);
			else
				Splat(splatMapXf, splatMapZf, startX, startZ, width, height);
		}

		void SplatBedrock(float _centrex, float _centrez, int _minX, int _minZ, int _width, int _height)
		{
			int ind = (_minX + _minZ * m_splatMapWidth) * 4;
			for (int z = _minZ; z < _height + _minZ; ++z)
			{
				for (int x = _minX; x < _width + _minX; ++x)
				{
					float dxSqrd = _centrex - x;
					dxSqrd *= dxSqrd;
					float dzSqrd = _centrez - z;
					dzSqrd *= dzSqrd;
					var radialIntensity = m_radiusSqr - (dxSqrd + dzSqrd);
					if (radialIntensity <= 0)
					{
						ind += 4;
						continue;
					}
					var blend = m_intensity * radialIntensity;
					int bedInd = ind >> 2;
					for (int c = 0; c < 4; ++c, ++ind)
					{
						byte c1 = m_bedrock[bedInd] == c + 1 + 4 * 0 ? (byte) 255 : (byte) 0;
						byte c2 = m_bedrock[bedInd] == c + 1 + 4 * 1 ? (byte) 255 : (byte) 0;
						byte c3 = m_bedrock[bedInd] == c + 1 + 4 * 2 ? (byte) 255 : (byte) 0;
						byte c4 = m_bedrock[bedInd] == c + 1 + 4 * 3 ? (byte) 255 : (byte) 0;
						m_tex1[ind] = (byte) math.lerp(m_tex1[ind], c1, blend);
						m_tex2[ind] = (byte) math.lerp(m_tex2[ind], c2, blend);
						m_tex3[ind] = (byte) math.lerp(m_tex3[ind], c3, blend);
						m_tex4[ind] = (byte) math.lerp(m_tex4[ind], c4, blend);
					}
				}
				ind += (m_splatMapWidth - _width) * 4;
			}
		}

		public void Splat(float _centrex, float _centrez, int _minX, int _minZ, int _width, int _height)
		{
			int ind = (_minX + _minZ * m_splatMapWidth) * 4;
			for (int z = _minZ; z < _height + _minZ; ++z)
			{
				for (int x = _minX; x < _width + _minX; ++x)
				{
					float dxSqrd = _centrex - x; dxSqrd *= dxSqrd;
					float dzSqrd = _centrez - z; dzSqrd *= dzSqrd;
					var radialIntensity = m_radiusSqr - (dxSqrd + dzSqrd);
					if (radialIntensity <= 0)
					{
						ind += 4;
						continue;
					}
					var blend = m_intensity * radialIntensity;
					for (int c = 0; c < 4; ++c)
					{
						m_tex1[ind] = (byte)math.lerp(m_tex1[ind], m_channels[c], blend);
						m_tex2[ind] = (byte)math.lerp(m_tex2[ind], m_channels[4 + c], blend);
						m_tex3[ind] = (byte)math.lerp(m_tex3[ind], m_channels[8 + c], blend);
						if (m_tex4.IsCreated)
							m_tex4[ind] = (byte)math.lerp(m_tex4[ind], m_channels[12 + c], blend);
						ind++;
					}
				}
				ind += (m_splatMapWidth - _width) * 4;
			}
		}

		public readonly float2 CatmullRom(NativeArray<float3> _list, int _listLength, float _t)
		{
			_t *= (_listLength - 4);
			int index = (int)_t;
			index = Mathf.Clamp(index, 0, (_listLength - 4));
			_t -= index;
			index += 1; // index is 1-based in smoothed lists

			int countM1 = _listLength - 1;
			if (index < 0)
			{
				index = 0;
				_t = 0;
			}
			if (index > countM1)
			{
				index = countM1;
			}
			float t1 = _t;
			float t2 = t1 * t1;
			float t3 = t2 * t1;
			int indexM1 = index - 1;
			if (indexM1 < 0) indexM1 = 0;
			int indexP1 = index + 1;
			if (indexP1 > countM1) indexP1 = countM1;
			int indexP2 = index + 2;
			if (indexP2 > countM1) indexP2 = countM1;
			const float Tau = 0.5f;//PathSet.s_tension;
			float kTt1 = Tau * t1, kTt2 = Tau * t2, kTt3 = Tau * t3;
			float k3mTt2 = t2 + t2 + t2 - kTt2, k2mTt3 = t3 + t3 - kTt3;
			float mM1 = kTt2 + kTt2 - kTt1 - kTt3;//(-t1 + 2 * t2 - t3) * Tau;
			float m0 = 1 - k3mTt2 + k2mTt3;//1 + (Tau - 3) * t2 + (2 - Tau) * t3;
			float mP1 = kTt1 + k3mTt2 - kTt2 - k2mTt3;//Tau * t1 + (3 - Tau * 2) * t2 + (Tau - 2) * t3;
			float mP2 = kTt3 - kTt2;//(-t2 + t3) * Tau;
			var lM1 = _list[indexM1];
			var l0 = _list[index];
			var lP1 = _list[indexP1];
			var lP2 = _list[indexP2];
			var cr = new float2(lM1.x * mM1 + l0.x * m0 + lP1.x * mP1 + lP2.x * mP2,
								lM1.z * mM1 + l0.z * m0 + lP1.z * mP1 + lP2.z * mP2);
			return cr;
		}
	}

	[BurstCompile]
	public struct SplatJob : IJobParallelFor
	{
		[ReadOnly] public int2 m_centre;
		[ReadOnly] public float m_radius;
		[ReadOnly] public float m_radiusSqrd;
		[ReadOnly] public int m_columns;
		[ReadOnly] public float m_intensity;
		[ReadOnly] public int2 m_splatMin;
		[ReadOnly] public int m_splatMapWidth;

		[ReadOnly] public NativeArray<byte> m_channelValues;

		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_tex1;
		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_tex2;
		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_tex3;
		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_tex4;

		public SplatJob(int2 _centre, float _radius, float _intensity, NativeArray<byte> _channels, int _cols, int2 _splatMin, int _splatMapWidth, NativeArray<NativeArray<byte>> _splats)
		{
			m_centre = _centre;
			m_radius = _radius;
			m_radiusSqrd = m_radius * m_radius;
			m_columns = _cols;
			m_intensity = _intensity / m_radiusSqrd;
			m_channelValues = _channels;
			m_splatMin = _splatMin;
			m_splatMapWidth = _splatMapWidth;
			m_tex1 = _splats[0];
			m_tex2 = _splats[1];
			m_tex3 = _splats[2];
			m_tex4 = _splats[3];
		}

		public void Execute(int _index)
		{
			int x = _index % m_columns + m_splatMin.x;
			int z = _index / m_columns + m_splatMin.y;
			float dxSqrd = m_centre.x - x; dxSqrd *= dxSqrd;
			float dzSqrd = m_centre.y - z; dzSqrd *= dzSqrd;
			var radialIntensity = m_radiusSqrd - (dxSqrd + dzSqrd);
			if (radialIntensity <= 0)
				return;
			var blend = m_intensity * radialIntensity;
			for (int c = 0; c < 4; ++c)
			{
				int ind = (x + z * m_splatMapWidth) * 4 + c;
				m_tex1[ind] = (byte)math.lerp(m_tex1[ind], m_channelValues[c], blend);
				m_tex2[ind] = (byte)math.lerp(m_tex2[ind], m_channelValues[4 + c], blend);
				m_tex3[ind] = (byte)math.lerp(m_tex3[ind], m_channelValues[8 + c], blend);
				m_tex4[ind] = (byte)math.lerp(m_tex4[ind], m_channelValues[12 + c], blend);
			}
		}
	}

	[BurstCompile]
	public struct SplatBedrockJob : IJobParallelFor
	{
		[ReadOnly] public int2 m_centre;
		[ReadOnly] public float m_radius;
		[ReadOnly] public float m_radiusSqrd;
		[ReadOnly] public int m_columns;
		[ReadOnly] public float m_intensity;
		[ReadOnly] public int2 m_splatMin;
		[ReadOnly] public int m_splatMapWidth;

		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_tex1;
		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_tex2;
		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_tex3;
		[NativeDisableContainerSafetyRestriction]
		public NativeArray<byte> m_tex4;
		
		[ReadOnly] public NativeArray<byte> m_bedrock;

		public SplatBedrockJob(int2 _centre, float _radius, float _intensity, int _cols, int2 _splatMin, int _splatMapWidth, NativeArray<NativeArray<byte>> _splats)
		{
			m_bedrock = Me.m_bedrockData;
			m_centre = _centre;
			m_radius = _radius;
			m_radiusSqrd = m_radius * m_radius;
			m_columns = _cols;
			m_intensity = _intensity / m_radiusSqrd;
			m_splatMin = _splatMin;
			m_splatMapWidth = _splatMapWidth;
			m_tex1 = _splats[0];
			m_tex2 = _splats[1];
			m_tex3 = _splats[2];
			m_tex4 = _splats[3];
		}

		public void Execute(int _index)
		{
			int x = _index % m_columns + m_splatMin.x;
			int z = _index / m_columns + m_splatMin.y;
			float dxSqrd = m_centre.x - x; dxSqrd *= dxSqrd;
			float dzSqrd = m_centre.y - z; dzSqrd *= dzSqrd;
			var radialIntensity = m_radiusSqrd - (dxSqrd + dzSqrd);
			if (radialIntensity <= 0)
				return;
			var blend = m_intensity * radialIntensity;
			int bedInd = x + z * m_splatMapWidth;
			for (int c = 0, ind = bedInd * 4; c < 4; ++c, ++ind)
			{
				byte c1 = m_bedrock[bedInd] == c + 1 + 4 * 0 ? (byte)255 : (byte) 0;
				byte c2 = m_bedrock[bedInd] == c + 1 + 4 * 1 ? (byte) 255 : (byte) 0;
				byte c3 = m_bedrock[bedInd] == c + 1 + 4 * 2 ? (byte) 255 : (byte) 0;
				byte c4 = m_bedrock[bedInd] == c + 1 + 4 * 3 ? (byte) 255 : (byte) 0;
				m_tex1[ind] = (byte)math.lerp(m_tex1[ind], c1, blend);
				m_tex2[ind] = (byte)math.lerp(m_tex2[ind], c2, blend);
				m_tex3[ind] = (byte)math.lerp(m_tex3[ind], c3, blend);
				m_tex4[ind] = (byte)math.lerp(m_tex4[ind], c4, blend);
			}
		}
	}
	
	void SetGrassColourAndHeightData()
	{
		//GrassDetails
		if (m_grassColoursAndPresence == null || m_grassColoursAndPresence.Length == 0) return;
		var clrs = new Vector4[m_grassColoursAndPresence.Length];
		var heights = new Vector4[m_grassColoursAndPresence.Length];
		var heights2 = new Vector4[m_grassColoursAndPresence.Length];
		for (int i = 0; i < clrs.Length; ++i)
		{
			clrs[i] = m_grassColoursAndPresence[i].m_colour;
			heights[i] = m_grassColoursAndPresence[i].m_presence;
			heights2[i] = m_grassColoursAndPresence[i].m_presence2;
		}
		Shader.SetGlobalVectorArray("_GrassColours", clrs);
		Shader.SetGlobalVectorArray("_GrassHeights", heights);
		Shader.SetGlobalVectorArray("_GrassHeights2", heights2);
	}

#if !SEPARATE_DETAIL_MAP
	// Detail Map is no longer used, detail (grass) is driven directly from terrain Splat data
	void CreateSplatWorking()
	{
		SetGrassColourAndHeightData();
	}
	public void BeginDetailOps() {}
	public void ResetDetailArea(Vector3 _min, Vector3 _max) {}
	void CheckDeferredDetailRects() {}
	public void FillDetailRect(int _x, int _y, int _w, int _h, byte _value) {}
	public void FillDetailRadial(int _x, int _y, int _radius, byte _value) {}
	public void EndDetailOps() {}
	private void ApplyTerrainTextures() {}
#else

	public Material[] m_terrainMaterials;
	void CreateSplatWorking() {
#if true
		if (m_textures == null || m_textures.Length == 0)
			return;
		if (m_TerrainDetailMapWorking != null) return;
		const int c_detailMapSize = GlobalData.c_heightmapW;
		m_TerrainDetailMapWorking = new Texture2D(c_detailMapSize, c_detailMapSize, TextureFormat.R8, false);
		m_TerrainDetailMapWorking.filterMode = FilterMode.Point;
		var detail = new NativeArray<byte>(c_detailMapSize * c_detailMapSize, Allocator.Persistent, NativeArrayOptions.UninitializedMemory);
		int maxChannel;
		byte maxVal;
		byte val;
		int ind;
		for (int z = 0; z < c_detailMapSize; ++z)
		{
			for (int x = 0; x < c_detailMapSize; ++x)
			{
				maxChannel = 0;
				maxVal = 0;
				for (int i = 0; i < 4; ++i)
				{
					ind = 4 * (c_detailMapSize * z + x) + i;
					for (int j = 0; j < m_texData.Length; ++j)
					{
						val = m_texData[j][ind];
						if (val > maxVal)
						{
							maxChannel = i + 4 * j;
							maxVal = val;
						}
					}
				}
				//GrassDetails
				detail[x + z * c_detailMapSize] = (byte)(maxChannel * 8 + 8);
				//GrassDetails
			}
		}
		m_TerrainDetailMapWorking.LoadRawTextureData(detail);
		m_TerrainDetailMapWorking.Apply(false, false);
		m_rawDetailOriginal = detail;
		SetGrassColourAndHeightData();
#else
		if (m_TerrainDetailMapSource == null) return;
		if (m_TerrainDetailMapWorking != null) return;
		m_TerrainDetailMapWorking = new Texture2D(m_TerrainDetailMapSource.width, m_TerrainDetailMapSource.height, m_TerrainDetailMapSource.format, false);
		m_TerrainDetailMapWorking.LoadRawTextureData(m_TerrainDetailMapSource.GetRawTextureData());
		m_TerrainDetailMapWorking.Apply(false, false);
		m_rawDetailOriginal = m_TerrainDetailMapSource.GetRawTextureData<byte>();
#endif
	}
	Unity.Collections.NativeArray<byte> m_rawDetailOriginal;
	Unity.Collections.NativeArray<byte> m_rawDetail;
	int m_rawDetailStride, m_rawDetailPixelStride;
	int m_rawDetailLockLevel = 0;
	public void BeginDetailOps() {
		CreateSplatWorking();
		++ m_rawDetailLockLevel;
		if (m_rawDetailLockLevel > 1) return;
		if (m_TerrainDetailMapWorking == null) return;
		m_rawDetail = m_TerrainDetailMapWorking.GetRawTextureData<byte>();
		m_rawDetailPixelStride = 1;
		m_rawDetailStride = m_TerrainDetailMapWorking.width * m_rawDetailPixelStride;
	}

	public void ResetDetailArea(Vector3 _min, Vector3 _max)
	{
		int ox = GlobalData.TerrainX(_min.x), oz = GlobalData.TerrainZ(_min.z);
		int w = GlobalData.TerrainXc(_max.x) - ox, h = GlobalData.TerrainZc(_max.z) - oz;
		BeginDetailOps();
		int offset = ox + oz * m_rawDetailStride;
		for (int z = 0; z < h; ++z)
		{
			NativeArray<byte>.Copy(m_rawDetailOriginal, offset, m_rawDetail, offset, w); 
			offset += m_rawDetailStride;
		}
		EndDetailOps();
	}

	private static System.Collections.Generic.List<int> s_deferredDetailRects = new System.Collections.Generic.List<int>();

	void CheckDeferredDetailRects()
	{
		CreateSplatWorking();
		if (m_TerrainDetailMapWorking == null) return;
		if (s_deferredDetailRects.Count > 0)
		{
			BeginDetailOps();
			for (int i = 0; i < s_deferredDetailRects.Count; i += 5)
			{
				int x = s_deferredDetailRects[i + 0], y = s_deferredDetailRects[i + 1];
				int w = s_deferredDetailRects[i + 2], h = s_deferredDetailRects[i + 3];
				byte val = (byte)s_deferredDetailRects[i + 4];
				if (h == 0)
					FillDetailRadial(x, y, w, val);
				else
					FillDetailRect(x, y, w, h, val);
			}
			EndDetailOps();
			s_deferredDetailRects.Clear();
		}
	}
	public void FillDetailRect(int _x, int _y, int _w, int _h, byte _value) {
		if (m_rawDetailStride == 0)
		{
			s_deferredDetailRects.Add(_x);
			s_deferredDetailRects.Add(_y);
			s_deferredDetailRects.Add(_w);
			s_deferredDetailRects.Add(_h);
			s_deferredDetailRects.Add(_value);
			return;
		}
		int offset = _x + _y * m_rawDetailStride;
		for (int y = 0; y  < _h; ++y) {
			for (int x = 0; x <= _w; ++x) {
				m_rawDetail[offset + x] = _value;
			}
			offset += m_rawDetailStride;
		}
	}
	public void FillDetailRadial(int _x, int _y, int _radius, byte _value) 
	{
		if (m_rawDetailStride == 0)
		{
			s_deferredDetailRects.Add(_x);
			s_deferredDetailRects.Add(_y);
			s_deferredDetailRects.Add(_radius);
			s_deferredDetailRects.Add(0);
			s_deferredDetailRects.Add(_value);
			return;
		}
		int ymin = Mathf.Max(_y-_radius, 0) - _y, ymax = Mathf.Min(_y + _radius, GlobalData.c_heightmapH-1) - _y;
		int xmin = Mathf.Max(_x - _radius, 0) - _x, xmax = Mathf.Min(_x + _radius, GlobalData.c_heightmapW - 1) - _x;
		int offset = _x + (_y + ymin) * m_rawDetailStride;
		for (int y = ymin; y  <= ymax; ++y)
		{
			for (int x = xmin; x <= xmax; ++x)
			{
				var d2 = x * x + y * y;
				if (d2 <= _radius * _radius)
					m_rawDetail[offset + x] = _value;
			}
			offset += m_rawDetailStride;
		}
	}	
	public void EndDetailOps() {
		-- m_rawDetailLockLevel;
		if (m_rawDetailLockLevel > 0) return;
		if (m_TerrainDetailMapWorking == null) return;
		m_TerrainDetailMapWorking.Apply(false, false);
		ApplyTerrainTextures();
		m_rawDetailStride = 0;
	}

	private void ApplyTerrainTextures()
	{
		foreach (var mat in m_terrainMaterials) mat.SetTexture("_DetailMap", m_TerrainDetailMapWorking);
		Shader.SetGlobalTexture("_DetailMap", m_TerrainDetailMapWorking);
	}
#endif
	
	private static DebugConsole.Command s_camrenderlayers = new ("layers", _s =>
	{
		_s = _s.ToLower();
		int toggle = -1;
		if (_s == "terrain") toggle = GameManager.c_layerTerrainBit;
		else if (_s == "default") toggle = 1;
		else if (_s == "road") toggle = 1 << GameManager.c_layerRoads;
		if (toggle != -1) Me.m_camera.cullingMask ^= toggle;
	});
	private static DebugConsole.Command s_simplelighting = new ("slight", _s =>
	{
		const string c_simpleLighting = "_SIMPLE_LIGHTING";
		var b = Shader.IsKeywordEnabled(c_simpleLighting);
		Utility.SetOrToggle(ref b, _s);
		if (b) Shader.EnableKeyword(c_simpleLighting);
		else Shader.DisableKeyword(c_simpleLighting);
	});
	private static DebugConsole.Command s_terraintex = new DebugConsole.Command("terraintex", _s => Me.ShowTerrainTextures());
	private static GameObject s_terrainTexObj = null;
	void ShowTerrainTextures()
	{
		if (s_terrainTexObj == null)
		{
			var go = new GameObject("TerrainTextures");
			var canvasObj = GameManager.Me.m_fullScreenCanvas;
			go.transform.SetParent(canvasObj.transform);
			var img = go.AddComponent<UnityEngine.UI.RawImage>();
			img.rectTransform.sizeDelta = Vector2.one * 200;
			img.rectTransform.anchoredPosition = Vector2.one * 400;
			img.texture = m_TerrainDetailMapWorking;
			s_terrainTexObj = go;
		}
		else
		{
			Destroy(s_terrainTexObj);
			s_terrainTexObj = null;
		}
	}
	
	void _CheckIfLightsCastShadows()
	{
		Light[] lights;
		lights = FindObjectsOfType(typeof(Light)) as Light[];
		foreach (Light light in lights)
		{
			if (light.shadows == LightShadows.Hard || light.shadows == LightShadows.Soft)
			{
				Shader.EnableKeyword("_DYNAMIC_SHADOWS_ENABLED");
			}
			else
			{
				Shader.DisableKeyword("_DYNAMIC_SHADOWS_ENABLED");
			}
		}
	}

	void SetShaderDefaults_Fire()
	{
		Shader.SetGlobalTexture("_DamageTexture".ShaderId(), m_DamageTex);
		Shader.SetGlobalFloat("_DamageTextureTiling".ShaderId(), m_DamageTexTiling);
		Shader.SetGlobalTexture("_HouseInteriorTexture".ShaderId(), m_HouseInteriorTex);
	}

	private static float Remap(float value, float oldStart, float oldEnd, float newStart, float newEnd)
	{
		return (value - oldStart) / (oldEnd - oldStart) * (newEnd - newStart) + newStart;
	}

#if UNITY_EDITOR || DEVELOPMENT_BUILD
	static DebugConsole.Command _giveCash = new DebugConsole.Command("renderscale", (_s) => {
		float f;
		if (float.TryParse(_s, out f)) {
			f = Mathf.Clamp( f, 0.1f, 2f );
			ScalableBufferManager.ResizeBuffers( f, f );
		}
	});
#endif

}

public static class HardwareLevels {
	public static bool ExtendedCachedShadows => false;
	public static bool RenderDepthNormals => true;
	public static float ShadowDistanceMod => 1;
	public static bool UseBlobShadows => false;
}


