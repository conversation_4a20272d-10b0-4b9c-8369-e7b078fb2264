using System;
using UnityEngine;

[Serializable]
public class ConfigurableJointFixerProperties
{
    [SerializeField]
    public ConfigurableJointMotion xMotion = ConfigurableJointMotion.Free;
    [SerializeField]
    public ConfigurableJointMotion yMotion = ConfigurableJointMotion.Free;
    [SerializeField]
    public ConfigurableJointMotion zMotion = ConfigurableJointMotion.Free;
    [SerializeField]
    public ConfigurableJointMotion angularXMotion = ConfigurableJointMotion.Free;
    [SerializeField]
    public ConfigurableJointMotion angularYMotion = ConfigurableJointMotion.Free;
    [SerializeField]
    public ConfigurableJointMotion angularZMotion = ConfigurableJointMotion.Free;
    
    [SerializeField]
    public Vector3 position = Vector3.zero;
    [SerializeField]
    public Quaternion rotation = Quaternion.identity;
    [SerializeField]
    public Vector3 axis = Vector3.zero;
    [SerializeField]
    public Vector3 secondaryAxis = Vector3.zero;
}

public class AngularLimit
{
    private float startLowerLimit = 0f;
    private float startHigherLimit = 0f;

    public string name = "";
    public float lowerLimit = 0f;
    public float higherLimit = 0f;

    public bool HasChanged
    {
        get
        {
            return (startLowerLimit != lowerLimit) || (startHigherLimit != higherLimit);
        }
    }

    public AngularLimit(string n, float sll, float shl)
    {
        name = n;
        lowerLimit = sll;
        higherLimit = shl;

        startLowerLimit = sll;
        startHigherLimit = shl;
    }
}

[RequireComponent(typeof(ConfigurableJoint))]
public class ConfigurableJointFixer : MonoBehaviour
{
    [SerializeField]
    private ConfigurableJoint joint = null;
    public ConfigurableJoint Joint
    {
        get
        {
            if (joint == null)
            {
                joint = GetComponent<ConfigurableJoint>();
            }

            return joint;
        }
    }

    [SerializeField]
    private ConfigurableJointFixerProperties jointFixerProperties = null;
    public ConfigurableJointFixerProperties JointFixerProperties
    {
        get
        {
            return jointFixerProperties;
        }
    }

    // private RagdollController m_controller = null;
    // private static Dictionary<string, AngularLimit> angularLimits = new();
    // public int m_numFrames = 0;

	// private void Awake()
	// {
    //     m_controller = GetComponentInParent<RagdollController>();
	// }

// #if UNITY_EDITOR
//     private void FixedUpdate()
//     {
//         if (m_numFrames++ > 3)
//             RunAngleLimitChecker();
//     }
//     private void RunAngleLimitChecker()
//     {
//         if ((m_controller == null) || !m_controller.debugAngularConstraints || !m_controller.didStart)
//             return;

//         if (Joint.configuredInWorldSpace)
//             return;

//         Joint.angularXMotion = ConfigurableJointMotion.Free;
//         Joint.angularYMotion = ConfigurableJointMotion.Free;
//         Joint.angularZMotion = ConfigurableJointMotion.Free;
//         JointFixerProperties.angularXMotion = ConfigurableJointMotion.Free;
//         JointFixerProperties.angularYMotion = ConfigurableJointMotion.Free;
//         JointFixerProperties.angularZMotion = ConfigurableJointMotion.Free;

//         var rot = transform.localEulerAngles;
//         var initRot = JointFixerProperties.rotation.eulerAngles;
        
//         var limitName = $"{gameObject.name},x";
//         if (!angularLimits.ContainsKey(limitName))
//             angularLimits.Add(limitName, new AngularLimit(limitName, Joint.lowAngularXLimit.limit, Joint.highAngularXLimit.limit));
//         var angularLimit = angularLimits[limitName];
//         var angle = rot.x;
//         if (angle < 0f)
//             angle = 360f + angle;
//         CheckLimits(angle, initRot.x, Joint.lowAngularXLimit.limit, Joint.highAngularXLimit.limit, ref angularLimit.lowerLimit, ref angularLimit.higherLimit);

//         limitName = $"{gameObject.name},y";
//         if (!angularLimits.ContainsKey(limitName))
//             angularLimits.Add(limitName, new AngularLimit(limitName, -Joint.angularYLimit.limit, Joint.angularYLimit.limit));
//         angularLimit = angularLimits[limitName];
//         angle = rot.y;
//         if (angle < 0f)
//             angle = 360f + angle;
//         CheckLimits(angle, initRot.y, -Joint.angularYLimit.limit, Joint.angularYLimit.limit, ref angularLimit.lowerLimit, ref angularLimit.higherLimit);

//         limitName = $"{gameObject.name},z";
//         if (!angularLimits.ContainsKey(limitName))
//             angularLimits.Add(limitName, new AngularLimit(limitName, -Joint.angularZLimit.limit, Joint.angularZLimit.limit));
//         angularLimit = angularLimits[limitName];
//         angle = rot.z;
//         if (angle < 0f)
//             angle = 360f + angle;
//         CheckLimits(angle, initRot.z, -Joint.angularZLimit.limit, Joint.angularZLimit.limit, ref angularLimit.lowerLimit, ref angularLimit.higherLimit);

//         if (Input.GetKey(KeyCode.S))
//         {
//             var str = "";
//             int changedCount = 0;
//             foreach (var limit in angularLimits)
//             {
//                 var val = limit.Value;
//                 var changed = val.HasChanged ? "changed" : "ok";
//                 changedCount += val.HasChanged ? 1 : 0;
//                 str += $"{limit.Key},{val.lowerLimit},{val.higherLimit} -> {changed}\n";
//             }
//             str += $"Changed = {changedCount}\n";
//             str = str[..^1];
//             var path = Path.Combine(Application.persistentDataPath, $"{m_controller.transform.parent.name}_angularLimits.txt");
//             File.WriteAllText(path, str);
//         }
//     }

//     private void CheckLimits(float targetAngle, float defaultAngle, float lowLimit, float highLimit, ref float lowerLimit, ref float higherLimit)
//     {
//         if (lowLimit > 0f)
//         {
//             Debug.LogError($"Angular Limits Error: Low limit {lowLimit} is > 0!");
//         }
//         if (highLimit < 0f)
//         {
//             Debug.LogError($"Angular Limits Error: High limit {highLimit} is < 0!");
//         }

//         if (Utility.AngleBetween(targetAngle, defaultAngle-180f, defaultAngle))
//         {
//             if (!Utility.AngleBetween(targetAngle, defaultAngle+lowLimit, defaultAngle))
//             {
//                 float min = Mathf.Repeat(targetAngle - defaultAngle, 360f) - 360;
//                 min = Mathf.Round(min);
//                 if (min < lowerLimit)
//                     lowerLimit = min;
//             }
//         }
//         else if (Utility.AngleBetween(targetAngle, defaultAngle, defaultAngle+180f))
//         {
//             if (!Utility.AngleBetween(targetAngle, defaultAngle, defaultAngle+highLimit))
//             {
//                 float max = Mathf.Repeat(targetAngle - defaultAngle, 360f);
//                 max = Mathf.Round(max);
//                 if (max > higherLimit)
//                     higherLimit = max;
//             }
//         }
//         else
//         {
//             Debug.LogError($"Angular Limits Error: Angle {targetAngle} is in no range!");
//         }
// 	}
// #endif

	public void DoJointResetRoutine()
    {
        FreeJointMotion();
        ResetJoint();
        RestoreJointMotion();
    }

    public void SetupJointFixerProperties()
    {
        if (jointFixerProperties == null)
        {
            jointFixerProperties = new ConfigurableJointFixerProperties();
        }

        jointFixerProperties.xMotion = Joint.xMotion;
        jointFixerProperties.yMotion = Joint.yMotion;
        jointFixerProperties.zMotion = Joint.zMotion;
        jointFixerProperties.angularXMotion = Joint.angularXMotion;
        jointFixerProperties.angularYMotion = Joint.angularYMotion;
        jointFixerProperties.angularZMotion = Joint.angularZMotion;
        jointFixerProperties.position = Joint.configuredInWorldSpace ? Joint.transform.position : Joint.transform.localPosition;
        jointFixerProperties.rotation = Joint.configuredInWorldSpace ? Joint.transform.rotation : Joint.transform.localRotation;
        jointFixerProperties.axis = Joint.axis;
        jointFixerProperties.secondaryAxis = Joint.secondaryAxis;
    }

    private void FreeJointMotion()
    {
        Joint.xMotion = ConfigurableJointMotion.Free;
        Joint.yMotion = ConfigurableJointMotion.Free;
        Joint.zMotion = ConfigurableJointMotion.Free;
        Joint.angularXMotion = ConfigurableJointMotion.Free;
        Joint.angularYMotion = ConfigurableJointMotion.Free;
        Joint.angularZMotion = ConfigurableJointMotion.Free;
    }

    private void ResetJoint()
    {
        Joint.autoConfigureConnectedAnchor = false;
        if (Joint.configuredInWorldSpace)
        {
            if (Joint.connectedBody != null)
            {
                Joint.transform.position = JointFixerProperties.position;
            }
            Joint.transform.rotation = JointFixerProperties.rotation;
        }
        else
        {
            if (Joint.connectedBody != null)
            {
                Joint.transform.localPosition = JointFixerProperties.position;
            }
            Joint.transform.localRotation = JointFixerProperties.rotation;
        }
        Joint.axis = JointFixerProperties.axis;
        Joint.secondaryAxis = JointFixerProperties.secondaryAxis;
        Joint.autoConfigureConnectedAnchor = true;
    }

    private void RestoreJointMotion()
    {
        Joint.xMotion = JointFixerProperties.xMotion;
        Joint.yMotion = JointFixerProperties.yMotion;
        Joint.zMotion = JointFixerProperties.zMotion;
        Joint.angularXMotion = JointFixerProperties.angularXMotion;
        Joint.angularYMotion = JointFixerProperties.angularYMotion;
        Joint.angularZMotion = JointFixerProperties.angularZMotion;
    }
}
