using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public static class GateOpenerState
{
    private static List<Vector3> s_gateStates => GameManager.Me.m_state.m_gateStates;

    public static bool GetState(Vector3 _pos)
    {
        for (int i = 0; i < s_gateStates.Count; ++i)
        {
            var state = s_gateStates[i];
            if ((state - _pos).xzSqrMagnitude() < 4.0f * 4.0f)
            {
                return state.y > 0;
            }
        }
        return false;
    }

    public static void SetState(Vector3 _pos, bool _newState)
    {
        _pos.y = _newState ? 1 : 0;
        for (int i = 0; i < s_gateStates.Count; ++i)
        {
            var state = s_gateStates[i];
            if ((state - _pos).xzSqrMagnitude() < 4.0f * 4.0f)
            {
                state.y = _pos.y;
                s_gateStates[i] = state;
                return;
            }
        }
        s_gateStates.Add(_pos);
    }
}

public class GateOpener : MonoBehaviour, ICharacterObjectInteract
{
    private static List<GateOpener> s_gateOpeners = new(); public static List<GateOpener> GateOpeners => s_gateOpeners;
    private static void Register(GateOpener _gate) => s_gateOpeners.Add(_gate);
    private static void Unregister(GateOpener _gate) => s_gateOpeners.Remove(_gate);

    public static GateOpener NearestToPoint(Vector3 _pos)
    {
        GateOpener nearest = null;
        float nearestDist = float.MaxValue;
        for (int i = 0; i < s_gateOpeners.Count; ++i)
        {
            var gate = s_gateOpeners[i];
            var dist = (gate.transform.position - _pos).xzSqrMagnitude();
            if (dist < nearestDist)
            {
                nearest = gate;
                nearestDist = dist;
            }
        }
        return nearest;
    }

    public static GateOpener ChooseBestGateFromPoints(Vector3 _p1, Vector3 _p2, bool _onlyConsiderRoadGates = false)
    {
        // attempt to find the best choice of gate between two points
        // the current scheme is to find the least sum of squared distances since that will tend towards gates between the two points
        GateOpener nearest = null;
        float nearestDist = float.MaxValue;
        for (int i = 0; i < s_gateOpeners.Count; ++i)
        {
            var gate = s_gateOpeners[i];
            if (_onlyConsiderRoadGates && gate.m_otherPath.Set.IsRoad == false)
                continue;
            var dist1 = (gate.transform.position - _p1).xzSqrMagnitude();
            var dist2 = (gate.transform.position - _p2).xzSqrMagnitude();
            var dist = dist1 + dist2;
            if (dist < nearestDist)
            {
                nearest = gate;
                nearestDist = dist;
            }
        }
        return nearest;
    }

    public static void ToggleAllGatesInUnlockedRegions(bool open)
    {
        for (int i = 0; i < s_gateOpeners.Count; ++i)
        {
            var gate = s_gateOpeners[i];
            if (gate != null && gate.IsInUnlockedRegion())
            {
                gate.ForceOpenState(open);
            }
        }
    }

    private static DebugConsole.Command s_toggleGateCmd = new ("togglegate", _s => ToggleGate(_s));

    public static void ToggleGate(string _s)
    {
        var bits = _s.Split(',');
        if (bits.Length == 3)
        {
            var x = floatinv.Parse(bits[0]);
            var z = floatinv.Parse(bits[1]);
            var open = bits[2][0] == 't' || bits[2][0] == 'T';
            ToggleGateAt(new Vector3(x, 0, z), open);
        }
    }

    public bool IsInUnlockedRegion()
    {
        return DistrictManager.Me.IsWithinDistrictBounds(transform.position, true);
    }
    
    public static void ToggleGateAt(Vector3 _pos, bool _open)
    {
        var gate = NearestToPoint(_pos);
        if (gate != null)
            gate.ForceOpenState(_open);
    }
    
    public static bool GetGateStateAt(Vector3 _pos)
    {   
        var gate = NearestToPoint(_pos);
        return gate != null && gate.IsGateOpen;
    }

    private bool m_canBeClosed = true;
    
    private GameObject m_gateLeft, m_gateRight;
    private BoxCollider m_gateLeftCollider, m_gateRightCollider;
    
    private bool m_autoOpen = false;
    private bool m_manualOpenState = false;
    private float m_gateClosedAngle;
    private BoxCollider m_trigger;
    private Balloon m_balloon;
    private Transform m_balloonHolder;
    private float m_lastInTriggerTime = 0, m_openState = 0;
    private BuildingNavBlocker m_navBlockerLeft;
    private BuildingNavBlocker m_navBlockerRight;
    private PathManager.Path m_thisPath;
    private PathManager.Path m_otherPath;
    private float m_otherPathT;
    private int m_lastDayStateChecked;

    public void SetPaths(PathManager.Path _this, PathManager.Path _other)
    {
        m_thisPath = _this;
        m_otherPath = _other;
        (_, m_otherPathT, _) = _other.GetClosestPointToPoint(transform.position, false, 10*10);
    }

    public PathManager.Path Path => m_thisPath;
    public PathManager.Path OtherPath => m_otherPath;
    
    public GlobalData.NavCostTypes NavState => m_autoOpen ? GlobalData.NavCostTypes.Gate : (m_manualOpenState ? GlobalData.NavCostTypes.Road : GlobalData.NavCostTypes.NoNav);
    
    private int m_queueSlotsBitmap = 0;

    public int ClaimQueueSlot(int _direction)
    {
        int baseIndex = _direction > 0 ? 0 : 16;
        for (int i = 0, bm = 1 << baseIndex; i < 15; ++i, bm <<= 1)
        {
            if ((m_queueSlotsBitmap & bm) == 0)
            {
                m_queueSlotsBitmap |= bm;
                return 16 + baseIndex + i;
            }
        }
        return 0;
    }
    
    public void ReleaseQueueSlot(int _index)
    {
        if (_index < 16) return;
        m_queueSlotsBitmap &= ~(1 << (_index - 16));
    }

    public void LoadQueueSlot(int _index)
    {
        if (_index < 16) return;
        m_queueSlotsBitmap |= 1 << (_index - 16);
    }

    public Vector3 QueueSlotPosition(int _index, int _direction) => QueueSlotPosition(_direction > 0 ? 16 + _index : 32 + _index);
    public Vector3 QueueSlotPosition(int _index)
    {
        if (_index < 16) return default;
        int direction = ((_index - 16) / 16) * 2 - 1;
        _index &= 15;
        const int c_queueSlotStepsPerIndex = 6;
        var t = m_otherPathT + m_otherPath.LocStep * (1 + _index) * 2 * direction * c_queueSlotStepsPerIndex;
        return m_otherPath.GetPoint(t);
    }

    public int QueueDirectionFromPosition(Vector3 _pos) => (QueueSlotPosition(1, 1) - _pos).xzSqrMagnitude() < (QueueSlotPosition(1, -1) - _pos).xzSqrMagnitude() ? 1 : -1;

    [SerializeField] private float m_stopDistanceToGate = 6.0f;

    private void Awake()
    {     
        Register(this);
        m_gateLeft = transform.GetChild(0).gameObject;
        m_gateRight = transform.GetChild(1).gameObject;
        m_gateLeftCollider = m_gateLeft.AddComponent<BoxCollider>();
        m_gateRightCollider = m_gateRight.AddComponent<BoxCollider>();
    }
    
    private void Start()
    {
        var leftGatePos = m_gateLeft.transform.position;
        var lToR = m_gateRight.transform.position - leftGatePos;
        lToR.y = 0;
        m_gateClosedAngle = 180 - Mathf.Atan2(lToR.z, lToR.x) * Mathf.Rad2Deg;
        // add trigger region
        m_trigger = gameObject.AddComponent<BoxCollider>();
        m_trigger.isTrigger = true;
        const float height = .1f;
        m_trigger.center = Vector3.up * height;
        m_trigger.size = new Vector3(lToR.magnitude, height * 2f, 10);
        
        m_gateLeft.AddComponent<ClickListener>().SetListener(() => ToggleGate(true));
        m_gateRight.AddComponent<ClickListener>().SetListener(() => ToggleGate(true));
        gameObject.AddComponent<ClickListener>().SetListener(() => ToggleGate(true));
        
        var centreOfGate = leftGatePos + lToR * 0.5f;
        Vector3 lToRPerp1 = Vector3.Cross(lToR, Vector3.up).normalized;
        Vector3 lToRPerp2 = lToRPerp1 * -1;
        
        m_navBlockerLeft = m_gateLeft.GetComponentInChildren<BuildingNavBlocker>();
        m_navBlockerRight = m_gateRight.GetComponentInChildren<BuildingNavBlocker>();
        if (m_navBlockerLeft != null)
        {
            const float c_roundingAdjustment = 1.1f;
            var blockerHolder = new GameObject("NavBlocker Holder L");
            blockerHolder.transform.SetParent(transform);
            blockerHolder.transform.position = m_gateLeft.transform.position;
            blockerHolder.transform.rotation = m_gateLeft.transform.rotation;
            m_navBlockerLeft.transform.SetParent(blockerHolder.transform, true);
            m_navBlockerLeft.ApplyScale(m_gateLeft.transform.lossyScale * c_roundingAdjustment);
            blockerHolder = new GameObject("NavBlocker Holder R");
            blockerHolder.transform.SetParent(transform);
            blockerHolder.transform.position = m_gateRight.transform.position;
            blockerHolder.transform.rotation = m_gateRight.transform.rotation;
            m_navBlockerRight.transform.SetParent(blockerHolder.transform, true);
            m_navBlockerRight.ApplyScale(m_gateRight.transform.lossyScale * c_roundingAdjustment);
        }
        
        m_manualOpenState = GateOpenerState.GetState(transform.position);
        m_openState = m_manualOpenState ? 1 : 0;
        
        UpdateGates(true);
    }
    
    void OnDestroy()
    {
        Unregister(this);
    }

    public bool IsGateOpen => m_manualOpenState;
    
    private void OnTriggerEnter(Collider other)
    {
        //if (other.gameObject.GetComponentInParent<Threat>() != null) return; // if collider is a Threat-type don't open the gate
        if (other.gameObject.GetComponentInParent<MAVehicle>() != null)
        {
            m_canBeClosed = false;
            m_lastInTriggerTime = Time.unscaledTime;
        }
    }
    
    private void OnTriggerStay(Collider other)
    {
        //if (other.gameObject.GetComponentInParent<Threat>() != null) return; // if collider is a Threat-type don't open the gate
        if (other.gameObject.GetComponentInParent<MAVehicle>() != null)
        {
            m_canBeClosed = false;
            m_lastInTriggerTime = Time.unscaledTime;
        }
    }
    
    private void OnTriggerExit(Collider other)
    {
        //if (other.gameObject.GetComponentInParent<Threat>() != null) return; // if collider is a Threat-type don't open the gate
        if (other.gameObject.GetComponentInParent<MAVehicle>() != null)
        {
            m_canBeClosed = true;
        }
    }

    private bool IsEnemyNear()
    {
        float checkDist = 50f*50f;
        var pos = transform.position;
        foreach(var creature in NGManager.Me.m_MACreatureList)
        {
            if (!creature.IsEnemy)
                continue;
            
            var d2 = (pos - creature.transform.position).xzSqrMagnitude();
            if(d2 <= checkDist)
                return true;
        }
        return false;
    }
    
    void Update()
    {
        UpdateGates(false);
    }
    void UpdateGates(bool _immediate)
    {
        if (GameManager.Me.DataReady == false) return;
        
        float target = m_manualOpenState ? 1 : 0;
        bool isInUnlockedRegion = IsInUnlockedRegion();
        if(isInUnlockedRegion)
        {
            if (m_autoOpen)
            {
                target = Time.unscaledTime - m_lastInTriggerTime < .5f ? 1 : 0;
            }
            else
            {
                CheckDayState();
                
                bool shouldBeOpen = DayNight.Me.m_isFullNight == false && IsEnemyNear() == false;
                bool balloonVisible = m_manualOpenState != shouldBeOpen && GameManager.Me.IsPossessing == false && isInUnlockedRegion;
                if (balloonVisible && m_balloon == null)
                {
                    if (m_balloonHolder == null)
                    {
                        var holder = new GameObject("Balloon Holder");
                        m_balloonHolder = holder.transform;
                        m_balloonHolder.SetParent(m_gateLeft.transform);
                        m_balloonHolder.localPosition = Vector3.up * 8;
                    }
                    m_balloon = NGBalloonManager.Me.CreateBalloon(NGBalloonManager.BalloonType.Red, m_balloonHolder, "", null);
                    m_balloon.SetText(shouldBeOpen ? "Click To Open Gates" : "Click To Close Gates");
                    m_balloon.GetComponentInChildren<NGColliderPasser>().m_onClick += _ => ToggleGate(true);
                }
                else if (balloonVisible == false && m_balloon != null)
                {
                    Destroy(m_balloon.gameObject);
                    m_balloon = null;
                }

                target = m_manualOpenState ? 1 : 0;
            }
        }
        
        m_openState = _immediate ? target : Mathf.Lerp(m_openState, target, .1f);
        const float c_swingAngle = 80;
        m_gateLeft.transform.eulerAngles = Vector3.up * (m_gateClosedAngle + m_openState * c_swingAngle);
        m_gateRight.transform.eulerAngles = Vector3.up * (m_gateClosedAngle + 180 - m_openState * c_swingAngle);
        if (m_navBlockerLeft != null)
        {
            m_navBlockerLeft.transform.parent.eulerAngles = Vector3.up * (m_gateClosedAngle + target * c_swingAngle);
            m_navBlockerRight.transform.parent.eulerAngles = Vector3.up * (m_gateClosedAngle + 180 - target * c_swingAngle);
        }
        bool open = IsOpen;
        if (m_gateLeftCollider.isTrigger != open)
            m_gateRightCollider.isTrigger = m_gateLeftCollider.isTrigger = open;
    }
    
    public bool IsOpen => m_openState * m_openState > .2f * .2f;
    
    private void ToggleGate(bool _isClick)
    {
        if (m_canBeClosed == false && IsOpen) return;
        if (_isClick && IsInUnlockedRegion() == false) return;
        //Debug.Log($"m_balloon - click for setNav");
        m_manualOpenState = !m_manualOpenState;
        if (m_balloon != null)
            m_balloon.SetText(m_manualOpenState == false ? "Click To Open Gates" : "Click To Close Gates");
        if (GameManager.Me.LoadComplete)
            AudioClipManager.Me.PlaySound(m_manualOpenState ? "PlaySound_TownGatesOpen" : "PlaySound_TownGatesClose", gameObject);
        GateOpenerState.SetState(transform.position, m_manualOpenState);
    }

    public void ForceOpenState(bool _open)
    {
        m_manualOpenState = _open;
        GateOpenerState.SetState(transform.position, m_manualOpenState);
    }

    private void CheckDayState()
    {
        var day = DayNight.Me.CurrentWorkingDay;
        var isDayDuskOrDawn = DayNight.Me.m_isFullNight == false;
        var combined = day * 2 + (isDayDuskOrDawn ? 1 : 0);
        if (m_lastDayStateChecked == combined) return;
        m_lastDayStateChecked = combined;
        if (m_manualOpenState != isDayDuskOrDawn)
            ToggleGate(false);
    }

    private bool m_interactDisabled = false;

    public string InteractType => null;

    public bool CanInteract(NGMovingObject _chr)
    { 
        if (m_interactDisabled)
            return false;
        if(GameManager.Me.GlobalInteractCheck(_chr) == false)
            return false;
         if(GameManager.Me.IsPossessed(_chr) == false)
            return false;

         return (_chr is MAHeroBase || _chr is MADog) && IsInUnlockedRegion() &&
                NGMovingObject.c_defaultInteractRange * NGMovingObject.c_defaultInteractRange >= transform.position.DistanceXZSq(_chr.transform.position);
    }

    public string GetInteractLabel(NGMovingObject _chr)
    {
        return m_manualOpenState ? "Close" : "Open";
    }

    public void DoInteract(NGMovingObject _chr)
    {
        ToggleGate(false);
    }
    
    public bool RunInteractSequence(List<FollowChild> _chr) => InteractionSequenceNode.AttemptInteraction(_chr, gameObject);

    public void EnableInteractionTriggers(bool _b) => m_interactDisabled = !_b;
    
    public float AutoInteractTime => 0;
}
