using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

[RequireComponent(typeof(ScrollRect))]
public class ScrollSound : MonoBehaviour
{
    [SerializeField] private string m_swipeSound = "";
    [SerializeField]private string m_clickSound = "PlaySound_UI_Scroll";
    [SerializeField] private float m_minSpeedThreshold = 2000.0f;
    [SerializeField] private float m_minRepeatTime = 0.2f;
    private float m_waitTime = 0.0f;
    private float m_previousSpeed = 0.0f;
    private ScrollRect m_scrollRect;
    private RectTransform m_previousItem = null;
    private float m_previousClickSpeed = 0.0f;

    void Awake()
    {
        m_scrollRect = GetComponent<ScrollRect>();
    }

    void Update()
    {
        if (GameManager.Me.IsOKToPlayUISound())
        {
            if (!string.IsNullOrEmpty(m_swipeSound))
            {
                float speed = m_scrollRect.velocity.magnitude;
                bool isSlowing = speed < m_previousSpeed;
                m_previousSpeed = speed;

                if (m_waitTime > 0.0f)
                {
                    m_waitTime -= Time.deltaTime;
                }
                else if (!isSlowing && speed > m_minSpeedThreshold)
                {
                    AudioClipManager.Me.PlaySoundOld(m_swipeSound, GameManager.Me.transform);
                    m_waitTime = m_minRepeatTime;
                }
            }
            if (!string.IsNullOrEmpty(m_clickSound))
            {
                float scrollDistance = 0.0f;
                float speed = 0.0f;

                if(m_scrollRect.horizontal)
                {
                    scrollDistance = -m_scrollRect.content.anchoredPosition.x + (GetComponent<RectTransform>().rect.width * 0.5f);
                    speed = m_scrollRect.velocity.x;
                }
                else if(m_scrollRect.vertical)
                {
                    scrollDistance = -m_scrollRect.content.anchoredPosition.y + (GetComponent<RectTransform>().rect.height * 0.5f);
                    speed = m_scrollRect.velocity.y;
                }

                if(speed.Sign() != m_previousClickSpeed.Sign())
                {
                    m_previousItem = null;
                }

                RectTransform nextItem = null;

                if(speed < 0.0f)
                {
                    for (int i = 0; i < m_scrollRect.content.childCount; i++)
                    {
                        RectTransform item = m_scrollRect.content.GetChild(i).GetComponent<RectTransform>();

                        if (item != null && item.gameObject.activeInHierarchy)
                        {
                            float pos = 0.0f;

                            if (m_scrollRect.horizontal)
                            {
                                pos = item.offsetMin.x + (item.rect.width * 0.5f);
                            }
                            else if (m_scrollRect.vertical)
                            {
                                pos = item.offsetMin.y + (item.rect.height * 0.5f);
                            }

                            if(pos > scrollDistance)
                            {
                                nextItem = item;
                                break;
                            }
                        }
                    }
                }
                else if(speed > 0.0f)
                {
                    for (int i = m_scrollRect.content.childCount-1; i >= 0; i--)
                    {
                        RectTransform item = m_scrollRect.content.GetChild(i).GetComponent<RectTransform>();

                        if (item != null && item.gameObject.activeInHierarchy)
                        {
                            float pos = 0.0f;

                            if (m_scrollRect.horizontal)
                            {
                                pos = item.offsetMin.x + (item.rect.width * 0.5f);
                            }
                            else if (m_scrollRect.vertical)
                            {
                                pos = item.offsetMin.y + (item.rect.height * 0.5f);
                            }

                            if (pos < scrollDistance)
                            {
                                nextItem = item;
                                break;
                            }
                        }
                    }
                }

                if(m_previousItem != null && m_previousItem != nextItem)
                {
                    AudioClipManager.Me.PlaySoundOld(m_clickSound, GameManager.Me.transform);
                }

                m_previousItem = nextItem;
                m_previousClickSpeed = speed;
            }
        }
    }
}
