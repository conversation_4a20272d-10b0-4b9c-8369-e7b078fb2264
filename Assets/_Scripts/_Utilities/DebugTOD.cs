using UnityEngine;
using UnityEngine.UI;

public class DebugTOD : Mono<PERSON><PERSON>leton<DebugTOD>
{
    public TMPro.TextMeshProUGUI m_days;
    public TMPro.TextMeshProUGUI m_hours;
    public TMPro.TextMeshProUGUI m_minutes;
    public Toggle m_freeze;
    
    void Start()
    {
        gameObject.SetActive(false);
        GetComponent<Canvas>().enabled = true;
    }
    
    private static DebugConsole.Command s_debugtodcmd = new ("debugtod", _s => { 
        Me.gameObject.SetActive(Utility.SetOrToggle(Me.gameObject.activeSelf, _s));
        //DebugConsole.Me.ExecuteConsole("tod=true", true);
    }, "Show the Time Of Day editor", "<bool>");

    private void ChangeTime(int _dDays, int _dHours, int _dMinutes)
    {
        var tod = GameManager.Me.m_state.m_gameTime.m_gameTime;
        var days = (int) tod;
        tod -= days;
        var totalHours = DayNight.FractionToClock(tod);
        
        days += _dDays;
        DayNight.Me.UpdateDayNightValues(days);
        
        totalHours += _dHours + _dMinutes * (1.0f / 60.0f);
        if (totalHours >= 24)
        {
            totalHours -= 24;
            days++;
        }

        tod = DayNight.ClockToFraction(totalHours);
        GameManager.Me.m_state.m_gameTime.m_gameTime = tod + days;
        if (DayNight.Me.m_overrideDayFraction >= 0)
            DayNight.Me.m_overrideDayFraction = tod;
    }

    public void OnFreezeChange()
    {
        if (DayNight.Me.m_overrideDayFraction < 0)
            DayNight.PauseTime();
        else
            DayNight.UnpauseTime();
    }
    public bool IsFrozen
    {
        get => DayNight.Me.m_overrideDayFraction >= 0;
        set
        {
            if (value)
            {
                DayNight.PauseTime();
            }
            else
            {
                DayNight.UnpauseTime();
            }
        }
    }
    public void OnDaysInc() => ChangeTime(1, 0, 0);
    public void OnDaysDec() => ChangeTime(-1, 0, 0);
    public void OnHoursInc() => ChangeTime(0, 1, 0);
    public void OnHoursDec() => ChangeTime(0, -1, 0);
    public void OnMinutesInc() => ChangeTime(0, 0, 1);
    public void OnMinutesDec() => ChangeTime(0, 0, -1);
    
    void Update()
    {
        var tod = GameManager.Me.m_state.m_gameTime.m_gameTime;
        var days = (int)tod;
        tod -= days;
        var totalHours = DayNight.FractionToClock(tod);
        var hours = (int)totalHours;
        totalHours -= hours;
        totalHours *= 60;
        var minutes = (int) totalHours;
        m_days.text = $"Day {days}";
        m_hours.text = $"{hours}";
        m_minutes.text = $"{minutes:00}";
        m_freeze.SetIsOnWithoutNotify(GameManager.Me.m_state.m_gameTime.m_gameTimeLocked);
    }

    public void GetDHM(out int days, out int hours, out int minutes)
    {
        var tod = GameManager.Me.m_state.m_gameTime.m_gameTime;
        days = (int)tod;
        tod -= days;
        var totalHours = DayNight.FractionToClock(tod);
        hours = (int)totalHours;
        totalHours -= hours;
        totalHours *= 60;
        minutes = (int) totalHours;
    }
}
