using UnityEngine;

#if UNITY_EDITOR
using UnityEditor;
#endif

public class ToGroundPosition : MonoBehaviour
{
    [SerializeField]
    private bool m_toSeaLevel = false;
    
    private void Update()
    {
        if (m_toSeaLevel)
        {
            Transform tr = transform;
            tr.position = tr.position.ToSeaLevel();
            enabled = false;
        }
        else if (GameManager.Me != null && GameManager.Me.LoadComplete)
        {
            Transform tr = transform;
            tr.position = tr.position.GroundPosition();
            enabled = false;
        }
    }

    public void ToGround()
    {
        if (GameManager.Me != null && GameManager.Me.LoadComplete)
        {
            Transform tr = transform;
            tr.position = tr.position.GroundPosition();
        }
    }

    public void ToSea()
    {  
        Transform tr = transform;
        tr.position = tr.position.ToSeaLevel();
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(ToGroundPosition))]
public class ToGroundPositionEditor : Editor
{
    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();

        var toG = target as ToGroundPosition;
        if (GUILayout.Button("To Sea Level"))
        {
            toG.ToSea();
        }
        
        if(GameManager.Me != null && GameManager.Me.LoadComplete && GUILayout.Button("To Ground Level"))
        {
            toG.ToGround();
        }
    }
}
#endif