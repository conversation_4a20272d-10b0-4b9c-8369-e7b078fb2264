using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using DG.Tweening;

//----------------------------------------------------------------------------------------
//----------------------------------------------------------------------------------------
public class CameraTransition
{
	public enum EState
	{
		WaitingForStart,
		Running,
		Finished,
	}
	
	// -- public --
	public Vector3 m_focusPos;
	public float m_transitionTime;
	public EState State { get { return m_state; } }
	public event System.Action		OnFinished;
	
	// -- private --
	protected Vector3 m_startPos;
	protected Vector3 m_endPos;
	protected Vector3 m_startRot;
	protected Vector3 m_endRot;
	protected float m_moveAmountPerSecond;
	protected EState m_state;
	protected float m_minTerrainHeightOffset = 2f;
	protected Vector3 cameraTween, cameraRotTween;
	protected float m_distanceToFocus = 50f;
	protected bool m_transitionRotation = true;
	
	//----------------------------------------------------------------------------------------
	public CameraTransition(Vector3 _focusPos, float _transitionTime, bool _resetRotation, System.Action _onFinishedCallback, float _distToFocus = 50f)
	{
		m_focusPos = _focusPos;
		m_transitionTime = _transitionTime;
		m_state = EState.WaitingForStart;
		OnFinished = _onFinishedCallback;
		m_distanceToFocus = _distToFocus;
		m_transitionRotation = _resetRotation;
	}
	
	//----------------------------------------------------------------------------------------
	public virtual void Start( out Vector3 _targetPosition, out Vector3 _targetRotation )
	{
		m_startRot = GameManager.Me.CamRot;
		if (m_transitionRotation)
		{
			m_endRot = GameManager.Me.DefaultCameraAngles;
			// rebase start rot around the circle to make sure we go the right way but end up at endRot
			var dRot = Vector3.zero;
			dRot.x = Mathf.DeltaAngle(m_startRot.x, m_endRot.x);
			dRot.y = Mathf.DeltaAngle(m_startRot.y, m_endRot.y);
			dRot.z = Mathf.DeltaAngle(m_startRot.z, m_endRot.z);
			m_startRot = m_endRot - dRot;
		}
		m_startPos = GameManager.Me.CamPos;
		m_endPos = CalculateCameraPosition(m_focusPos);
		//CameraMovement.Me.CancelCameraTransition();
		m_state = EState.Running;

		cameraTween = m_startPos;
		cameraRotTween = m_startRot;
		
		_targetRotation = GameManager.Me.CamRot;

		// If already at end position, finish immedietly
		if(m_transitionTime <= 0 || (m_startPos - m_endPos).magnitude < Mathf.Epsilon)
		{
			m_state = EState.Finished;
			_targetPosition = m_endPos;
			if(OnFinished != null)
				OnFinished();
			return;
		}
		
		DOTween.To(()=> cameraTween, x => cameraTween = x, m_endPos, m_transitionTime)
		.SetEase(Ease.InOutSine)
		.OnComplete(()=>
		{
			m_state = EState.Finished;
			if(OnFinished != null)
			OnFinished();
		});
		if (m_transitionRotation)
		{
			DOTween.To(()=> cameraRotTween, x => cameraRotTween = x, m_endRot, m_transitionTime)
			.SetEase(Ease.InOutSine);
		}
		
		_targetPosition = GameManager.Me.CamPos;
	}

	public void ForceFinish() {
		if (OnFinished != null) OnFinished();
	}

	//----------------------------------------------------------------------------------------
	public virtual void Update( out Vector3 _targetPosition, out Vector3 _targetRotation )
	{
		cameraTween = (m_state == EState.Finished) ? GameManager.Me.CamPos : cameraTween;
		
		_targetPosition = cameraTween;
		_targetRotation = cameraRotTween;//CameraMovement.Me.GetCamRot();
	}
	
	//----------------------------------------------------------------------------------------
	public void SetDistanceToFocus(float _distToFocus){
		m_distanceToFocus = _distToFocus;
	}

	protected Vector3 CalculateCameraPosition(Vector3 _focusPoint)
	{
		Vector3 camPosToFocus;
		if (m_transitionRotation)
		{
			camPosToFocus = Quaternion.Euler(m_endRot) * Vector3.forward;
		}
		else
		{
			var camPos = GameManager.Me.CamPos;
			var camFocus = GameManager.Me.CamFocus;
			camPosToFocus = (camFocus - camPos).normalized;
		}

		var camPosForFocus = _focusPoint - camPosToFocus * m_distanceToFocus;

		// clamp to terrain height
		var height = GlobalData.Me.GetRealHeight(camPosForFocus);
		camPosForFocus.y = Mathf.Max(height + m_minTerrainHeightOffset, camPosForFocus.y);
		
		return camPosForFocus;
	}


}
