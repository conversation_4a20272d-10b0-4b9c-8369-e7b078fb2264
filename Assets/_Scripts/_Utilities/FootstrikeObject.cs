using UnityEngine;

public class FootstrikeObject : MonoBehaviour
{
    private MeshRenderer m_meshRenderer;
    private Material m_mat;
    private Color m_initialCol;
    private Vector3 m_prevPos;
    
    private void Start()
    {
        m_meshRenderer = GetComponent<MeshRenderer>();
        m_mat = m_meshRenderer.material;
        m_initialCol = m_mat.color;
        m_prevPos = transform.position;
    }

    private void Update()
    {
        if (transform.position != m_prevPos)
        {
            m_prevPos = transform.position;
            SetColor();
        }
        else
        {
            FadeColor();
        }
    }
    
    private void SetColor()
    {
        m_mat.color = m_initialCol;
    }
    
    private void FadeColor()
    {
        m_mat.color = Color.Lerp(m_mat.color, m_initialCol * 0.2f, 0.04f);
    }
}
