using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class BoundsDebug : MonoBehaviour {
#if UNITY_EDITOR
	Bounds m_awakeBounds;
	void Awake() {
		m_awakeBounds = ManagedBlock.GetTotalVisualBounds(gameObject);
	}
	void OnDrawGizmos() {
		var corners = new Vector3[8+1];
		for (int j = 0; j < 2; ++j) {
			var bounds = (j == 0) ? ManagedBlock.GetTotalVisualBounds(gameObject) : m_awakeBounds;
			Gizmos.color = (j == 0) ? Color.cyan : Color.green;
			for (int i = 0; i < 8; ++i) {
				float fx = ((i >> 0) & 1) * 2.0f - 1.0f;
				float fy = ((i >> 1) & 1) * 2.0f - 1.0f;
				float fz = ((i >> 2) & 1) * 2.0f - 1.0f;
				var c = bounds.center + Vector3.right * (bounds.extents.x * fx) + Vector3.up * (bounds.extents.y * fy) + Vector3.forward * (bounds.extents.z * fz);
				Gizmos.DrawCube(c, Vector3.one * .01f);
				corners[i] = c;
				if (i == 0 && j == 0) corners[8] = c;
			}
			Gizmos.DrawLine(corners[0], corners[1]); Gizmos.DrawLine(corners[1], corners[3]); Gizmos.DrawLine(corners[3], corners[2]); Gizmos.DrawLine(corners[2], corners[0]);
			Gizmos.DrawLine(corners[4], corners[5]); Gizmos.DrawLine(corners[5], corners[7]); Gizmos.DrawLine(corners[7], corners[6]); Gizmos.DrawLine(corners[6], corners[4]);
			Gizmos.DrawLine(corners[0], corners[4]); Gizmos.DrawLine(corners[1], corners[5]); Gizmos.DrawLine(corners[2], corners[6]); Gizmos.DrawLine(corners[3], corners[7]);
		}
		Gizmos.DrawLine(corners[0], corners[8]);
	}
#endif
}
