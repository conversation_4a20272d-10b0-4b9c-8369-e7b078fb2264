using System;
using System.Collections;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.UI;
using Object = UnityEngine.Object;

public class BlockLabel : MonoBehaviour
{
    private DTDragDrawer[] m_drawers;
    private int m_drawerIndex, m_slot;
    private float m_baseScale;
    private float m_currentScale;

    private NGBlockInfo m_blockInfo;
    private PaintPotData m_paintInfo;
    private PatternData m_patternInfo;
    
    private DesignTableManager.BlockLabelMode m_currentLabelMode = DesignTableManager.BlockLabelMode.None;
    private DesignTableManager.BlockLabelMode m_nextLabelMode = DesignTableManager.BlockLabelMode.None;
    
    public GameObject[] m_items;
    public Image[] m_backgrounds;
    public TMP_Text[] m_lables;
    
    void Awake()
    {
        UpdateItems();
    }
    void Start()
    {
        transform.localPosition = Vector3.zero;
        m_baseScale = transform.localScale.x;
        m_currentScale = 0;
        transform.localScale = Vector3.zero;
    }

    public void SetDrawerDetails(DTDragDrawer[] _drawers, int _drawerIndex, int _slot)
    {
        m_drawers = _drawers;
        m_drawerIndex = _drawerIndex;
        m_slot = _slot;
    }
    
    public void SetData(object _data)
    {
        if(_data is NGBlockInfo blockInfo)
            m_blockInfo = blockInfo;
        else if(_data is PatternData patternInfo)
            m_patternInfo = patternInfo;
        else if(_data is PaintPotData paintInfo)
            m_paintInfo = paintInfo;
    }
    
    public static string GetFunctionString(GameObject _go, NGBlockInfo _blockInfo, NGDesignInterface.DesignScoreInterface _dsi)
    {
        if(_blockInfo == null)
            return null;
        
        var infos = _blockInfo.GetComponentInfos();
        var text = "";
        foreach (var info in infos)
        {
            if (string.IsNullOrEmpty(info.m_class))
                continue;
                
            var cmpInst = _go.AddComponent(info.m_classType) as BCBase;
            
            if (cmpInst != null)
            {
                cmpInst.Setup(info);
                BCBase.CombinedStat stat = null;
                cmpInst.GetCombinedValue(ref stat);
                if(stat != null)
                {
                    var val = stat.GetValue(BCBase.CombinedStat.ValueDisplay.NewBlockInfo);
                    if(val != null) text += val + "\n";
                }
            }
            Destroy(cmpInst);
        }
        return text.Trim();
    }
    
    private string GetMaterialsString(NGDesignInterface.DesignScoreInterface _dsi)
    {
        var materials = _dsi.IsProduct ? m_blockInfo.GetProductMaterials(_dsi) : m_blockInfo.BuildingMaterials;

        string matString = ""; 
        foreach (var mat in materials) 
            matString += $"{m_blockInfo.m_materialCost}x{mat.TextSprite} ";
        return matString.TrimEnd();
    }
    
    private string GetCostString(NGDesignInterface.DesignScoreInterface _dsi)
    {
        float price = 0;
        if (m_blockInfo != null)
            price = _dsi.GetPriceOfBlock(m_blockInfo);
        else if (m_paintInfo != null)
            price = m_paintInfo.m_cost;
        else if (m_patternInfo != null)
            price = m_patternInfo.m_cost;
            
        return $"<color=red>{price.ToCurrencyString(50)}</color>";
    }
    
    private string GetSellingPriceString(NGDesignInterface.DesignScoreInterface _dsi)
    {
        if (m_blockInfo == null)
            return null;
        
        var valueMod = DesignTableManager.Me.ValueModWithAddedBlock(m_blockInfo.m_prefabName);
        return $"<color=green>{valueMod.ToCurrencyString(50)}</color>";
    }
    
    public void SetLabel(DesignTableManager.BlockLabelMode _mode)
    {
        m_nextLabelMode = _mode;
    }
    
    private void SetItem(int _index, string _value, Color _borderColor)
    {
        if(_index < 0 || _index >= m_items.Length)
            return;
        
        m_items[_index].SetActive(_value.IsNullOrWhiteSpace() == false);
        m_backgrounds[_index].color = _borderColor;
        m_lables[_index].text = _value;
    }
    
    private void UpdateItems()
    {
        m_currentLabelMode = m_nextLabelMode;
        
        for(int i = 0; i < m_items.Length; i++)
        {
            m_items[i].SetActive(false);
        }
        
        if(m_currentLabelMode != DesignTableManager.BlockLabelMode.Info)
            return;
        
        var dsi = DesignTableManager.Me.DesignInterface;
        
        if(dsi.IsProduct)
        {
            SetItem(0, GetCostString(dsi), DesignTableManager.Me.m_blockLabelBorder_Costs);
            SetItem(1, GetMaterialsString(dsi), DesignTableManager.Me.m_blockLabelBorder_Costs);
            SetItem(2, GetSellingPriceString(dsi), DesignTableManager.Me.m_blockLabelBorder_Value);
            
        }
        else
        {
            SetItem(0, GetCostString(dsi), DesignTableManager.Me.m_blockLabelBorder_Costs);
            SetItem(1, GetFunctionString(gameObject, m_blockInfo, dsi), DesignTableManager.Me.m_blockLabelBorder_Function);
        }
    }
    
    void Update()
    {
        bool visible = DesignTableManager.GetItemVisibility(m_drawers, m_drawerIndex, m_slot) && m_currentLabelMode == DesignTableManager.BlockLabelMode.Info;
        float targetScale = (visible ? 1f : .01f) * m_baseScale;
        if (m_currentLabelMode != m_nextLabelMode)
        {
            targetScale = .01f;
            if (m_currentScale < .011f)
            {
                UpdateItems();
            }
        }
        m_currentScale = Mathf.Lerp(m_currentScale, targetScale, .5f);
        transform.localScale = Vector3.one * m_currentScale;
    }
}
