using System;
using System.Collections.Generic;
using TMPro;
using UnityEngine;
using UnityEngine.Serialization;
using UnityEngine.UI;

public class DesignUIManager : MonoSingleton<DesignUIManager>
{
    public bool m_drawerScrollersIn3D = true;
    public ScrollRect m_drawerTypeScroller;
    public ScrollRect m_drawerSetScroller;
    public ScrollRect m_drawerScroller;
    
    public Button m_showInfoButton;
    public Button m_showHelpersButton;
    public Button m_showResourceButton;

    public DTDragDrawer m_priceDrawer;
    public Transform m_priceDrawerContent;
    public DTDragDrawer m_marketPanel;
    public TMP_Text m_marketPanelTitle;
    public Transform m_marketPanelContent;
    public DTDragDrawer m_infoDrawer;
    public Transform m_infoDrawerContent;
    public TMP_Text m_detailsPanelText;
    
    public DTDragDrawer m_detailsPanel;
    public Transform m_detailsPanelContent;
    
    public RectTransform m_designGauge;
    public RectTransform m_undoButton2D;
    public RectTransform m_redoButton2D;
    
    public Image m_leftArrow, m_rightArrow;
    
    public CanvasGroup m_deleteIndicator;
    public Transform m_deleteIndicatorLeft;
    public Transform m_deleteIndicatorRight;

    public GameObject m_undo3D;
    public GameObject m_redo3D;

    public TMPro.TextMeshProUGUI m_problemDisplayText;
    
    public void ShowDropdowns(bool _visible) { }

    public void UpdateArrows(bool _showLeft, bool _showRight)
    {
        m_leftArrow.color = new Color(1,1,1, Mathf.Lerp(m_leftArrow.color.a, _showLeft ? .5f : .05f, .5f));
        m_rightArrow.color = new Color(1, 1, 1, Mathf.Lerp(m_rightArrow.color.a, _showRight ? .5f : .05f, .5f));
        if (m_drawerScrollersIn3D)
        {
            var rt = m_rightArrow.transform as RectTransform; 
            var a3D = rt.anchoredPosition3D;
            a3D.x = Mathf.Lerp(a3D.x, Screen.width - 80, .25f);
            rt.anchoredPosition3D = a3D;
        }
    }

    float m_priceDrawerInitial = -1e23f;
    float m_infoDrawerInitial = -1e23f;
    float m_marketDrawerInitial = -1e23f;
    float m_detailsDrawerInitial = -1e23f;
    float m_undoInitial = -1e23f;
    float m_redoInitial = -1e23f;
    float m_gaugeInitial = -1e23f;
    float m_leftArrowInitial = -1e23f, m_rightArrowInitial = -1e23f;
    void BlendRT(RectTransform _rt, ref float _initial, float _target, Vector3 _axis, bool _toInitial)
    {
        if (_rt == null) return;
        var pos = _rt.anchoredPosition3D;
        var current = Vector3.Dot(pos, _axis);
        if (_initial < -1e22f)
            _initial = current;
        if (_toInitial)
            _target = _initial;
        pos += _axis * ((_target - current) * .2f);
        if ((pos - _rt.anchoredPosition3D).sqrMagnitude > .001f * .001f)
            _rt.anchoredPosition3D = pos;
    }

    void Update()
    {
        var isInDesign = GameManager.IsDesignTableActively;

        BlendRT(m_priceDrawer.transform as RectTransform, ref m_priceDrawerInitial, -120, Vector3.right, isInDesign);
        BlendRT(m_infoDrawer.transform as RectTransform, ref m_infoDrawerInitial, 120, Vector3.right, isInDesign);
        BlendRT(m_marketPanel.transform as RectTransform, ref m_marketDrawerInitial, 120, Vector3.right, isInDesign);
        BlendRT(m_detailsPanel.transform as RectTransform, ref m_detailsDrawerInitial, 120, Vector3.right, isInDesign);
        BlendRT(m_designGauge, ref m_gaugeInitial, 120, Vector3.up, isInDesign || GameManager.Me.IsProductTestingScene);
        BlendRT(m_undoButton2D, ref m_undoInitial, -220, Vector3.right, isInDesign);
        BlendRT(m_redoButton2D, ref m_redoInitial, 120, Vector3.right, isInDesign);
        BlendRT(m_leftArrow.transform as RectTransform, ref m_leftArrowInitial, -60, Vector3.up, isInDesign);
        BlendRT(m_rightArrow.transform as RectTransform, ref m_rightArrowInitial, -60, Vector3.up, isInDesign);
    }
}
