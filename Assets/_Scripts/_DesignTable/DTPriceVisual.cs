using UnityEngine;

public class DTPriceVisual : MonoBehaviour
{
    public Color m_positivePriceColour = Color.red;
    public Color m_negativePriceColour = Color.green;
    public TMPro.TextMeshProUGUI m_priceText;
    public int m_priceDecimalScale = 75;
    private Transform m_owner;
    private Vector3 m_initialEndPos;
    private float m_finishProgress = -1;
    private float m_price = 0;
    private bool m_priceIsIfDeleted;
    private bool m_toBeDeleted;

    public void Set(float _price, bool _priceIsIfDeleted)
    {
        if (_priceIsIfDeleted)
            m_price = _price;
        else
            m_price = _price;
        m_priceIsIfDeleted = _priceIsIfDeleted;
        m_toBeDeleted = false;
        Refresh();
    }
    
    private void Refresh()
    {
        float _price = m_toBeDeleted == m_priceIsIfDeleted ? m_price : 0;
        m_priceText.transform.parent.gameObject.SetActive(_price.Nearly(0) == false);
        m_priceText.text = Mathf.Abs(_price).ToCurrencyString(m_priceDecimalScale);
        m_priceText.color = _price > 0 ? m_positivePriceColour : m_negativePriceColour;
    }

    public void SetOwner(Transform _owner)
    {
        m_owner = _owner;
        LateUpdate();
    }

    public void SetToBeDeleted(bool _toBeDeleted)
    {
        m_toBeDeleted = _toBeDeleted;
        Refresh();
    }

    void LateUpdate()
    {
        if (m_finishProgress >= 0)
        {
            var destPos = NGPlayer.Me.m_cash.UI.m_currencyText.transform.position;
            const float c_lerpTime = 2;
            m_finishProgress += Time.deltaTime / c_lerpTime;
            var lerp = Mathf.Clamp01(m_finishProgress);
            lerp = lerp * lerp * (3 - lerp - lerp);
            transform.position = Vector3.Lerp(m_initialEndPos, destPos, lerp);
            if (m_finishProgress > 1)
                Destroy(gameObject);
        }
        else
            transform.position = Camera.main.WorldToScreenPoint(m_owner.position);
    }
    
    public void Finish()
    {
        m_finishProgress = 0;
        m_initialEndPos = transform.position;
    }

    public static void Finish(DTPriceVisual _visual)
    {
        if (_visual == null) return;
        _visual.Finish();
    }

    public static DTPriceVisual Create(float _price, bool _priceIsIfDeleted, Transform _owner)
    {
        var pt = Instantiate(DesignTableManager.Me.m_designTablePriceVisualPrefab, UIManager.Me.transform);
        pt.SetOwner(_owner);
        pt.Set(_price, _priceIsIfDeleted);
        return pt;
    }
}
