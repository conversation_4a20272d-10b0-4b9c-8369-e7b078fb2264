using UnityEngine;

public class BuildHelper : MonoBehaviour
{
    private int m_buildingID, m_padID, m_height, m_side;
    public int BuildingID => m_buildingID;
    public int PadID => m_padID;
    public int Height => m_height;
    public int Side => m_side;
    public MABuilding Building => GameManager.Me.GetMACommander<MABuilding>(m_buildingID);
    public Transform Pad {
        get {
            var building = Building;
            if (building == null) return null;
            var block = building.GetComponentInChildren<BaseBlock>();
            if (block == null) return null;
            var hinges = block.GetHinges();
            if (m_padID < 0 || m_padID >= hinges.Count) return null;
            return hinges[m_padID];
        }
    }

    public void Set(int _buildingID, int _padID, int _height, int _direction)
    {
        m_buildingID = _buildingID;
        m_padID = _padID;
        m_height = _height;
        m_side = _direction;
    }
}
